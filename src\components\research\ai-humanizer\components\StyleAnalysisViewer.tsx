/**
 * Style Analysis Viewer Component
 * Displays detailed analysis of the user's writing style
 */

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Brain,
  MessageSquare,
  BookOpen,
  Target,
  Lightbulb,
  TrendingUp
} from "lucide-react";

import { StyleAnalysis } from '../types';

interface StyleAnalysisViewerProps {
  analysis: StyleAnalysis;
}

export function StyleAnalysisViewer({ analysis }: StyleAnalysisViewerProps) {
  const { characteristics, confidence, insights, recommendations } = analysis;

  /**
   * Get confidence color based on value
   */
  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600';
    if (confidence >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  /**
   * Get confidence label
   */
  const getConfidenceLabel = (confidence: number) => {
    if (confidence >= 0.9) return 'Excellent';
    if (confidence >= 0.8) return 'Very Good';
    if (confidence >= 0.7) return 'Good';
    if (confidence >= 0.6) return 'Fair';
    return 'Needs More Samples';
  };

  return (
    <div className="space-y-6">
      {/* Confidence Score */}
      <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
        <div>
          <h3 className="font-semibold text-blue-800">Analysis Confidence</h3>
          <p className="text-sm text-blue-600">
            How well we understand your writing style
          </p>
        </div>
        <div className="text-right">
          <div className={`text-2xl font-bold ${getConfidenceColor(confidence)}`}>
            {Math.round(confidence * 100)}%
          </div>
          <div className="text-sm text-gray-600">
            {getConfidenceLabel(confidence)}
          </div>
        </div>
      </div>

      {/* Style Characteristics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Tone & Voice */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <MessageSquare className="h-4 w-4 text-blue-600" />
              Tone & Voice
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <span className="text-sm font-medium text-gray-600">Tone:</span>
              <div className="flex flex-wrap gap-1 mt-1">
                {characteristics.tone.map((tone, index) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {tone}
                  </Badge>
                ))}
              </div>
            </div>
            <div>
              <span className="text-sm font-medium text-gray-600">Formality:</span>
              <Badge variant="outline" className="ml-2 text-xs">
                {characteristics.voice.formality}
              </Badge>
            </div>
            <div>
              <span className="text-sm font-medium text-gray-600">Perspective:</span>
              <Badge variant="outline" className="ml-2 text-xs">
                {characteristics.voice.perspective}
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Vocabulary */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <BookOpen className="h-4 w-4 text-green-600" />
              Vocabulary
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <span className="text-sm font-medium text-gray-600">Complexity:</span>
              <Badge variant="outline" className="ml-2 text-xs">
                {characteristics.vocabulary.complexity}
              </Badge>
            </div>
            {characteristics.vocabulary.commonWords.length > 0 && (
              <div>
                <span className="text-sm font-medium text-gray-600">Common Words:</span>
                <div className="flex flex-wrap gap-1 mt-1">
                  {characteristics.vocabulary.commonWords.slice(0, 5).map((word, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {word}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Sentence Structure */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <Target className="h-4 w-4 text-purple-600" />
              Sentence Structure
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <span className="text-sm font-medium text-gray-600">Average Length:</span>
              <span className="ml-2 text-sm">{characteristics.sentenceStructure.averageLength} words</span>
            </div>
            <div>
              <span className="text-sm font-medium text-gray-600">Complexity:</span>
              <Badge variant="outline" className="ml-2 text-xs">
                {characteristics.sentenceStructure.complexity}
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Writing Patterns */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-orange-600" />
              Writing Patterns
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <span className="text-sm font-medium text-gray-600">Paragraph Length:</span>
              <Badge variant="outline" className="ml-2 text-xs">
                {characteristics.writingPatterns.paragraphLength}
              </Badge>
            </div>
            {characteristics.writingPatterns.transitionStyle.length > 0 && (
              <div>
                <span className="text-sm font-medium text-gray-600">Transitions:</span>
                <div className="flex flex-wrap gap-1 mt-1">
                  {characteristics.writingPatterns.transitionStyle.slice(0, 3).map((style, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {style}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Insights */}
      {insights.length > 0 && (
        <Card className="bg-yellow-50 border-yellow-200">
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <Lightbulb className="h-4 w-4 text-yellow-600" />
              Key Insights
            </CardTitle>
            <CardDescription>
              What we learned about your writing style
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {insights.map((insight, index) => (
                <li key={index} className="flex items-start gap-2 text-sm">
                  <span className="text-yellow-600 mt-1">•</span>
                  <span className="text-yellow-800">{insight}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}

      {/* Recommendations */}
      {recommendations.length > 0 && (
        <Card className="bg-green-50 border-green-200">
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <Brain className="h-4 w-4 text-green-600" />
              Recommendations
            </CardTitle>
            <CardDescription>
              Tips for better humanization results
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {recommendations.map((recommendation, index) => (
                <li key={index} className="flex items-start gap-2 text-sm">
                  <span className="text-green-600 mt-1">•</span>
                  <span className="text-green-800">{recommendation}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
