/**
 * AI Humanizer Types
 * Type definitions for the AI writing style humanizer module
 */

import { LucideIcon } from "lucide-react";

// Core humanizer types
export interface HumanizerSession {
  id: string;
  title: string;
  sampleTexts: SampleText[];
  originalContent: string;
  humanizedContent: string;
  styleProfile: StyleProfile | null;
  createdAt: Date;
  updatedAt: Date;
  userId?: string;
  isActive: boolean;
  metadata?: {
    wordCount: number;
    processingTime: number; // in seconds
    modelUsed: string;
    confidence: number;
  };
}

export interface SampleText {
  id: string;
  content: string;
  label: string;
  wordCount: number;
  addedAt: Date;
  isAnalyzed: boolean;
}

export interface StyleProfile {
  id: string;
  name: string;
  characteristics: StyleCharacteristics;
  sampleCount: number;
  createdAt: Date;
  confidence: number; // 0-1 scale
}

export interface StyleCharacteristics {
  tone: string[];
  vocabulary: {
    complexity: 'simple' | 'moderate' | 'complex' | 'academic';
    commonWords: string[];
    preferredPhrases: string[];
  };
  sentenceStructure: {
    averageLength: number;
    complexity: 'simple' | 'compound' | 'complex' | 'varied';
    patterns: string[];
  };
  voice: {
    perspective: 'first' | 'second' | 'third' | 'mixed';
    formality: 'casual' | 'semi-formal' | 'formal' | 'academic';
    personality: string[];
  };
  writingPatterns: {
    paragraphLength: 'short' | 'medium' | 'long' | 'varied';
    transitionStyle: string[];
    emphasisMethods: string[];
  };
}

// Processing states
export type ProcessingState = 
  | 'idle'
  | 'analyzing-samples'
  | 'generating-profile'
  | 'humanizing-content'
  | 'complete'
  | 'error';

export interface ProcessingStatus {
  state: ProcessingState;
  progress: number; // 0-100
  currentStep: string;
  estimatedTimeRemaining?: number; // in seconds
  error?: string;
}

// AI model configuration
export interface HumanizerAIModel {
  id: string;
  name: string;
  provider: string;
  description: string;
  maxTokens: number;
  supportsStreaming: boolean;
  cost: 'low' | 'medium' | 'high';
  strengths: string[];
  bestFor: string[];
}

// Humanizer settings
export interface HumanizerSettings {
  preferredModel: string;
  preserveFormatting: boolean;
  maintainTechnicalTerms: boolean;
  creativityLevel: 'conservative' | 'moderate' | 'creative';
  outputStyle: 'natural' | 'enhanced' | 'creative';
  autoSave: boolean;
  showAnalysisDetails: boolean;
}

// Analysis results
export interface StyleAnalysis {
  id: string;
  sampleTextIds: string[];
  characteristics: StyleCharacteristics;
  confidence: number;
  analysisDate: Date;
  modelUsed: string;
  insights: string[];
  recommendations: string[];
}

// Export formats
export type ExportFormat = 'txt' | 'docx' | 'pdf' | 'html';

export interface ExportOptions {
  format: ExportFormat;
  includeOriginal: boolean;
  includeAnalysis: boolean;
  includeMetadata: boolean;
}

// UI state types
export interface UIState {
  activeTab: 'samples' | 'content' | 'results' | 'settings';
  sidebarCollapsed: boolean;
  showAnalysisPanel: boolean;
  showPreview: boolean;
}

// Error types
export interface HumanizerError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
}

// API response types
export interface HumanizerResponse {
  success: boolean;
  data?: any;
  error?: HumanizerError;
  metadata?: {
    processingTime: number;
    tokensUsed: number;
    modelUsed: string;
  };
}

export interface StreamingHumanizerResponse {
  chunk: string;
  isComplete: boolean;
  metadata?: {
    progress: number;
    currentStep: string;
  };
}

// Constants for validation
export const VALIDATION_LIMITS = {
  MAX_SAMPLE_TEXTS: 10,
  MIN_SAMPLE_TEXT_LENGTH: 50,
  MAX_SAMPLE_TEXT_LENGTH: 5000,
  MAX_CONTENT_LENGTH: 4000,
  MIN_CONTENT_LENGTH: 10,
  MAX_SAMPLE_LABEL_LENGTH: 100
} as const;

// Style analysis categories
export const STYLE_CATEGORIES = {
  TONE: ['professional', 'casual', 'friendly', 'authoritative', 'conversational', 'academic', 'creative', 'humorous'],
  FORMALITY: ['very-formal', 'formal', 'semi-formal', 'informal', 'very-informal'],
  COMPLEXITY: ['simple', 'moderate', 'complex', 'highly-complex'],
  VOICE: ['active', 'passive', 'mixed'],
  PERSPECTIVE: ['first-person', 'second-person', 'third-person', 'mixed']
} as const;
