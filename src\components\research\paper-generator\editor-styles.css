/* Enhanced Editor Styles */

.ProseMirror {
  outline: none;
  min-height: 600px;
  padding: 1.5rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
  font-family: 'Georgia', 'Times New Roman', serif;
  font-size: 16px;
  line-height: 1.6;
  color: #374151;
  max-width: none;
  margin: 0 auto;
  transition: all 0.2s ease-in-out;
}

/* Dynamic font sizing support */
.ProseMirror.text-sm {
  font-size: 12px;
  line-height: 1.5;
}

.ProseMirror.text-base {
  font-size: 14px;
  line-height: 1.6;
}

.ProseMirror.text-lg {
  font-size: 16px;
  line-height: 1.6;
}

.ProseMirror.text-xl {
  font-size: 18px;
  line-height: 1.7;
}

.ProseMirror.text-2xl {
  font-size: 20px;
  line-height: 1.7;
}

/* Font family support */
.ProseMirror [style*="font-family: Times New Roman"] {
  font-family: "Times New Roman", Times, serif !important;
}

.ProseMirror [style*="font-family: Georgia"] {
  font-family: Georgia, serif !important;
}

.ProseMirror [style*="font-family: Arial"] {
  font-family: Arial, sans-serif !important;
}

.ProseMirror [style*="font-family: Helvetica"] {
  font-family: Helvetica, Arial, sans-serif !important;
}

.ProseMirror [style*="font-family: Calibri"] {
  font-family: Calibri, sans-serif !important;
}

.ProseMirror [style*="font-family: serif"] {
  font-family: serif !important;
}

.ProseMirror [style*="font-family: sans-serif"] {
  font-family: sans-serif !important;
}

.ProseMirror [style*="font-family: monospace"] {
  font-family: monospace !important;
}

.ProseMirror:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Typography improvements */
.ProseMirror h1 {
  font-size: 2.25rem;
  font-weight: 700;
  line-height: 1.2;
  margin-top: 2rem;
  margin-bottom: 1rem;
  color: #1f2937;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 0.5rem;
}

.ProseMirror h2 {
  font-size: 1.875rem;
  font-weight: 600;
  line-height: 1.3;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  color: #1f2937;
}

.ProseMirror h3 {
  font-size: 1.5rem;
  font-weight: 600;
  line-height: 1.4;
  margin-top: 1.25rem;
  margin-bottom: 0.5rem;
  color: #374151;
}

.ProseMirror p {
  margin-bottom: 1rem;
  text-align: justify;
}

.ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #9ca3af;
  pointer-events: none;
  height: 0;
}

/* Enhanced lists */
.ProseMirror ul, .ProseMirror ol {
  margin: 1rem 0;
  padding-left: 2rem;
}

.ProseMirror ul {
  list-style-type: disc;
}

.ProseMirror ol {
  list-style-type: decimal;
}

.ProseMirror li {
  margin-bottom: 0.5rem;
  line-height: 1.6;
  display: list-item;
}

.ProseMirror .rich-editor-bullet-list {
  list-style-type: disc;
  padding-left: 2rem;
}

.ProseMirror .rich-editor-ordered-list {
  list-style-type: decimal;
  padding-left: 2rem;
}

.ProseMirror .rich-editor-list-item {
  display: list-item;
}

/* Enhanced blockquotes */
.ProseMirror blockquote {
  border-left: 4px solid #3b82f6;
  margin: 1.5rem 0;
  padding: 0.5rem 0 0.5rem 1.5rem;
  background-color: #f8fafc;
  border-radius: 0 8px 8px 0;
  font-style: italic;
  color: #4b5563;
}

/* Enhanced code blocks */
.ProseMirror pre {
  background-color: #1f2937;
  color: #e5e7eb;
  padding: 1rem;
  border-radius: 8px;
  margin: 1rem 0;
  overflow-x: auto;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
}

.ProseMirror code {
  background-color: #f3f4f6;
  color: #dc2626;
  padding: 0.125rem 0.25rem;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875em;
}

/* Enhanced tables */
.ProseMirror table {
  border-collapse: collapse;
  table-layout: fixed;
  width: 100%;
  margin: 1.5rem 0;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.ProseMirror td, .ProseMirror th {
  border: 1px solid #e5e7eb;
  padding: 12px;
  vertical-align: top;
  box-sizing: border-box;
  position: relative;
  min-width: 100px;
}

.ProseMirror th {
  background-color: #f9fafb;
  font-weight: 600;
  color: #374151;
}

.ProseMirror .selectedCell:after {
  z-index: 2;
  position: absolute;
  content: "";
  left: 0; right: 0; top: 0; bottom: 0;
  background: rgba(59, 130, 246, 0.1);
  pointer-events: none;
}

.ProseMirror .column-resize-handle {
  position: absolute;
  right: -2px;
  top: 0;
  bottom: -2px;
  width: 4px;
  background-color: #3b82f6;
  pointer-events: none;
}

.ProseMirror.resize-cursor {
  cursor: ew-resize;
  cursor: col-resize;
}

/* Enhanced images */
.ProseMirror img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 1rem 0;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s ease-in-out;
}

.ProseMirror img:hover {
  transform: scale(1.02);
}

.ProseMirror .ProseMirror-selectednode img {
  outline: 3px solid #3b82f6;
  outline-offset: 2px;
}

/* Image alignment */
.ProseMirror .has-alignment-left {
  float: left;
  margin-right: 1.5rem;
  margin-bottom: 1rem;
}

.ProseMirror .has-alignment-right {
  float: right;
  margin-left: 1.5rem;
  margin-bottom: 1rem;
}

.ProseMirror .has-alignment-center {
  display: block;
  margin-left: auto;
  margin-right: auto;
}

/* Image resize handles */
.ProseMirror .resize-handle {
  width: 10px;
  height: 10px;
  background-color: #3b82f6;
  border-radius: 50%;
}

/* Image toolbar */
.image-toolbar {
  position: absolute;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  padding: 4px;
  display: flex;
  gap: 4px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.image-toolbar button {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  background: none;
  border-radius: 4px;
  cursor: pointer;
  color: #4b5563;
}

.image-toolbar button:hover {
  background-color: #f3f4f6;
}

.image-toolbar button.active {
  background-color: #e5e7eb;
  color: #1f2937;
}

/* Enhanced highlighting */
.ProseMirror mark {
  background-color: #fef3c7;
  color: #92400e;
  padding: 0.125rem 0.25rem;
  border-radius: 4px;
}

/* Links */
.ProseMirror a {
  color: #3b82f6;
  text-decoration: underline;
  cursor: pointer;
}

.ProseMirror a:hover {
  color: #1d4ed8;
}

/* Selection styles */
.ProseMirror-selectednode {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
  border-radius: 4px;
}

/* Drag handle for improved UX */
.ProseMirror-gapcursor {
  display: none;
  pointer-events: none;
  position: absolute;
}

.ProseMirror-gapcursor:after {
  content: "";
  display: block;
  position: absolute;
  top: -2px;
  width: 20px;
  border-top: 1px solid #3b82f6;
  animation: ProseMirror-cursor-blink 1.1s steps(2, start) infinite;
}

@keyframes ProseMirror-cursor-blink {
  to {
    visibility: hidden;
  }
}

/* Responsive improvements */
@media (max-width: 1024px) {
  .ProseMirror {
    padding: 1.25rem;
  }
}

@media (max-width: 768px) {
  .ProseMirror {
    padding: 1rem;
    font-size: 14px;
    min-height: 500px;
  }

  .ProseMirror h1 {
    font-size: 1.75rem;
  }

  .ProseMirror h2 {
    font-size: 1.5rem;
  }

  .ProseMirror h3 {
    font-size: 1.25rem;
  }

  /* Adjust font sizes for mobile */
  .ProseMirror.text-sm {
    font-size: 11px;
  }

  .ProseMirror.text-base {
    font-size: 13px;
  }

  .ProseMirror.text-lg {
    font-size: 15px;
  }

  .ProseMirror.text-xl {
    font-size: 17px;
  }

  .ProseMirror.text-2xl {
    font-size: 19px;
  }
}

@media (max-width: 480px) {
  .ProseMirror {
    padding: 0.75rem;
    min-height: 400px;
  }
}

/* Focus and hover improvements */
.ProseMirror .empty-node::before {
  position: absolute;
  color: #9ca3af;
  cursor: text;
}

.ProseMirror .empty-node:hover::before {
  color: #6b7280;
}

/* Better spacing and typography for academic writing */
.ProseMirror {
  line-height: 2;
  letter-spacing: 0.025em;
}

.ProseMirror p + p {
  margin-top: 1.25rem;
}

.ProseMirror h1 + p,
.ProseMirror h2 + p,
.ProseMirror h3 + p {
  margin-top: 0.75rem;
}

/* Add a visible drag handle overlay for images */
.ProseMirror .resizable-image-wrapper {
  position: relative;
  display: inline-block;
}
.ProseMirror .image-drag-handle {
  position: absolute;
  top: 50%;
  left: -18px;
  transform: translateY(-50%);
  width: 16px;
  height: 32px;
  background: #3b82f6;
  border-radius: 6px;
  cursor: grab;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  box-shadow: 0 2px 6px rgba(59,130,246,0.15);
  opacity: 0.85;
}
.ProseMirror .image-drag-handle svg {
  color: #fff;
  width: 12px;
  height: 24px;
}
.ProseMirror .resizable-image-wrapper.selected .image-drag-handle {
  display: flex;
}

/* Global font classes for better font support */
.font-times-new-roman {
  font-family: "Times New Roman", Times, serif !important;
}

.font-georgia {
  font-family: Georgia, serif !important;
}

.font-arial {
  font-family: Arial, sans-serif !important;
}

.font-helvetica {
  font-family: Helvetica, Arial, sans-serif !important;
}

.font-calibri {
  font-family: Calibri, sans-serif !important;
}

/* Ensure Times New Roman is properly loaded and displayed */
/* Note: Google Fonts import moved to index.html for better performance */

/* Fallback for Times New Roman if not available */
.times-new-roman-fallback {
  font-family: "Times New Roman", "Liberation Serif", "Nimbus Roman No9 L", Times, serif !important;
}

/* Enhanced Generation Panel Animations */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.6);
  }
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes typing-dots {
  0%, 20% {
    color: rgba(59, 130, 246, 0.4);
    text-shadow: 0.25em 0 0 rgba(59, 130, 246, 0.4),
                 0.5em 0 0 rgba(59, 130, 246, 0.4);
  }
  40% {
    color: rgba(59, 130, 246, 1);
    text-shadow: 0.25em 0 0 rgba(59, 130, 246, 0.4),
                 0.5em 0 0 rgba(59, 130, 246, 0.4);
  }
  60% {
    text-shadow: 0.25em 0 0 rgba(59, 130, 246, 1),
                 0.5em 0 0 rgba(59, 130, 246, 0.4);
  }
  80%, 100% {
    text-shadow: 0.25em 0 0 rgba(59, 130, 246, 1),
                 0.5em 0 0 rgba(59, 130, 246, 1);
  }
}

.animate-fade-in {
  animation: fade-in 0.5s ease-out;
}

.animate-fade-in-up {
  animation: fade-in-up 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slide-in-right 0.4s ease-out;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-gradient-shift {
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

.animate-typing-dots::after {
  content: '...';
  animation: typing-dots 1.4s ease-in-out infinite;
}

/* Enhanced hover effects for generation cards */
.generation-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.generation-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.generation-card.active {
  transform: scale(1.02);
  box-shadow: 0 15px 35px rgba(59, 130, 246, 0.15);
}

/* Smooth progress bar animation */
.progress-bar {
  transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced completion celebration */
.completion-celebration {
  animation: fade-in 0.5s ease-out, pulse-glow 1s ease-in-out 3;
}

/* Floating animation for icons */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Shimmer effect for loading states */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Staggered animation delays for section cards */
.animate-stagger-1 { animation-delay: 0.1s; }
.animate-stagger-2 { animation-delay: 0.2s; }
.animate-stagger-3 { animation-delay: 0.3s; }
.animate-stagger-4 { animation-delay: 0.4s; }
.animate-stagger-5 { animation-delay: 0.5s; }
.animate-stagger-6 { animation-delay: 0.6s; }

/* Additional floating animation for particles */
@keyframes float-particles {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.3;
  }
  25% {
    transform: translateY(-20px) translateX(10px);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-10px) translateX(-5px);
    opacity: 0.5;
  }
  75% {
    transform: translateY(-30px) translateX(15px);
    opacity: 0.8;
  }
}

/* Enhanced typing cursor animation */
@keyframes typing-cursor {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

.typing-cursor {
  animation: typing-cursor 1s infinite;
}

/* Smooth scale animation for cards */
@keyframes scale-in {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.animate-scale-in {
  animation: scale-in 0.3s ease-out;
}

/* Progress bar glow effect */
@keyframes progress-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(59, 130, 246, 0.4);
  }
}

.progress-glow {
  animation: progress-glow 2s ease-in-out infinite;
}

/* Celebration confetti animation */
@keyframes confetti-fall {
  0% {
    transform: translateY(-100vh) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(100vh) rotate(720deg);
    opacity: 0;
  }
}

.confetti {
  animation: confetti-fall 3s linear infinite;
}

/* Enhanced bounce animation */
@keyframes enhanced-bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -15px, 0);
  }
  70% {
    transform: translate3d(0, -7px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

.animate-enhanced-bounce {
  animation: enhanced-bounce 1s ease-in-out;
}

/* Smooth color transition for status changes */
.status-transition {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Glowing text effect */
@keyframes text-glow {
  0%, 100% {
    text-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
  }
  50% {
    text-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(59, 130, 246, 0.6);
  }
}

.text-glow {
  animation: text-glow 2s ease-in-out infinite;
}
