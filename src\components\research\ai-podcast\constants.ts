import { AudioProvider, VoiceOption } from './types';

// Default audio providers configuration
export const DEFAULT_AUDIO_PROVIDERS: AudioProvider[] = [
  {
    id: 'fish-audio',
    name: 'Fish Audio',
    enabled: true,
    voices: [],
    maxDuration: 300, // 5 minutes
    costPerMinute: 0.1
  },
  {
    id: 'gemini',
    name: 'Google Gemini',
    enabled: true,
    voices: [],
    maxDuration: 600, // 10 minutes
    costPerMinute: 0.2
  }
];

// Default voice options for Fish Audio
export const FISH_AUDIO_VOICES: VoiceOption[] = [
  {
    id: 'fish-energetic-male',
    name: 'Energetic Male',
    gender: 'male',
    language: 'en',
    provider: DEFAULT_AUDIO_PROVIDERS[0],
    description: 'Enthusiastic and engaging male voice'
  },
  {
    id: 'fish-friendly-female',
    name: 'Friendly Female',
    gender: 'female',
    language: 'en',
    provider: DEFAULT_AUDIO_PROVIDERS[0],
    description: 'Warm and approachable female voice'
  },
  {
    id: 'fish-professional-male',
    name: 'Professional Male',
    gender: 'male',
    language: 'en',
    provider: DEFAULT_AUDIO_PROVIDERS[0],
    description: 'Clear and authoritative male voice'
  },
  {
    id: 'fish-conversational-female',
    name: 'Conversational Female',
    gender: 'female',
    language: 'en',
    provider: DEFAULT_AUDIO_PROVIDERS[0],
    description: 'Natural and conversational female voice'
  }
];

// Default voice options for Gemini
export const GEMINI_VOICES: VoiceOption[] = [
  {
    id: 'gemini-narrator-male',
    name: 'Narrator Male',
    gender: 'male',
    language: 'en',
    provider: DEFAULT_AUDIO_PROVIDERS[1],
    description: 'Deep and engaging male narrator voice'
  },
  {
    id: 'gemini-host-female',
    name: 'Host Female',
    gender: 'female',
    language: 'en',
    provider: DEFAULT_AUDIO_PROVIDERS[1],
    description: 'Professional female host voice'
  }
];

// Supported languages
export const SUPPORTED_LANGUAGES = [
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'es', name: 'Spanish', flag: '🇪🇸' },
  { code: 'fr', name: 'French', flag: '🇫🇷' },
  { code: 'de', name: 'German', flag: '🇩🇪' },
  { code: 'it', name: 'Italian', flag: '🇮🇹' },
  { code: 'pt', name: 'Portuguese', flag: '🇵🇹' },
  { code: 'zh', name: 'Chinese', flag: '🇨🇳' },
  { code: 'ja', name: 'Japanese', flag: '🇯🇵' },
  { code: 'ko', name: 'Korean', flag: '🇰🇷' }
];

// File upload configuration
export const UPLOAD_CONFIG = {
  maxFileSize: 50 * 1024 * 1024, // 50MB
  acceptedFormats: ['.pdf', '.doc', '.docx', '.txt', '.md'],
  acceptedMimeTypes: [
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/msword',
    'text/plain',
    'text/markdown'
  ]
};

// Podcast generation stages
export const GENERATION_STAGES = [
  {
    id: 'outline',
    name: 'Generating Outline',
    description: 'Creating podcast structure and key points',
    estimatedDuration: 30 // seconds
  },
  {
    id: 'script',
    name: 'Writing Script',
    description: 'Generating conversational dialogue between hosts',
    estimatedDuration: 60 // seconds
  },
  {
    id: 'audio',
    name: 'Creating Audio',
    description: 'Converting script to speech with selected voices',
    estimatedDuration: 120 // seconds
  }
];

// Default podcast settings
export const DEFAULT_PODCAST_SETTINGS = {
  duration: 300, // 5 minutes
  language: 'en',
  outputFormat: 'mp3' as const,
  quality: 'standard' as const,
  speed: 1.0,
  includeIntro: true,
  includeOutro: true
};

// Input method tabs configuration
export const INPUT_TABS = [
  {
    id: 'topic',
    name: 'Topic',
    icon: '💡',
    description: 'Enter a topic or subject for the podcast',
    placeholder: 'e.g., "The future of artificial intelligence in healthcare"'
  },
  {
    id: 'link',
    name: 'Link',
    icon: '🔗',
    description: 'Provide a URL to an article or webpage',
    placeholder: 'e.g., https://example.com/article'
  },
  {
    id: 'file',
    name: 'Upload File',
    icon: '📄',
    description: 'Upload a document (PDF, Word, or text file)',
    placeholder: 'Drag and drop files here or click to browse'
  },
  {
    id: 'text',
    name: 'Long Text',
    icon: '📝',
    description: 'Paste or type long-form content',
    placeholder: 'Paste your content here...'
  }
];

// Audio quality options
export const AUDIO_QUALITY_OPTIONS = [
  {
    value: 'standard',
    label: 'Standard Quality',
    description: '128 kbps MP3',
    fileSize: 'Smaller file size'
  },
  {
    value: 'high',
    label: 'High Quality',
    description: '320 kbps MP3',
    fileSize: 'Larger file size'
  }
];

// Playback speed options
export const PLAYBACK_SPEED_OPTIONS = [
  { value: 0.5, label: '0.5x' },
  { value: 0.75, label: '0.75x' },
  { value: 1.0, label: '1x' },
  { value: 1.25, label: '1.25x' },
  { value: 1.5, label: '1.5x' },
  { value: 2.0, label: '2x' }
];

// Error messages
export const ERROR_MESSAGES = {
  FILE_TOO_LARGE: 'File size exceeds the maximum limit of 50MB',
  INVALID_FILE_TYPE: 'File type not supported. Please upload PDF, Word, or text files.',
  UPLOAD_FAILED: 'Failed to upload file. Please try again.',
  GENERATION_FAILED: 'Failed to generate podcast. Please try again.',
  INVALID_URL: 'Please enter a valid URL',
  EMPTY_CONTENT: 'Please provide content for the podcast',
  VOICE_SELECTION_REQUIRED: 'Please select voices for both hosts',
  PROVIDER_NOT_AVAILABLE: 'Selected audio provider is not available'
};

// Success messages
export const SUCCESS_MESSAGES = {
  FILE_UPLOADED: 'File uploaded successfully',
  PODCAST_GENERATED: 'Podcast generated successfully',
  PODCAST_SAVED: 'Podcast saved to history',
  PODCAST_DOWNLOADED: 'Podcast downloaded successfully'
};

// API endpoints (relative to base URL)
export const API_ENDPOINTS = {
  GENERATE_PODCAST: '/api/podcast/generate',
  GET_VOICES: '/api/podcast/voices',
  GET_HISTORY: '/api/podcast/history',
  UPLOAD_FILE: '/api/podcast/upload',
  DOWNLOAD_PODCAST: '/api/podcast/download'
};

// Gemini API configuration
export const GEMINI_CONFIG = {
  model: 'gemini-2.5-pro',
  maxTokens: 4096,
  temperature: 0.7,
  topP: 0.9
};
