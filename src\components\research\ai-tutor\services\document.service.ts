/**
 * Document Service
 * Main service that orchestrates document upload, processing, and storage
 * for the Research Comprehension Platform
 */

import { ResearchDocument, LearningProgress } from '../types';
import { documentProcessorService } from './document-processor.service';
import { documentStorageService } from './document-storage.service';
import { toast } from 'sonner';

interface UploadProgress {
  stage: 'uploading' | 'processing' | 'storing' | 'complete';
  progress: number; // 0-100
  message: string;
}

interface UploadOptions {
  extractMetadata?: boolean;
  generateSections?: boolean;
  analyzeContent?: boolean;
  chunkSize?: number;
  onProgress?: (progress: UploadProgress) => void;
}

class DocumentService {
  /**
   * Upload and process a document
   */
  async uploadDocument(
    file: File,
    userId: string,
    options: UploadOptions = {}
  ): Promise<ResearchDocument> {
    const defaultOptions: UploadOptions = {
      extractMetadata: true,
      generateSections: true,
      analyzeContent: true,
      chunkSize: 1000,
      ...options
    };

    try {
      // Stage 1: Validate file
      const validation = documentProcessorService.validateFile(file);
      if (!validation.isValid) {
        throw new Error(validation.error);
      }

      defaultOptions.onProgress?.({
        stage: 'uploading',
        progress: 5,
        message: 'Starting document upload...'
      });

      // Stage 2: Process document
      defaultOptions.onProgress?.({
        stage: 'processing',
        progress: 10,
        message: 'Processing document content...'
      });

      const document = await documentProcessorService.processDocument(
        file,
        userId,
        {
          ...defaultOptions,
          onProgress: (processingProgress) => {
            // Map processing progress to overall progress (10-70%)
            const overallProgress = 10 + (processingProgress.progress * 0.6);
            defaultOptions.onProgress?.({
              stage: 'processing',
              progress: overallProgress,
              message: processingProgress.message
            });
          }
        }
      );

      // Stage 3: Upload file to storage
      defaultOptions.onProgress?.({
        stage: 'storing',
        progress: 75,
        message: 'Uploading file to storage...'
      });

      const { data: filePath, error: uploadError } = await documentStorageService.uploadFile(
        file,
        document.id
      );

      if (uploadError) {
        console.warn('File upload failed, continuing without file storage:', uploadError);
      } else {
        document.filePath = filePath;
      }

      // Stage 4: Save document to database
      defaultOptions.onProgress?.({
        stage: 'storing',
        progress: 85,
        message: 'Saving document to database...'
      });

      const { data: savedDocument, error: saveError } = await documentStorageService.saveDocument(document);

      if (saveError) {
        console.warn('Failed to save to database, using in-memory storage:', saveError);

        // Fallback: Store in memory and continue
        defaultOptions.onProgress?.({
          stage: 'storing',
          progress: 95,
          message: 'Using temporary storage...'
        });

        defaultOptions.onProgress?.({
          stage: 'complete',
          progress: 100,
          message: 'Document processed successfully! (Note: Database not available)'
        });

        toast.success(`Document "${document.title}" processed successfully! (Temporary storage)`);
        return document;
      }

      // Stage 5: Initialize learning progress (only if database save succeeded)
      try {
        defaultOptions.onProgress?.({
          stage: 'storing',
          progress: 95,
          message: 'Initializing learning progress...'
        });

        await this.initializeLearningProgress(userId, document.id);
      } catch (progressError) {
        console.warn('Failed to initialize learning progress:', progressError);
        // Continue anyway
      }

      defaultOptions.onProgress?.({
        stage: 'complete',
        progress: 100,
        message: 'Document upload complete!'
      });

      toast.success(`Document "${document.title}" uploaded successfully!`);
      return savedDocument || document;

    } catch (error) {
      console.error('Document upload failed:', error);
      toast.error(`Failed to upload document: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get user's documents
   */
  async getUserDocuments(userId?: string): Promise<ResearchDocument[]> {
    try {
      const { data, error } = await documentStorageService.getUserDocuments(userId);
      
      if (error) {
        console.error('Failed to fetch documents:', error);
        toast.error('Failed to load documents');
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error in getUserDocuments:', error);
      toast.error('Failed to load documents');
      return [];
    }
  }

  /**
   * Get single document
   */
  async getDocument(documentId: string): Promise<ResearchDocument | null> {
    try {
      const { data, error } = await documentStorageService.getDocument(documentId);
      
      if (error) {
        console.error('Failed to fetch document:', error);
        toast.error('Failed to load document');
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in getDocument:', error);
      toast.error('Failed to load document');
      return null;
    }
  }

  /**
   * Delete document
   */
  async deleteDocument(documentId: string): Promise<boolean> {
    try {
      const { data, error } = await documentStorageService.deleteDocument(documentId);
      
      if (error) {
        console.error('Failed to delete document:', error);
        toast.error('Failed to delete document');
        return false;
      }

      toast.success('Document deleted successfully');
      return data || false;
    } catch (error) {
      console.error('Error in deleteDocument:', error);
      toast.error('Failed to delete document');
      return false;
    }
  }

  /**
   * Update document
   */
  async updateDocument(documentId: string, updates: Partial<ResearchDocument>): Promise<ResearchDocument | null> {
    try {
      const { data, error } = await documentStorageService.updateDocument(documentId, updates);
      
      if (error) {
        console.error('Failed to update document:', error);
        toast.error('Failed to update document');
        return null;
      }

      toast.success('Document updated successfully');
      return data;
    } catch (error) {
      console.error('Error in updateDocument:', error);
      toast.error('Failed to update document');
      return null;
    }
  }

  /**
   * Get learning progress for a document
   */
  async getLearningProgress(userId: string, documentId: string): Promise<LearningProgress | null> {
    try {
      const { data, error } = await documentStorageService.getLearningProgress(userId, documentId);
      
      if (error) {
        console.error('Failed to fetch learning progress:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in getLearningProgress:', error);
      return null;
    }
  }

  /**
   * Update learning progress
   */
  async updateLearningProgress(progress: LearningProgress): Promise<boolean> {
    try {
      progress.updatedAt = new Date();
      
      const { data, error } = await documentStorageService.saveLearningProgress(progress);
      
      if (error) {
        console.error('Failed to save learning progress:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in updateLearningProgress:', error);
      return false;
    }
  }

  /**
   * Initialize learning progress for a new document
   */
  private async initializeLearningProgress(userId: string, documentId: string): Promise<void> {
    try {
      const progress: LearningProgress = {
        id: crypto.randomUUID(),
        userId,
        documentId,
        comprehensionLevel: 0,
        conceptsLearned: [],
        conceptsMastered: [],
        quizzesCompleted: [],
        gamesCompleted: [],
        timeSpent: 0,
        sessionsCount: 0,
        lastActivity: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      await documentStorageService.saveLearningProgress(progress);
    } catch (error) {
      console.warn('Failed to initialize learning progress:', error);
      // Don't fail the entire upload for this
    }
  }

  /**
   * Search documents by title or content
   */
  async searchDocuments(query: string, userId?: string): Promise<ResearchDocument[]> {
    try {
      const documents = await this.getUserDocuments(userId);
      
      if (!query.trim()) {
        return documents;
      }

      const searchTerm = query.toLowerCase();
      
      return documents.filter(doc =>
        doc.title?.toLowerCase().includes(searchTerm) ||
        doc.abstract?.toLowerCase().includes(searchTerm) ||
        (doc.authors && Array.isArray(doc.authors) && doc.authors.some(author => author?.toLowerCase().includes(searchTerm))) ||
        doc.content?.toLowerCase().includes(searchTerm)
      );
    } catch (error) {
      console.error('Error in searchDocuments:', error);
      return [];
    }
  }

  /**
   * Get document statistics
   */
  async getDocumentStats(documentId: string): Promise<{
    wordCount: number;
    sectionCount: number;
    readingTime: number; // in minutes
    complexity: 'low' | 'medium' | 'high';
  }> {
    try {
      const document = await this.getDocument(documentId);
      
      if (!document) {
        throw new Error('Document not found');
      }

      const wordCount = document.content ? document.content.split(/\s+/).length : 0;
      const sectionCount = document.sections ? document.sections.length : 0;
      const readingTime = Math.ceil(wordCount / 200); // Average reading speed

      // Simple complexity calculation based on word count and sentence length
      const sentences = document.content ? document.content.split(/[.!?]+/).length : 1;
      const avgWordsPerSentence = wordCount / sentences;
      
      let complexity: 'low' | 'medium' | 'high' = 'low';
      if (avgWordsPerSentence > 20 || wordCount > 10000) {
        complexity = 'high';
      } else if (avgWordsPerSentence > 15 || wordCount > 5000) {
        complexity = 'medium';
      }

      return {
        wordCount,
        sectionCount,
        readingTime,
        complexity
      };
    } catch (error) {
      console.error('Error in getDocumentStats:', error);
      return {
        wordCount: 0,
        sectionCount: 0,
        readingTime: 0,
        complexity: 'low'
      };
    }
  }

  /**
   * Check if document processing is supported
   */
  isFileSupported(file: File): boolean {
    return documentProcessorService.isSupportedFileType(file);
  }

  /**
   * Get supported file types
   */
  getSupportedFileTypes(): string[] {
    return [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain'
    ];
  }

  /**
   * Get file type display names
   */
  getFileTypeDisplayNames(): { [key: string]: string } {
    return {
      'application/pdf': 'PDF',
      'application/msword': 'Word Document (DOC)',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word Document (DOCX)',
      'text/plain': 'Text File'
    };
  }
}

export const documentService = new DocumentService();
