/**
 * AI Humanizer Constants
 * Configuration constants for the AI writing style humanizer module
 */

import {
  <PERSON>Text,
  Wand2,
  Brain,
  Settings,
  Download,
  Upload,
  Eye,
  Edit3,
  Sparkles,
  Target,
  Zap,
  BookOpen
} from "lucide-react";
import { HumanizerAIModel, HumanizerSettings } from "./types";

// Available AI models for academic writing humanization
export const HUMANIZER_AI_MODELS: HumanizerAIModel[] = [
  // Google Models (via OpenRouter for CORS compatibility)
  {
    id: "google/gemini-2.5-flash",
    name: "Gemini 2.5 Flash (Recommended)",
    provider: "Google",
    description: "Fast and efficient model for academic writing style analysis",
    maxTokens: 8192,
    supportsStreaming: true,
    cost: 'low',
    strengths: ['Speed', 'Academic Writing', 'Style Analysis'],
    bestFor: ['Research papers', 'Quick rewrites', 'Academic content']
  },
  {
    id: "google/gemini-2.5-pro",
    name: "Gemini 2.5 Pro",
    provider: "Google",
    description: "Advanced reasoning for complex academic style transfer",
    maxTokens: 8192,
    supportsStreaming: true,
    cost: 'medium',
    strengths: ['Deep Analysis', 'Complex Reasoning', 'Academic Precision'],
    bestFor: ['Dissertations', 'Complex academic content', 'Detailed analysis']
  },

  // OpenRouter Models
  {
    id: "anthropic/claude-3.5-sonnet",
    name: "Claude 3.5 Sonnet",
    provider: "Anthropic",
    description: "Excellent for academic writing and scholarly style transfer",
    maxTokens: 200000,
    supportsStreaming: true,
    cost: 'medium',
    strengths: ['Academic Writing', 'Long Context', 'Scholarly Analysis'],
    bestFor: ['Long dissertations', 'Academic papers', 'Research content']
  },
  {
    id: "openai/gpt-4o",
    name: "GPT-4o",
    provider: "OpenAI",
    description: "Advanced model for academic content humanization",
    maxTokens: 8192,
    supportsStreaming: true,
    cost: 'high',
    strengths: ['Versatility', 'Academic Writing', 'Style Consistency'],
    bestFor: ['Research papers', 'Academic reports', 'Scholarly content']
  },
  {
    id: "openai/gpt-4o-mini",
    name: "GPT-4o Mini",
    provider: "OpenAI",
    description: "Cost-effective model for academic writing tasks",
    maxTokens: 8192,
    supportsStreaming: true,
    cost: 'low',
    strengths: ['Cost Effective', 'Good Quality', 'Academic Focus'],
    bestFor: ['Student papers', 'Quick edits', 'Budget-friendly option']
  }
];

// Default settings
export const DEFAULT_HUMANIZER_SETTINGS: HumanizerSettings = {
  preferredModel: "google/gemini-2.5-flash",
  preserveFormatting: true,
  maintainTechnicalTerms: true,
  creativityLevel: 'moderate',
  outputStyle: 'natural',
  autoSave: true,
  showAnalysisDetails: true
};

// Processing steps for UI feedback
export const PROCESSING_STEPS = {
  ANALYZING_SAMPLES: {
    name: "Analyzing Sample Texts",
    description: "Examining writing patterns and style characteristics",
    estimatedTime: 15 // seconds
  },
  GENERATING_PROFILE: {
    name: "Generating Style Profile",
    description: "Creating your personalized writing style model",
    estimatedTime: 10
  },
  HUMANIZING_CONTENT: {
    name: "Humanizing Content",
    description: "Rewriting content to match your style",
    estimatedTime: 20
  },
  FINALIZING: {
    name: "Finalizing Results",
    description: "Preparing final output and analysis",
    estimatedTime: 5
  }
} as const;

// Sample text templates for academic writing guidance
export const SAMPLE_TEXT_TEMPLATES = [
  {
    id: 'research-paper',
    name: 'Research Paper',
    description: 'A section from a research paper you\'ve authored',
    placeholder: 'Paste a section from your research paper that demonstrates your academic writing style...'
  },
  {
    id: 'dissertation',
    name: 'Dissertation/Thesis',
    description: 'A chapter or section from your dissertation or thesis',
    placeholder: 'Paste a section from your dissertation or thesis that showcases your scholarly voice...'
  },
  {
    id: 'academic-report',
    name: 'Academic Report',
    description: 'A formal academic report or technical document',
    placeholder: 'Paste a section from an academic report or technical document you\'ve written...'
  },
  {
    id: 'journal-article',
    name: 'Journal Article',
    description: 'A published or submitted journal article',
    placeholder: 'Paste a section from a journal article that represents your academic writing style...'
  },
  {
    id: 'conference-paper',
    name: 'Conference Paper',
    description: 'A conference paper or proceedings contribution',
    placeholder: 'Paste a section from a conference paper that demonstrates your research writing...'
  }
];

// Style analysis prompts
export const STYLE_ANALYSIS_PROMPT = `Act as an academic writing style analyst specializing in research papers, dissertations, and scholarly publications. Analyze the following academic writing samples to understand the author's unique scholarly style, tone, vocabulary choices, sentence structure, and voice patterns.

Sample Academic Texts:
{SAMPLE_TEXTS}

Please provide a comprehensive analysis focusing on academic writing characteristics:
1. **Academic Tone & Voice**: Identify the scholarly tone (formal, authoritative, analytical, etc.) and academic voice characteristics
2. **Academic Vocabulary & Language**: Analyze technical terminology usage, academic phrase patterns, and disciplinary language conventions
3. **Sentence Structure**: Examine academic sentence complexity, clause structures, and scholarly writing patterns
4. **Research Writing Patterns**: Identify argument structure, evidence presentation, citation integration, and academic discourse markers
5. **Scholarly Characteristics**: Highlight distinctive elements of this academic writing style including objectivity, precision, and scholarly conventions

Format your response as a detailed JSON object with the following structure:
{
  "tone": ["list", "of", "tone", "characteristics"],
  "vocabulary": {
    "complexity": "simple|moderate|complex|academic",
    "commonWords": ["frequently", "used", "words"],
    "preferredPhrases": ["characteristic", "phrases"]
  },
  "sentenceStructure": {
    "averageLength": number,
    "complexity": "simple|compound|complex|varied",
    "patterns": ["structural", "patterns"]
  },
  "voice": {
    "perspective": "first|second|third|mixed",
    "formality": "casual|semi-formal|formal|academic",
    "personality": ["personality", "traits"]
  },
  "writingPatterns": {
    "paragraphLength": "short|medium|long|varied",
    "transitionStyle": ["transition", "methods"],
    "emphasisMethods": ["emphasis", "techniques"]
  },
  "insights": ["key", "insights", "about", "the", "style"],
  "confidence": 0.85
}`;

export const HUMANIZATION_PROMPT = `Act as an academic writing specialist and scholarly style analyst. First, analyze the following academic writing samples to understand the scholarly writing style, tone, vocabulary choices, sentence structure, and academic voice:

{SAMPLE_TEXTS}

Then, rewrite the following academic content to match exactly the same scholarly writing style, tone, and voice patterns you identified from the samples:

{USER_TEXT_TO_REWRITE}

Requirements for Academic Writing:
- Maintain the original meaning, arguments, and key academic information
- Apply the learned scholarly writing style consistently
- Preserve all technical terms, academic terminology, and disciplinary language
- Maintain academic rigor and scholarly tone
- Keep the same logical structure and argumentative flow
- Ensure proper academic discourse markers and transitions
- Make the rewritten text sound natural and authentic to the learned academic style
- Preserve any citations or references in their original format

Provide only the rewritten academic text without explanations or additional commentary.`;

// UI configuration
export const UI_CONFIG = {
  TABS: [
    {
      id: 'samples',
      name: 'Sample Texts',
      icon: Upload,
      description: 'Add your writing samples'
    },
    {
      id: 'content',
      name: 'Content to Humanize',
      icon: Edit3,
      description: 'Input content to rewrite'
    },
    {
      id: 'results',
      name: 'Results',
      icon: Sparkles,
      description: 'View humanized content'
    },
    {
      id: 'settings',
      name: 'Settings',
      icon: Settings,
      description: 'Configure preferences'
    }
  ],
  CREATIVITY_LEVELS: [
    {
      id: 'conservative',
      name: 'Conservative',
      description: 'Minimal changes, preserve original structure',
      icon: Target
    },
    {
      id: 'moderate',
      name: 'Moderate',
      description: 'Balanced approach with natural improvements',
      icon: Brain
    },
    {
      id: 'creative',
      name: 'Creative',
      description: 'More creative interpretation while maintaining style',
      icon: Wand2
    }
  ],
  OUTPUT_STYLES: [
    {
      id: 'natural',
      name: 'Natural',
      description: 'Focus on natural, human-like writing',
      icon: BookOpen
    },
    {
      id: 'enhanced',
      name: 'Enhanced',
      description: 'Improve clarity and flow while maintaining style',
      icon: Zap
    },
    {
      id: 'creative',
      name: 'Creative',
      description: 'Add creative flair within style boundaries',
      icon: Sparkles
    }
  ]
} as const;

// Validation messages
export const VALIDATION_MESSAGES = {
  SAMPLE_TEXT_TOO_SHORT: 'Sample text must be at least 50 characters long',
  SAMPLE_TEXT_TOO_LONG: 'Sample text cannot exceed 5,000 characters',
  CONTENT_TOO_SHORT: 'Content must be at least 10 characters long',
  CONTENT_TOO_LONG: 'Content cannot exceed 4,000 words',
  MAX_SAMPLES_REACHED: 'Maximum of 10 sample texts allowed',
  EMPTY_SAMPLE_LABEL: 'Please provide a label for your sample text',
  DUPLICATE_SAMPLE_LABEL: 'A sample with this label already exists',
  NO_SAMPLES_PROVIDED: 'Please add at least one sample text before proceeding',
  NO_CONTENT_PROVIDED: 'Please provide content to humanize'
} as const;

// Export formats
export const EXPORT_FORMATS = [
  { id: 'txt', name: 'Plain Text (.txt)', icon: FileText },
  { id: 'docx', name: 'Word Document (.docx)', icon: FileText },
  { id: 'pdf', name: 'PDF Document (.pdf)', icon: FileText },
  { id: 'html', name: 'HTML File (.html)', icon: FileText }
] as const;

// Color scheme for UI consistency
export const COLORS = {
  primary: 'blue',
  secondary: 'purple',
  success: 'green',
  warning: 'yellow',
  error: 'red',
  info: 'cyan'
} as const;
