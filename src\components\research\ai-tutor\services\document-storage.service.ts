/**
 * Document Storage Service
 * Handles document storage and retrieval using Supabase
 * for the Research Comprehension Platform
 */

import { supabase } from '@/lib/supabase';
import { ResearchDocument, DocumentSection, LearningProgress } from '../types';

interface StorageResult<T> {
  data: T | null;
  error: string | null;
}

class DocumentStorageService {
  private readonly BUCKET_NAME = 'research-documents';

  /**
   * Check if Supabase is configured
   */
  isConfigured(): boolean {
    try {
      return Boolean(supabase);
    } catch (error) {
      console.error('Failed to check Supabase configuration:', error);
      return false;
    }
  }

  /**
   * Get current user ID
   */
  private async getCurrentUserId(): Promise<string | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      return user?.id || null;
    } catch (error) {
      console.error('Failed to get current user:', error);
      return null;
    }
  }

  /**
   * Upload file to Supabase storage
   */
  async uploadFile(file: File, documentId: string): Promise<StorageResult<string>> {
    if (!this.isConfigured()) {
      return { data: null, error: 'Supabase not configured' };
    }

    try {
      const userId = await this.getCurrentUserId();
      if (!userId) {
        return { data: null, error: 'User not authenticated' };
      }

      // Generate unique file path
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileExtension = file.name.split('.').pop();
      const sanitizedFileName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_');
      const fileName = `${sanitizedFileName}_${timestamp}.${fileExtension}`;
      const filePath = `${userId}/${documentId}/${fileName}`;

      // Upload file
      const { data, error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false,
          metadata: {
            originalName: file.name,
            uploadedAt: new Date().toISOString(),
            userId: userId,
            documentId: documentId,
            fileSize: file.size.toString(),
            mimeType: file.type
          }
        });

      if (error) {
        console.error('Error uploading file:', error);
        return { data: null, error: error.message };
      }

      return { data: data.path, error: null };
    } catch (error) {
      console.error('Error in uploadFile:', error);
      return { data: null, error: error.message };
    }
  }

  /**
   * Save research document to database
   */
  async saveDocument(document: ResearchDocument): Promise<StorageResult<ResearchDocument>> {
    if (!this.isConfigured()) {
      return { data: null, error: 'Supabase not configured' };
    }

    try {
      const userId = await this.getCurrentUserId();
      if (!userId) {
        return { data: null, error: 'User not authenticated' };
      }

      // Sanitize document.content to prevent unsupported Unicode escape sequence errors
      const safeContent = typeof document.content === 'string'
        ? document.content.replace(/\\(?![\\'"bfnrtu])/g, '\\\\') // double lone backslashes
            .replace(/[\u0000-\u001F\u007F-\u009F]/g, '') // remove control chars
        : document.content;

      // Save main document - using the actual schema from research_analysis_schema.sql
      const { data: docData, error: docError } = await supabase
        .from('research_documents')
        .insert({
          id: document.id,
          user_id: userId,
          title: document.title,
          authors: document.authors,
          abstract: document.abstract,
          // Note: removed 'content' field as it doesn't exist in the actual schema
          filename: document.title, // Use title as filename if not provided
          file_path: document.filePath,
          file_size: document.fileSize,
          file_type: document.fileType,
          raw_content: safeContent, // Store sanitized content in raw_content field
          status: 'ready', // Set status as ready
          uploaded_at: document.uploadedAt.toISOString(),
          created_at: document.uploadedAt.toISOString(),
          updated_at: document.processedAt.toISOString()
        })
        .select()
        .single();

      if (docError) {
        console.error('Error saving document:', docError);

        // Check if it's a table not found error
        if (docError.message?.includes('relation "research_documents" does not exist') ||
            docError.message?.includes('table')) {
          console.warn('Database tables not set up yet, cannot save document');
          return { data: null, error: 'Database not configured. Please contact administrator.' };
        }

        return { data: null, error: docError.message };
      }

      // Save document sections
      if (document.sections.length > 0) {
        const sectionsData = document.sections.map(section => ({
          id: section.id,
          document_id: document.id,
          title: section.title,
          content: section.content,
          section_type: section.type,
          start_page: section.startPage,
          end_page: section.endPage,
          order_index: section.orderIndex
        }));

        const { error: sectionsError } = await supabase
          .from('document_sections')
          .insert(sectionsData);

        if (sectionsError) {
          console.error('Error saving sections:', sectionsError);
          // Don't fail the entire operation for section errors
        }
      }

      return { data: document, error: null };
    } catch (error) {
      console.error('Error in saveDocument:', error);
      return { data: null, error: error.message };
    }
  }

  /**
   * Get user's documents
   */
  async getUserDocuments(userId?: string): Promise<StorageResult<ResearchDocument[]>> {
    if (!this.isConfigured()) {
      return { data: [], error: 'Supabase not configured' };
    }

    try {
      const currentUserId = userId || await this.getCurrentUserId();

      if (!currentUserId) {
        return { data: [], error: 'User not authenticated' };
      }

      const { data, error } = await supabase
        .from('research_documents')
        .select(`
          *,
          document_sections (*)
        `)
        .eq('user_id', currentUserId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching documents:', error);

        // Check if it's a table not found error (common when database isn't set up)
        if (error.message?.includes('relation "research_documents" does not exist') ||
            error.message?.includes('table') ||
            error.code === 'PGRST116') {
          console.warn('Database tables not set up yet, returning empty array');
          return { data: [], error: null };
        }

        return { data: [], error: error.message };
      }

      // Check if data exists and is an array
      if (!data || !Array.isArray(data)) {
        console.warn('No documents found or invalid data format');
        return { data: [], error: null };
      }

      // Transform database format to application format
      const documents: ResearchDocument[] = data.map(doc => ({
        id: doc.id,
        title: doc.title,
        authors: doc.authors || [],
        abstract: doc.abstract || '',
        content: doc.content || '',
        sections: (doc.document_sections || []).map((section: any) => ({
          id: section.id,
          title: section.title,
          content: section.content,
          type: section.section_type,
          startPage: section.start_page,
          endPage: section.end_page,
          orderIndex: section.order_index
        })),
        metadata: doc.metadata || {},
        uploadedAt: new Date(doc.created_at),
        processedAt: new Date(doc.updated_at),
        userId: doc.user_id,
        filePath: doc.file_path,
        fileSize: doc.file_size,
        fileType: doc.file_type
      }));

      return { data: documents, error: null };
    } catch (error) {
      console.error('Error in getUserDocuments:', error);
      return { data: null, error: error.message };
    }
  }

  /**
   * Get single document by ID
   */
  async getDocument(documentId: string): Promise<StorageResult<ResearchDocument>> {
    if (!this.isConfigured()) {
      return { data: null, error: 'Supabase not configured' };
    }

    try {
      const { data, error } = await supabase
        .from('research_documents')
        .select(`
          *,
          document_sections (*)
        `)
        .eq('id', documentId)
        .single();

      if (error) {
        console.error('Error fetching document:', error);
        return { data: null, error: error.message };
      }

      // Check if data exists
      if (!data) {
        console.warn('Document not found');
        return { data: null, error: 'Document not found' };
      }

      // Transform to application format
      const document: ResearchDocument = {
        id: data.id,
        title: data.title,
        authors: data.authors || [],
        abstract: data.abstract || '',
        content: data.content || '',
        sections: (data.document_sections || []).map((section: any) => ({
          id: section.id,
          title: section.title,
          content: section.content,
          type: section.section_type,
          startPage: section.start_page,
          endPage: section.end_page,
          orderIndex: section.order_index
        })),
        metadata: data.metadata || {},
        uploadedAt: new Date(data.created_at),
        processedAt: new Date(data.updated_at),
        userId: data.user_id,
        filePath: data.file_path,
        fileSize: data.file_size,
        fileType: data.file_type
      };

      return { data: document, error: null };
    } catch (error) {
      console.error('Error in getDocument:', error);
      return { data: null, error: error.message };
    }
  }

  /**
   * Delete document and associated data
   */
  async deleteDocument(documentId: string): Promise<StorageResult<boolean>> {
    if (!this.isConfigured()) {
      return { data: null, error: 'Supabase not configured' };
    }

    try {
      const userId = await this.getCurrentUserId();
      if (!userId) {
        return { data: null, error: 'User not authenticated' };
      }

      // Delete from database (sections will be deleted via cascade)
      const { error: dbError } = await supabase
        .from('research_documents')
        .delete()
        .eq('id', documentId)
        .eq('user_id', userId);

      if (dbError) {
        console.error('Error deleting document from database:', dbError);
        return { data: null, error: dbError.message };
      }

      // Delete file from storage
      try {
        const { error: storageError } = await supabase.storage
          .from(this.BUCKET_NAME)
          .remove([`${userId}/${documentId}`]);

        if (storageError) {
          console.warn('Error deleting file from storage:', storageError);
          // Don't fail the operation for storage errors
        }
      } catch (storageError) {
        console.warn('Storage deletion failed:', storageError);
      }

      return { data: true, error: null };
    } catch (error) {
      console.error('Error in deleteDocument:', error);
      return { data: null, error: error.message };
    }
  }

  /**
   * Update document metadata
   */
  async updateDocument(documentId: string, updates: Partial<ResearchDocument>): Promise<StorageResult<ResearchDocument>> {
    if (!this.isConfigured()) {
      return { data: null, error: 'Supabase not configured' };
    }

    try {
      const userId = await this.getCurrentUserId();
      if (!userId) {
        return { data: null, error: 'User not authenticated' };
      }

      const updateData: any = {
        updated_at: new Date().toISOString()
      };

      if (updates.title) updateData.title = updates.title;
      if (updates.authors) updateData.authors = updates.authors;
      if (updates.abstract) updateData.abstract = updates.abstract;
      if (updates.metadata) updateData.metadata = updates.metadata;

      const { data, error } = await supabase
        .from('research_documents')
        .update(updateData)
        .eq('id', documentId)
        .eq('user_id', userId)
        .select()
        .single();

      if (error) {
        console.error('Error updating document:', error);
        return { data: null, error: error.message };
      }

      // Get the updated document with sections
      return this.getDocument(documentId);
    } catch (error) {
      console.error('Error in updateDocument:', error);
      return { data: null, error: error.message };
    }
  }

  /**
   * Save learning progress
   */
  async saveLearningProgress(progress: LearningProgress): Promise<StorageResult<LearningProgress>> {
    if (!this.isConfigured()) {
      return { data: null, error: 'Supabase not configured' };
    }

    try {
      const { data, error } = await supabase
        .from('learning_progress')
        .upsert({
          id: progress.id,
          user_id: progress.userId,
          document_id: progress.documentId,
          comprehension_level: progress.comprehensionLevel,
          concepts_learned: progress.conceptsLearned,
          concepts_mastered: progress.conceptsMastered,
          quizzes_completed: progress.quizzesCompleted,
          games_completed: progress.gamesCompleted,
          time_spent: progress.timeSpent,
          sessions_count: progress.sessionsCount,
          last_activity: progress.lastActivity.toISOString(),
          created_at: progress.createdAt.toISOString(),
          updated_at: progress.updatedAt.toISOString()
        })
        .select()
        .single();

      if (error) {
        console.error('Error saving learning progress:', error);
        return { data: null, error: error.message };
      }

      return { data: progress, error: null };
    } catch (error) {
      console.error('Error in saveLearningProgress:', error);
      return { data: null, error: error.message };
    }
  }

  /**
   * Get learning progress for a document
   */
  async getLearningProgress(userId: string, documentId: string): Promise<StorageResult<LearningProgress>> {
    if (!this.isConfigured()) {
      return { data: null, error: 'Supabase not configured' };
    }

    try {
      const { data, error } = await supabase
        .from('learning_progress')
        .select('*')
        .eq('user_id', userId)
        .eq('document_id', documentId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // No progress found, return null
          return { data: null, error: null };
        }
        console.error('Error fetching learning progress:', error);
        return { data: null, error: error.message };
      }

      const progress: LearningProgress = {
        id: data.id,
        userId: data.user_id,
        documentId: data.document_id,
        comprehensionLevel: data.comprehension_level,
        conceptsLearned: data.concepts_learned || [],
        conceptsMastered: data.concepts_mastered || [],
        quizzesCompleted: data.quizzes_completed || [],
        gamesCompleted: data.games_completed || [],
        timeSpent: data.time_spent,
        sessionsCount: data.sessions_count,
        lastActivity: new Date(data.last_activity),
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at)
      };

      return { data: progress, error: null };
    } catch (error) {
      console.error('Error in getLearningProgress:', error);
      return { data: null, error: error.message };
    }
  }
}

export const documentStorageService = new DocumentStorageService();
