import { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Textarea } from "@/components/ui/textarea";
import { Toggle } from "@/components/ui/toggle";
import { useLocation } from "react-router-dom";
import { toast } from "sonner";
import {
  Bold,
  Italic,
  Underline,
  List,
  ListOrdered,
  Quote,
  Code,
  Save,
  Download,
  Share,
  MoreHorizontal,
  Bot,
  Send,
  X,
  Zap,
  Sparkles,
  PenTool,
  BookOpen,
  FileText,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Lightbulb,
  Copy,
  ArrowRight,
  Wand2,
  Brain,
  Replace,
  PlusCircle,
  RotateCcw,
  FileUp,
  CheckCheck,
  History,
  Type,
  Heading1,
  Heading2,
  Heading3,
  Highlighter,
  Clock,
  Undo,
  FileDown,
  Image as ImageIcon,
  Table as TableIcon,
  HelpCircle,
  BookMarked,
  <PERSON>eat,
  <PERSON><PERSON><PERSON>riangle,
  <PERSON><PERSON><PERSON><PERSON>
} from "lucide-react";
import { Tabs, Ta<PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Card } from "@/components/ui/card";
import { editorService } from "../research/paper-generator/editor.service";
import { documentExportService } from "../research/paper-generator/document-export.service";
import { AI_MODELS } from "../research/paper-generator/constants";
import { cn } from "@/lib/utils";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import mammoth from 'mammoth';
import RichTextEditor, { RichTextEditorRef } from "./paper-generator/RichTextEditor";
import { formatConverter } from "./paper-generator/format-converter.service";
import * as htmlExportService from "./paper-generator/html-export.service";

interface AIResponse {
  id: string;
  content: string;
  status: "loading" | "completed" | "error";
  timestamp: Date;
  type?: "replace" | "insert" | "improve";
}

interface Message {
  id: string;
  type: "user" | "assistant";
  content: string;
  timestamp: Date;
}

interface AIOption {
  id: string;
  name: string;
  description: string;
  prompt: string;
  icon: React.ReactNode;
  color: string;
}

interface MainEditorProps {
  initialTitle?: string;
  initialContent?: string;
}

// New interface for revision history
interface RevisionEntry {
  id: string;
  timestamp: Date;
  content: string;
  title: string;
  description: string;
}

// Font options
const fontFamilyOptions = [
  { value: 'font-serif', label: 'Serif' },
  { value: 'font-sans', label: 'Sans Serif' },
  { value: 'font-mono', label: 'Monospace' },
  { value: 'font-georgia', label: 'Georgia' },
  { value: 'font-times', label: 'Times New Roman' },
  { value: 'font-arial', label: 'Arial' }
];

const fontSizeOptions = [
  { value: 'text-sm', label: 'Small' },
  { value: 'text-base', label: 'Medium' },
  { value: 'text-lg', label: 'Large' },
  { value: 'text-xl', label: 'X-Large' }
];

// 1. Add tone and depth options for AI
const toneOptions = [
  { value: 'formal', label: 'Formal' },
  { value: 'concise', label: 'Concise' },
  { value: 'persuasive', label: 'Persuasive' },
  { value: 'neutral', label: 'Neutral' },
];
const depthOptions = [
  { value: 'brief', label: 'Brief' },
  { value: 'detailed', label: 'Detailed' },
  { value: 'comprehensive', label: 'Comprehensive' },
];

export function MainEditor({ initialTitle, initialContent }: MainEditorProps) {
  const [title, setTitle] = useState(initialTitle || "Untitled Document");
  const [content, setContent] = useState(initialContent || `<h1>Introduction</h1>

<p>Welcome to your research paper editor. This modern editor provides enhanced features for academic writing including:</p>

<ul>
<li>AI-powered writing assistance</li>
<li>Resizable images and tables</li>
<li>Advanced formatting options</li>
<li>Export to DOCX and PDF formats</li>
<li>Auto-save and revision history</li>
</ul>

<h2>Literature Review</h2>

<p>Begin your literature review here. Use the AI assistant to help improve your academic writing, suggest citations, and enhance clarity.</p>

<h2>Methodology</h2>

<p>Describe your research methodology in detail...</p>

<h2>Results</h2>

<p>Present your findings and analysis...</p>

<h2>Discussion</h2>

<p>Discuss the implications of your results...</p>

<h2>Conclusion</h2>

<p>Summarize your work and suggest future research directions...</p>
`);

  // Try to load content from localStorage if coming from paper generator
  useEffect(() => {
    const editorContent = editorService.getEditorContent();
    
    if (editorContent) {
      setTitle(editorContent.title || "Imported Research Paper");
      setContent(editorContent.content || "");
      toast.success("Research paper successfully imported to editor!");
    }
  }, []);

  // Basic formatting states
  const [isBold, setIsBold] = useState(false);
  const [isItalic, setIsItalic] = useState(false);
  const [isUnderline, setIsUnderline] = useState(false);
  
  // Text selection states
  const [selectedText, setSelectedText] = useState("");
  const [selectionPosition, setSelectionPosition] = useState({ x: 0, y: 0 });
  const [selectionRange, setSelectionRange] = useState({ start: 0, end: 0 });
  const [showAIOptions, setShowAIOptions] = useState(false);
  
  // AI integration states
  const [selectedAIOption, setSelectedAIOption] = useState<string | null>(null);
  const [aiResponse, setAIResponse] = useState<AIResponse | null>(null);
  const [customPrompt, setCustomPrompt] = useState("");
  const [showCustomPrompt, setShowCustomPrompt] = useState(false);
  const [model, setModel] = useState(AI_MODELS[0].id);
  const [showAIPanel, setShowAIPanel] = useState(false);
  
  // New states for additional features
  const [fontFamily, setFontFamily] = useState(fontFamilyOptions[0].value);
  const [fontSize, setFontSize] = useState(fontSizeOptions[1].value);
  const [revisionHistory, setRevisionHistory] = useState<RevisionEntry[]>([]);
  const [showRevisionHistory, setShowRevisionHistory] = useState(false);
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [exportFormat, setExportFormat] = useState("docx");
  const [docxFileName, setDocxFileName] = useState("");
  const [pdfFileName, setPdfFileName] = useState("");
  
  // Tone and depth states
  const [tone, setTone] = useState(toneOptions[0].value);
  const [depth, setDepth] = useState(depthOptions[0].value);
  
  // File upload ref
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // Rich Text Editor ref
  const richTextEditorRef = useRef<RichTextEditorRef>(null);
  
  // API key from .env
  const apiKey = import.meta.env.VITE_OPENROUTER_API_KEY || "";
  const apiBase = "https://openrouter.ai/api/v1/chat/completions";

  // Academic-focused AI writing options (improved and expanded)
  const academicAIOptions: AIOption[] = [
    {
      id: "improve-clarity",
      name: "Improve Clarity",
      description: "Edit to be clearer and more concise, using formal academic language. Do not add new information.",
      prompt: "You are an academic writing editor. Edit the following text to be clearer and more concise, using formal academic language. Do not use markdown syntax (no #, *, etc.). Return only the improved text without explanations. Do not add new information:",
      icon: <Sparkles className="h-4 w-4" />,
      color: "blue"
    },
    {
      id: "expand-point",
      name: "Expand Point",
      description: "Expand with relevant evidence or examples. Do not repeat or add unrelated content.",
      prompt: "You are an academic writing assistant. Expand the following point with relevant evidence, examples, or analysis. Do not use markdown syntax (no #, *, etc.). Include supporting citations where appropriate. Do not repeat or add unrelated content:",
      icon: <AlignLeft className="h-4 w-4" />,
      color: "indigo"
    },
    {
      id: "suggest-citations",
      name: "Suggest Citations",
      description: "List 3-5 recent, high-quality academic sources (APA format) that directly support the text. Only output citations.",
      prompt: "You are an academic research assistant. List 3-5 recent, high-quality academic sources (APA format, 2018-2024) that directly support the following text. Provide realistic but hypothetical citations with proper APA formatting. Only output the citations without additional commentary:",
      icon: <BookOpen className="h-4 w-4" />,
      color: "amber"
    },
    {
      id: "rephrase-academic",
      name: "Academic Tone",
      description: "Rewrite in a formal, objective academic style. Do not add or remove information.",
      prompt: "You are an academic writing editor. Rewrite the following text in a formal, objective academic style suitable for a peer-reviewed journal. Do not use markdown syntax (no #, *, etc.). Do not add or remove information. Return only the rewritten text:",
      icon: <FileText className="h-4 w-4" />,
      color: "emerald"
    },
    {
      id: "critique-argument",
      name: "Critique",
      description: "Briefly analyze strengths and weaknesses, focusing on logic and evidence. Be concise.",
      prompt: "Briefly analyze the strengths and weaknesses of the following argument, focusing on logic and evidence. Be concise:",
      icon: <PenTool className="h-4 w-4" />,
      color: "rose"
    },
    {
      id: "summarize",
      name: "Summarize",
      description: "Summarize in 2-3 sentences, focusing only on main points. Do not add interpretation.",
      prompt: "Summarize the following text in 2-3 sentences, focusing only on the main points. Do not add interpretation:",
      icon: <List className="h-4 w-4" />,
      color: "purple"
    },
    // Section generation options
    {
      id: "introduction",
      name: "Introduction",
      description: "Write an academic introduction for the selected topic or text.",
      prompt: "You are an expert academic writer. Write a comprehensive introduction for a research paper based on the following topic or text. Do not use markdown syntax (no #, *, etc.). Structure with plain text heading 'Introduction' followed by well-developed paragraphs. Include background context, problem statement, objectives, and significance. Include relevant citations in APA format (e.g., Smith, 2022). Length: 500-800 words:",
      icon: <Heading1 className="h-4 w-4" />,
      color: "cyan"
    },
    {
      id: "literature-review",
      name: "Literature Review",
      description: "Write a brief literature review summarizing key research related to the topic.",
      prompt: "You are an expert academic researcher. Write a comprehensive literature review on the following topic. Do not use markdown syntax (no #, *, etc.). Begin with plain text heading 'Literature Review'. Include current state of research, key theories, major findings, and research gaps. Use proper APA citations throughout (e.g., Johnson & Smith, 2023). Length: 1000-1500 words. End with a References section listing all cited works:",
      icon: <BookOpen className="h-4 w-4" />,
      color: "violet"
    },
    {
      id: "methodology",
      name: "Methodology",
      description: "Describe a suitable research methodology for the topic or text.",
      prompt: "You are an expert research methodologist. Write a detailed methodology section for the following research topic. Do not use markdown syntax (no #, *, etc.). Begin with plain text heading 'Methodology'. Include research design, participants/materials, procedures, data collection methods, and analytical approaches. Cite methodological sources in APA format. Be specific enough for replication:",
      icon: <FileText className="h-4 w-4" />,
      color: "lime"
    },
    {
      id: "results",
      name: "Results",
      description: "Present possible results or findings for a study based on the topic or text.",
      prompt: "Present possible results or findings for a study based on the following topic or text. Be factual and avoid speculation:",
      icon: <ListOrdered className="h-4 w-4" />,
      color: "orange"
    },
    {
      id: "discussion",
      name: "Discussion",
      description: "Discuss the implications of the results or topic in an academic style.",
      prompt: "You are an expert academic writer. Write a comprehensive discussion section based on the following results or topic. Do not use markdown syntax (no #, *, etc.). Begin with plain text heading 'Discussion'. Interpret findings in context of existing literature, address research questions, discuss implications, acknowledge limitations, and suggest future research. Include relevant citations in APA format. Length: 800-1200 words:",
      icon: <AlignJustify className="h-4 w-4" />,
      color: "teal"
    },
    {
      id: "conclusion",
      name: "Conclusion",
      description: "Write a concise academic conclusion for the topic or text.",
      prompt: "Write a concise academic conclusion for the following topic or text. Do not introduce new information:",
      icon: <CheckCheck className="h-4 w-4" />,
      color: "red"
    },
    {
      id: "abstract",
      name: "Abstract",
      description: "Write a concise abstract (max 150 words) for a research paper based on the topic or text.",
      prompt: "Write a concise abstract (max 150 words) for a research paper based on the following topic or text:",
      icon: <Type className="h-4 w-4" />,
      color: "yellow"
    },
  ];

  // New AI prompt options and categories
  const researchAIOptions: AIOption[] = [
    ...academicAIOptions,
    {
      id: 'generate-questions',
      name: 'Generate Research Questions',
      description: 'Suggest 3-5 research questions based on the topic or text.',
      prompt: 'Suggest 3-5 research questions for the following topic or text:',
      icon: <HelpCircle className="h-4 w-4" />, color: 'sky',
    },
    {
      id: 'related-work',
      name: 'Suggest Related Work',
      description: 'List recent, relevant papers or studies related to the topic.',
      prompt: 'List recent, relevant academic papers or studies related to the following topic or text:',
      icon: <BookMarked className="h-4 w-4" />, color: 'fuchsia',
    },
    {
      id: 'paraphrase',
      name: 'Paraphrase (Plagiarism)',
      description: 'Paraphrase the text to reduce plagiarism while keeping meaning.',
      prompt: 'Paraphrase the following text to reduce plagiarism while keeping the original meaning:',
      icon: <Repeat className="h-4 w-4" />, color: 'orange',
    },
    {
      id: 'logical-fallacies',
      name: 'Check Logical Fallacies',
      description: 'Identify logical fallacies or weak arguments in the text.',
      prompt: 'Identify any logical fallacies or weak arguments in the following text:',
      icon: <AlertTriangle className="h-4 w-4" />, color: 'rose',
    },
    {
      id: 'suggest-figures',
      name: 'Suggest Figures/Tables',
      description: 'Suggest figures or tables that could support the text.',
      prompt: 'Suggest figures or tables that could support the following text:',
      icon: <BarChart3 className="h-4 w-4" />, color: 'teal',
    },
    {
      id: 'generate-title',
      name: 'Generate Title/Keywords',
      description: 'Suggest a suitable title and 5-7 keywords for the paper.',
      prompt: 'Suggest a suitable academic title and 5-7 keywords for the following paper or topic:',
      icon: <Type className="h-4 w-4" />, color: 'yellow',
    },
  ];

  // Academic section generation templates (for reference, can be removed if all are in academicAIOptions)
  const sectionPrompts = {
    introduction: academicAIOptions.find(opt => opt.id === 'introduction')?.prompt || '',
    literatureReview: academicAIOptions.find(opt => opt.id === 'literature-review')?.prompt || '',
    methodology: academicAIOptions.find(opt => opt.id === 'methodology')?.prompt || '',
    results: academicAIOptions.find(opt => opt.id === 'results')?.prompt || '',
    discussion: academicAIOptions.find(opt => opt.id === 'discussion')?.prompt || '',
    conclusion: academicAIOptions.find(opt => opt.id === 'conclusion')?.prompt || '',
    abstract: academicAIOptions.find(opt => opt.id === 'abstract')?.prompt || '',
  };
  
  // Save current document state to revision history with improved reliability
  const saveRevision = (description: string = "Manual save") => {
    if (!richTextEditorRef.current) {
      toast.error("Editor not available, couldn't save revision");
      return;
    }
    
    try {
      // Get the current HTML content from the editor
      const htmlContent = richTextEditorRef.current.getHTML();
      
      // Check if there's actual content
      if (!htmlContent || htmlContent.trim() === '') {
        toast.warning("Cannot save empty document to revision history");
        return;
      }
      
      // Create new revision entry
      const newRevision: RevisionEntry = {
        id: Date.now().toString(),
        timestamp: new Date(),
        content: htmlContent,
        title: title,
        description
      };
      
      // Use functional update to ensure we're working with the latest state
      setRevisionHistory(prevHistory => {
        // Check if this revision is identical to the most recent one to avoid duplicates
        const mostRecent = prevHistory[0];
        
        if (mostRecent && mostRecent.content === htmlContent) {
          // Just update the timestamp and description
          const updatedHistory = [...prevHistory];
          updatedHistory[0] = {
            ...mostRecent,
            timestamp: new Date(),
            description: description
          };
          return updatedHistory;
        } else {
          // Add new revision
          return [newRevision, ...prevHistory];
        }
      });
      
      toast.success("Document revision saved");
    } catch (error) {
      console.error("Error saving revision:", error);
      toast.error("Failed to save document revision");
    }
  };

  // Revert to a previous revision with improved handling
  const revertToRevision = (revisionId: string) => {
    const revision = revisionHistory.find(r => r.id === revisionId);
    if (!revision) {
      toast.error("Could not find the selected revision");
      return;
    }
    
    // Save current state before reverting
    saveRevision("Auto-save before reverting");
    
    try {
      // Make sure we have richTextEditorRef
      if (richTextEditorRef.current) {
        // Set content directly to the editor for proper rendering
        richTextEditorRef.current.editor?.commands.setContent(revision.content);
        
        // Update the state
        setContent(revision.content);
        setTitle(revision.title);
        
        // If editor doesn't have focus, focus it
        richTextEditorRef.current.editor?.commands.focus();
        
        toast.success(`Successfully reverted to revision from ${revision.timestamp.toLocaleString()}`);
      } else {
        // Fallback if editor ref isn't available
        setContent(revision.content);
        setTitle(revision.title);
        toast.success(`Reverted to revision. Please check if content looks correct.`);
      }
      
      setShowRevisionHistory(false);
    } catch (error) {
      console.error("Error reverting to revision:", error);
      toast.error("Failed to revert to the selected revision. Please try again.");
    }
  };

  // Handle document upload
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Create a revision before importing
    saveRevision("Auto-save before file import");

    // Removed file size limit as requested

    // Show loading indicator
    const loadingToast = toast.loading(`Importing ${file.name}...`);
    
    const reader = new FileReader();
    
    if (file.name.toLowerCase().endsWith('.docx')) {
      reader.onload = async (e) => {
        try {
          const arrayBuffer = e.target?.result;
          if (!arrayBuffer || typeof arrayBuffer === 'string') {
            toast.dismiss(loadingToast);
            toast.error("Invalid file data");
            return;
          }
          const result = await mammoth.convertToHtml({ arrayBuffer });
          if (!result.value || result.value.trim() === '') {
            toast.dismiss(loadingToast);
            toast.error("The document appears to be empty or could not be parsed correctly");
            return;
          }
          // Set the HTML content directly to the RichTextEditor (fix: update editor via ref)
          if (richTextEditorRef.current && richTextEditorRef.current.editor) {
            richTextEditorRef.current.editor.commands.setContent(result.value);
          }
          setContent(result.value);
          setTitle(file.name.replace('.docx', ''));
          toast.dismiss(loadingToast);
          toast.success(`Successfully imported ${file.name}`);
          if (result.messages && result.messages.length > 0) {
            // Log any conversion warnings
            console.warn("Document import warnings:", result.messages);
          }
        } catch (error) {
          console.error("Error importing DOCX file:", error);
          toast.dismiss(loadingToast);
          toast.error("Failed to import DOCX file. Please make sure it's a valid document.");
        }
      };
      reader.onerror = () => {
        toast.dismiss(loadingToast);
        toast.error("Failed to read the document file");
      };
      reader.readAsArrayBuffer(file);
    } else if (file.name.toLowerCase().endsWith('.txt')) {
      reader.onload = (e) => {
        try {
          const text = e.target?.result;
          if (typeof text === 'string') {
            if (text.trim() === '') {
              toast.dismiss(loadingToast);
              toast.error("The text file is empty");
              return;
            }
            
            // Convert plain text to HTML for the RichTextEditor
            const html = text
              .split('\n\n')
              .map(paragraph => `<p>${paragraph.replace(/\n/g, '<br>')}</p>`)
              .join('');
              
            setContent(html);
            setTitle(file.name.replace('.txt', ''));
            
            toast.dismiss(loadingToast);
            toast.success(`Successfully imported ${file.name}`);
          } else {
            toast.dismiss(loadingToast);
            toast.error("Invalid file content");
          }
        } catch (error) {
          console.error("Error importing text file:", error);
          toast.dismiss(loadingToast);
          toast.error("Failed to import text file");
        }
      };
      reader.onerror = () => {
        toast.dismiss(loadingToast);
        toast.error("Failed to read the text file");
      };
      reader.readAsText(file);
    } else {
      toast.dismiss(loadingToast);
      toast.error("Unsupported file format. Please upload .docx or .txt files");
    }
    
    // Clear the file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Export document to DOCX format using the HTML export service
  const exportToDocx = async () => {
    try {
      if (!richTextEditorRef.current) {
        toast.error("Editor not available. Please try again.");
        return;
      }
      
      const fileName = docxFileName || title + '.docx';
      const htmlContent = richTextEditorRef.current.getHTML();
      
      if (!htmlContent || htmlContent.trim() === '') {
        toast.error("Cannot export empty document. Please add some content first.");
        return;
      }
      
      // Use the HTML export service to convert HTML to DOCX
      await htmlExportService.documentExportService.exportToDocx(title, htmlContent, fileName);
      toast.success(`Document exported as ${fileName}`);
    } catch (error) {
      console.error("Error exporting to DOCX:", error);
      toast.error("Failed to export document to DOCX format. Please try again.");
    }
  };

  // Export document to PDF format using the HTML export service
  const exportToPdf = async () => {
    try {
      if (!richTextEditorRef.current) {
        toast.error("Editor not available. Please try again.");
        return;
      }
      
      const fileName = pdfFileName || title + '.pdf';
      const htmlContent = richTextEditorRef.current.getHTML();
      
      if (!htmlContent || htmlContent.trim() === '') {
        toast.error("Cannot export empty document. Please add some content first.");
        return;
      }
      
      // Use the HTML export service to convert HTML to PDF
      await htmlExportService.documentExportService.exportToPdf(title, htmlContent, fileName);
      toast.success(`Document exported as ${fileName}`);
    } catch (error) {
      console.error("Error exporting to PDF:", error);
      toast.error("Failed to export document to PDF format. Please try again.");
    }
  };

  // Update formatting state based on editor's current state
  const updateFormattingState = () => {
    if (richTextEditorRef.current?.editor) {
      const editor = richTextEditorRef.current.editor;
      setIsBold(editor.isActive('bold'));
      setIsItalic(editor.isActive('italic'));
      setIsUnderline(editor.isActive('underline'));
    }
  };

  // Apply formatting to selected text
  const applyFormatting = (format: string) => {
    if (!richTextEditorRef.current) return;

    // Use the rich text editor's formatting API
    richTextEditorRef.current.applyFormat(format);

    // Update formatting state after applying format
    setTimeout(updateFormattingState, 10);
  };

  // Handle text selection
  const handleTextSelection = () => {
    if (!richTextEditorRef.current || !richTextEditorRef.current.editor) return;

    // Update formatting state based on current selection
    updateFormattingState();

    const selectedText = richTextEditorRef.current.getSelectedText();

    if (!selectedText) {
      setShowAIOptions(false);
      return;
    }

    setSelectedText(selectedText);

    // Get selection range from the editor
    const range = richTextEditorRef.current.getSelectionRange();
    if (range) {
      setSelectionRange({ start: range.from, end: range.to });
    }

    try {
      // Get position for AI options popup
      const editorElement = richTextEditorRef.current.editor.view.dom;
      const editorBounds = editorElement.getBoundingClientRect();

      // Get the coordinates of the selection
      const { from } = richTextEditorRef.current.editor.state.selection;
      const selectionCoords = richTextEditorRef.current.editor.view.coordsAtPos(from);

      const x = selectionCoords ? selectionCoords.left : editorBounds.left;
      const y = selectionCoords ? selectionCoords.top : editorBounds.top;

      setSelectionPosition({ x, y });
      setShowAIOptions(true);
    } catch (error) {
      // Fall back to a default position if there's an issue getting coordinates
      console.error("Error getting selection position:", error);
      setSelectionPosition({ x: window.innerWidth / 2 - 140, y: window.innerHeight / 2 - 100 });
      setShowAIOptions(true);
    }
  };

  // We're now using the onSelectionChange prop for selection handling

  // Handle AI action for selected text
  const handleAIAction = async (optionId: string) => {
    if (!selectedText) return;
    setShowAIOptions(false);
    const option = researchAIOptions.find(opt => opt.id === optionId);
    if (!option) return;
    const responseId = Date.now().toString();
    setAIResponse({
      id: responseId,
      content: 'Generating response...',
      status: 'loading',
      timestamp: new Date(),
    });
    setShowAIPanel(true);
    try {
      const selectedAIModel = AI_MODELS.find(m => m.id === model) || AI_MODELS[0];
      // Compose system prompt with tone and depth
      const systemPrompt = `You are an academic writing assistant. Use a ${tone} tone and provide a ${depth} response.`;
      const response = await fetch(apiBase, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`,
          'HTTP-Referer': window.location.href,
        },
        body: JSON.stringify({
          model: selectedAIModel.id,
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: `${option.prompt}\n\n${selectedText}` },
          ],
        }),
      });
      const data = await response.json();
      if (data.choices && data.choices[0]?.message?.content) {
        setAIResponse({
          id: responseId,
          content: data.choices[0].message.content,
          status: 'completed',
          timestamp: new Date(),
        });
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      setAIResponse({
        id: responseId,
        content: 'Error generating response. Please try again.',
        status: 'error',
        timestamp: new Date(),
      });
    }
  };

  // Apply AI response to the document
  const applyAIResponse = (placement: 'before' | 'replace' | 'after') => {
    if (!aiResponse || aiResponse.status !== "completed" || !richTextEditorRef.current) return;
    
    // Create revision before applying AI changes
    saveRevision("Auto-save before AI changes");
    
    try {
      // Convert markdown response to HTML and clean up formatting
      const contentToConvert = typeof aiResponse.content === 'string' ? aiResponse.content : String(aiResponse.content);
      // Clean up any stray markdown before conversion
      const cleanedContent = contentToConvert
        .replace(/^#{1,6}\s+/gm, '') // Remove markdown headers
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // Convert bold
        .replace(/\*(.+?)\*/g, '<em>$1</em>') // Convert italic (non-greedy)
        .replace(/^\s*[-*+]\s+/gm, '• ') // Convert bullet points
        .replace(/^\s*\d+\.\s+/gm, (match, offset, string) => {
          const num = match.match(/\d+/)[0];
          return `${num}. `;
        }) // Clean numbered lists
        // Remove any remaining stray asterisks
        .replace(/(?<!\*)\*(?!\*)/g, '')
        .replace(/^\*+\s*/gm, '')
        .replace(/\s*\*+$/gm, '');
      
      const htmlContent = formatConverter.markdownToHtml(cleanedContent);
      
      if (placement === 'before') {
        // Insert before selection
        const beforeRange = richTextEditorRef.current.getSelectionRange();
        if (beforeRange) {
          // Insert content at the start of selection
          richTextEditorRef.current.editor?.chain().focus().insertContentAt(beforeRange.from, htmlContent).run();
        } else {
          richTextEditorRef.current.insertContent(htmlContent);
        }
      } else if (placement === 'replace') {
        // Replace selected text
        richTextEditorRef.current.replaceSelectedText(htmlContent);
      } else if (placement === 'after') {
        // First get current position
        const selectionRange = richTextEditorRef.current.getSelectionRange();
        
        if (selectionRange) {
          // Insert content after selection end
          richTextEditorRef.current.editor?.chain().focus().insertContentAt(selectionRange.to, htmlContent).run();
        } else {
          // If no selection, just insert at current position
          richTextEditorRef.current.insertContent(htmlContent);
        }
      }
      
      toast.success("AI content applied successfully");
    } catch (error) {
      console.error("Error applying AI content:", error);
      toast.error("Failed to apply AI content");
    }
    
    setAIResponse(null);
  };

  // Submit custom prompt to AI
  const submitCustomPrompt = async () => {
    if (!customPrompt.trim()) return;
    
    const responseId = Date.now().toString();
    setAIResponse({
      id: responseId,
      content: "Generating response...",
      status: "loading",
      timestamp: new Date()
    });
    
    try {
      const selectedAIModel = AI_MODELS.find(m => m.id === model) || AI_MODELS[0];
      
      const response = await fetch(apiBase, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${apiKey}`,
          "HTTP-Referer": window.location.href
        },
        body: JSON.stringify({
          model: selectedAIModel.id,
          messages: [
            {
              role: "system",
              content: "You are an academic writing assistant helping with research papers."
            },
            {
              role: "user",
              content: customPrompt
            }
          ]
        })
      });
      
      const data = await response.json();
      
      if (data.choices && data.choices[0]?.message?.content) {
        setAIResponse({
          id: responseId,
          content: data.choices[0].message.content,
          status: "completed",
          timestamp: new Date()
        });
      } else {
        throw new Error("Invalid response format");
      }
      
      setCustomPrompt("");
      setShowCustomPrompt(false);
    } catch (error) {
      console.error("AI API error:", error);
      setAIResponse({
        id: responseId,
        content: "Error generating response. Please try again.",
        status: "error",
        timestamp: new Date()
      });
    }
  };

  // Auto-save to revision history every 5 minutes
  useEffect(() => {
    const autoSaveInterval = setInterval(() => {
      if (content.trim()) {
        saveRevision("Auto-save");
      }
    }, 5 * 60 * 1000); // 5 minutes
    
    return () => clearInterval(autoSaveInterval);
  }, [content]);

  // Handle image upload with improved functionality
  const handleImageUpload = () => {
    // Save current state before inserting image
    saveRevision("Auto-save before inserting image");
    
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.multiple = false;
    
    input.onchange = async (e) => {
      if (!richTextEditorRef.current) {
        toast.error("Editor not available");
        return;
      }
      
      const target = e.target as HTMLInputElement;
      const file = target.files?.[0];
      
      if (file) {
        // Check file size (limit to 10MB)
        if (file.size > 10 * 1024 * 1024) {
          toast.error("Image is too large. Maximum size is 10MB.");
          return;
        }
        
        // Show loading indicator
        const loadingToast = toast.loading("Processing and inserting image...");
        
        try {
          const reader = new FileReader();
          reader.onload = (e) => {
            const imageUrl = e.target?.result as string;
            if (imageUrl) {
              try {
                // Insert the image with the resizable extension
                richTextEditorRef.current?.applyFormat('image', imageUrl);
                
                // Show a helpful message about image alignment
                toast.dismiss(loadingToast);
                toast.success('Image inserted! Select it and use the alignment buttons to position it.', {
                  duration: 5000,
                  action: {
                    label: 'Got it',
                    onClick: () => {},
                  },
                });
              } catch (error) {
                console.error("Error inserting image:", error);
                toast.dismiss(loadingToast);
                toast.error("Failed to insert image. Please try again.");
              }
            }
          };
          
          reader.onerror = () => {
            toast.dismiss(loadingToast);
            toast.error("Failed to read image file. Please try again.");
          };
          
          reader.readAsDataURL(file);
        } catch (error) {
          console.error("Error processing image:", error);
          toast.dismiss(loadingToast);
          toast.error("Failed to process image. Please try again.");
        }
      }
    };
    
    input.click();
  };
  
  // Handle image alignment
  const handleImageAlignment = (alignment: 'left' | 'center' | 'right') => {
    if (!richTextEditorRef.current) {
      toast.error("Editor not available");
      return;
    }
    
    try {
      const formatName = `image-align-${alignment}`;
      richTextEditorRef.current.applyFormat(formatName);
      toast.success(`Image aligned ${alignment}`, { duration: 2000 });
    } catch (error) {
      console.error("Error aligning image:", error);
      toast.error("Failed to align image. Make sure an image is selected.");
    }
  };

  // Table insert dialog state
  const [showTableDialog, setShowTableDialog] = useState(false);
  const [tableRows, setTableRows] = useState(3);
  const [tableCols, setTableCols] = useState(3);

  // Enhanced table insertion with user input
  const handleInsertTable = () => {
    setShowTableDialog(true);
  };

  const confirmInsertTable = () => {
    if (!richTextEditorRef.current) {
      toast.error("Editor not available");
      setShowTableDialog(false);
      return;
    }
    if (tableRows < 1 || tableCols < 1) {
      toast.error("Rows and columns must be at least 1");
      return;
    }
    try {
      saveRevision("Auto-save before inserting table");
      richTextEditorRef.current.applyFormat('table', { rows: tableRows, cols: tableCols });
      toast.success('Table inserted successfully! You can resize columns by dragging the borders.');
    } catch (error) {
      console.error("Error inserting table:", error);
      toast.error("Failed to insert table. Please try again.");
    }
    setShowTableDialog(false);
  };

  // Main component render
  return (
    <div className="flex flex-col h-full">
      {/* Editor header with title and actions */}
      <header className="border-b bg-white sticky top-0 z-10">
        <div className="container flex items-center justify-between p-2">
          <div className="flex items-center gap-2 w-2/5">
            <Input
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="font-medium text-lg border-none focus-visible:ring-0 focus-visible:ring-offset-0 px-2 h-9"
              placeholder="Document Title"
            />
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => saveRevision("Manual save")}
              className="text-muted-foreground"
            >
              <Save className="h-4 w-4 mr-1" /> 
              Save
            </Button>
            
            {/* Hidden file input for document upload */}
            <input 
              type="file" 
              ref={fileInputRef} 
              onChange={handleFileUpload} 
              accept=".docx,.txt" 
              className="hidden" 
            />
            
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => fileInputRef.current?.click()}
              className="text-muted-foreground"
            >
              <FileUp className="h-4 w-4 mr-1" /> 
              Import
            </Button>
            
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => setShowExportDialog(true)}
              className="text-muted-foreground"
            >
              <FileDown className="h-4 w-4 mr-1" /> 
              Export
            </Button>
            
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => setShowRevisionHistory(true)}
              className="text-muted-foreground"
            >
              <History className="h-4 w-4 mr-1" /> 
              History
            </Button>
          </div>

          <div className="flex items-center gap-2">
            <Select value={fontFamily} onValueChange={setFontFamily}>
              <SelectTrigger className="w-[130px] h-8">
                <Type className="h-3.5 w-3.5 mr-1.5" />
                <SelectValue placeholder="Font Family" />
              </SelectTrigger>
              <SelectContent>
                {fontFamilyOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Select value={fontSize} onValueChange={setFontSize}>
              <SelectTrigger className="w-[100px] h-8">
                <SelectValue placeholder="Size" />
              </SelectTrigger>
              <SelectContent>
                {fontSizeOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Button 
              variant={showAIPanel ? "outline" : "default"} 
              size="sm"
              onClick={() => setShowAIPanel(!showAIPanel)} 
              className="ml-2"
            >
              <Sparkles className="h-4 w-4 mr-1.5" />
              AI Assistant
            </Button>
          </div>
        </div>

        {/* Formatting toolbar */}
        <div className="border-t px-2 py-1.5 flex items-center space-x-1">
          <Toggle
            variant="outline"
            size="sm"
            pressed={isBold}
            onPressedChange={() => applyFormatting('bold')}
            className="h-8 w-8 p-0"
            title="Bold"
          >
            <Bold className="h-4 w-4" />
          </Toggle>
          <Toggle
            variant="outline"
            size="sm"
            pressed={isItalic}
            onPressedChange={() => applyFormatting('italic')}
            className="h-8 w-8 p-0"
            title="Italic"
          >
            <Italic className="h-4 w-4" />
          </Toggle>
          <Toggle
            variant="outline"
            size="sm"
            pressed={isUnderline}
            onPressedChange={() => applyFormatting('underline')}
            className="h-8 w-8 p-0"
            title="Underline"
          >
            <Underline className="h-4 w-4" />
          </Toggle>
          
          <div className="h-5 w-px bg-muted mx-1" />
          
          <Toggle 
            variant="outline" 
            size="sm" 
            onClick={() => applyFormatting('h1')}
            className="h-8 w-8 p-0"
          >
            <Heading1 className="h-4 w-4" />
          </Toggle>
          <Toggle 
            variant="outline" 
            size="sm" 
            onClick={() => applyFormatting('h2')}
            className="h-8 w-8 p-0"
          >
            <Heading2 className="h-4 w-4" />
          </Toggle>
          <Toggle 
            variant="outline" 
            size="sm" 
            onClick={() => applyFormatting('h3')}
            className="h-8 w-8 p-0"
          >
            <Heading3 className="h-4 w-4" />
          </Toggle>
          
          <div className="h-5 w-px bg-muted mx-1" />
          
          <Toggle 
            variant="outline" 
            size="sm" 
            onClick={() => applyFormatting('ul')}
            className="h-8 w-8 p-0"
          >
            <List className="h-4 w-4" />
          </Toggle>
          <Toggle 
            variant="outline" 
            size="sm" 
            onClick={() => applyFormatting('ol')}
            className="h-8 w-8 p-0"
          >
            <ListOrdered className="h-4 w-4" />
          </Toggle>
          
          <div className="h-5 w-px bg-muted mx-1" />
          
          <Toggle 
            variant="outline" 
            size="sm" 
            onClick={() => applyFormatting('quote')}
            className="h-8 w-8 p-0"
          >
            <Quote className="h-4 w-4" />
          </Toggle>
          <Toggle 
            variant="outline" 
            size="sm" 
            onClick={() => applyFormatting('code')}
            className="h-8 w-8 p-0"
          >
            <Code className="h-4 w-4" />
          </Toggle>
          <Toggle 
            variant="outline" 
            size="sm" 
            onClick={() => applyFormatting('highlight')}
            className="h-8 w-8 p-0"
          >
            <Highlighter className="h-4 w-4" />
          </Toggle>
          
          <div className="h-5 w-px bg-muted mx-1" />
          
          <Toggle 
            variant="outline" 
            size="sm" 
            onClick={() => applyFormatting('align-left')}
            className="h-8 w-8 p-0"
            title="Align Left"
          >
            <AlignLeft className="h-4 w-4" />
          </Toggle>
          <Toggle 
            variant="outline" 
            size="sm" 
            onClick={() => applyFormatting('align-center')}
            className="h-8 w-8 p-0"
            title="Align Center"
          >
            <AlignCenter className="h-4 w-4" />
          </Toggle>
          <Toggle 
            variant="outline" 
            size="sm" 
            onClick={() => applyFormatting('align-right')}
            className="h-8 w-8 p-0"
            title="Align Right"
          >
            <AlignRight className="h-4 w-4" />
          </Toggle>
          <Toggle 
            variant="outline" 
            size="sm" 
            onClick={() => applyFormatting('align-justify')}
            className="h-8 w-8 p-0"
            title="Justify"
          >
            <AlignJustify className="h-4 w-4" />
          </Toggle>
          
          <div className="h-5 w-px bg-muted mx-1" />
          
          <Toggle 
            variant="outline" 
            size="sm" 
            onClick={handleImageUpload}
            className="h-8 w-8 p-0"
            title="Insert Image"
          >
            <ImageIcon className="h-4 w-4" />
          </Toggle>
          <Toggle 
            variant="outline" 
            size="sm" 
            onClick={handleInsertTable}
            className="h-8 w-8 p-0"
            title="Insert Table"
          >
            <TableIcon className="h-4 w-4" />
          </Toggle>
          
          <div className="h-5 w-px bg-muted mx-1" />
          
          <div className="flex items-center bg-slate-100 rounded-md p-1">
            <span className="text-xs text-slate-500 mr-1 hidden sm:inline">Image:</span>
            <Toggle 
              variant="outline" 
              size="sm" 
              onClick={() => handleImageAlignment('left')}
              className="h-7 w-7 p-0"
              title="Align Image Left"
            >
              <AlignLeft className="h-3.5 w-3.5" />
            </Toggle>
            <Toggle 
              variant="outline" 
              size="sm" 
              onClick={() => handleImageAlignment('center')}
              className="h-7 w-7 p-0"
              title="Align Image Center"
            >
              <AlignCenter className="h-3.5 w-3.5" />
            </Toggle>
            <Toggle 
              variant="outline" 
              size="sm" 
              onClick={() => handleImageAlignment('right')}
              className="h-7 w-7 p-0"
              title="Align Image Right"
            >
              <AlignRight className="h-3.5 w-3.5" />
            </Toggle>
          </div>
        </div>
      </header>
      
      {/* Main content area with editor and AI panel */}
      <div className="flex flex-1 overflow-hidden bg-gray-50">
        {/* Main editor area */}
        <div className={cn(
          "flex-1 overflow-auto",
          showAIPanel ? "w-2/3" : "w-full" 
        )}>
          <div className="max-w-4xl mx-auto p-6">
            <RichTextEditor
              ref={richTextEditorRef}
              content={content}
              onChange={setContent}
              onSelectionChange={handleTextSelection}
              className="w-full"
            />
          </div>
        </div>
        
        {/* AI Assistant Panel */}
        {showAIPanel && (
          <div className="border-l w-1/3 flex flex-col">
            <div className="p-3 border-b bg-muted/20 flex justify-between items-center">
              <div className="font-medium flex items-center">
                <Sparkles className="h-4 w-4 mr-1.5 text-blue-500" />
                AI Writing Assistant
              </div>
              <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => setShowAIPanel(false)}>
                <X className="h-4 w-4" />
              </Button>
            </div>
            <div className="p-3 flex gap-2">
              <Select value={model} onValueChange={setModel}>
                <SelectTrigger className="w-[160px]">
                  <SelectValue placeholder="Select AI model" />
                </SelectTrigger>
                <SelectContent>
                  {AI_MODELS.map(model => (
                    <SelectItem key={model.id} value={model.id}>{model.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={tone} onValueChange={setTone}>
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="Tone" />
                </SelectTrigger>
                <SelectContent>
                  {toneOptions.map(option => (
                    <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={depth} onValueChange={setDepth}>
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="Depth" />
                </SelectTrigger>
                <SelectContent>
                  {depthOptions.map(option => (
                    <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <ScrollArea className="flex-1 p-3">
              <div className="space-y-6">
                <div>
                  <h3 className="text-sm font-semibold mb-2">Editing Tools</h3>
                  <div className="grid grid-cols-2 gap-2">
                    {researchAIOptions.filter(opt => [
                      'improve-clarity', 'expand-point', 'suggest-citations', 'rephrase-academic', 'paraphrase', 'critique-argument', 'summarize', 'logical-fallacies'
                    ].includes(opt.id)).map(option => (
                      <Button key={option.id} variant="outline" size="sm" className="justify-start text-xs h-auto py-1.5" title={option.description} onClick={() => { setSelectedAIOption(option.id); handleAIAction(option.id); }}>
                        <span className={`h-3.5 w-3.5 mr-1.5 text-${option.color}-500`}>{option.icon}</span>
                        {option.name}
                      </Button>
                    ))}
                  </div>
                </div>
                <div>
                  <h3 className="text-sm font-semibold mb-2">Section Generation</h3>
                  <div className="grid grid-cols-2 gap-2">
                    {researchAIOptions.filter(opt => [
                      'introduction', 'literature-review', 'methodology', 'results', 'discussion', 'conclusion', 'abstract'
                    ].includes(opt.id)).map(option => (
                      <Button key={option.id} variant="outline" size="sm" className="justify-start text-xs h-auto py-1.5" title={option.description} onClick={() => { setSelectedAIOption(option.id); handleAIAction(option.id); }}>
                        <span className={`h-3.5 w-3.5 mr-1.5 text-${option.color}-500`}>{option.icon}</span>
                        {option.name}
                      </Button>
                    ))}
                  </div>
                </div>
                <div>
                  <h3 className="text-sm font-semibold mb-2">Research Assistance</h3>
                  <div className="grid grid-cols-2 gap-2">
                    {researchAIOptions.filter(opt => [
                      'generate-questions', 'related-work', 'suggest-figures', 'generate-title'
                    ].includes(opt.id)).map(option => (
                      <Button key={option.id} variant="outline" size="sm" className="justify-start text-xs h-auto py-1.5" title={option.description} onClick={() => { setSelectedAIOption(option.id); handleAIAction(option.id); }}>
                        <span className={`h-3.5 w-3.5 mr-1.5 text-${option.color}-500`}>{option.icon}</span>
                        {option.name}
                      </Button>
                    ))}
                  </div>
                </div>
                <div>
                  <Button variant="outline" className="w-full text-sm justify-start" onClick={() => setShowCustomPrompt(true)}>
                    <Wand2 className="h-4 w-4 mr-1.5" /> Custom AI Prompt
                  </Button>
                </div>
              </div>
              
              {/* Custom prompt input */}
              {showCustomPrompt ? (
                <div className="mb-4">
                  <div className="flex items-start mb-2">
                    <Textarea
                      value={customPrompt}
                      onChange={(e) => setCustomPrompt(e.target.value)}
                      placeholder="Enter your custom prompt..."
                      className="text-sm flex-1 min-h-[80px]"
                    />
                  </div>
                  <div className="flex justify-end gap-2">
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={() => setShowCustomPrompt(false)}
                    >
                      Cancel
                    </Button>
                    <Button 
                      variant="default" 
                      size="sm" 
                      onClick={submitCustomPrompt}
                    >
                      <Send className="h-3.5 w-3.5 mr-1.5" />
                      Generate
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="mb-5">
                  <Button 
                    variant="outline" 
                    className="w-full text-sm justify-start"
                    onClick={() => setShowCustomPrompt(true)}
                  >
                    <Wand2 className="h-4 w-4 mr-1.5" />
                    Custom AI Prompt
                  </Button>
                </div>
              )}
              
              {/* AI Response */}
              {aiResponse && (
                <div className="space-y-3">
                  <div className="bg-white border rounded-lg p-4 text-sm">
                    <div className="font-medium mb-2 text-gray-700 flex items-center">
                      <Brain className="h-4 w-4 mr-1.5 text-blue-600" />
                      AI Response
                    </div>
                    <div 
                      className="prose prose-sm max-w-none"
                      dangerouslySetInnerHTML={{
                        __html: (() => {
                          // Clean and format AI response content
                          const content = typeof aiResponse.content === 'string' ? aiResponse.content : String(aiResponse.content);
                          // Remove markdown syntax and convert to proper HTML
                          const cleanedContent = content
                            .replace(/^#{1,6}\s+(.+)$/gm, '<h3>$1</h3>') // Convert headers to h3
                            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // Convert bold
                            .replace(/\*(.*?)\*/g, '<em>$1</em>') // Convert italic
                            .replace(/^\s*[-*+]\s+(.+)$/gm, '<li>$1</li>') // Convert bullet points
                            .replace(/(<li>.*<\/li>)/gs, '<ul>$1</ul>') // Wrap lists
                            .replace(/^\s*(\d+)\.\s+(.+)$/gm, '<li>$2</li>') // Convert numbered lists
                            .replace(/\n\n/g, '</p><p>') // Convert paragraphs
                            .replace(/^(?!<[hul])/gm, '<p>') // Add opening p tags
                            .replace(/(?<!>)$/gm, '</p>'); // Add closing p tags
                          
                          return formatConverter.markdownToHtml(content);
                        })()
                      }}
                    />
                  </div>
                  
                  <div className="flex justify-between">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => setAIResponse(null)}
                    >
                      <X className="h-3.5 w-3.5 mr-1" />
                      Dismiss
                    </Button>
                    
                    <div className="flex gap-1">
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={() => applyAIResponse('before')}
                        className="text-xs"
                      >
                        <PlusCircle className="h-3.5 w-3.5 mr-1" />
                        Insert Before
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={() => applyAIResponse('replace')}
                        className="text-xs"
                      >
                        <Replace className="h-3.5 w-3.5 mr-1" />
                        Replace
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={() => applyAIResponse('after')}
                        className="text-xs"
                      >
                        <ArrowRight className="h-3.5 w-3.5 mr-1" />
                        Insert After
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </ScrollArea>
          </div>
        )}
      </div>

      {/* AI options popup */}
      {showAIOptions && (
        <div className="fixed bg-white rounded-xl shadow-2xl border p-4 w-[340px] z-50 flex flex-col gap-3 animate-fade-in" style={{ top: `${selectionPosition.y + 20}px`, left: `${selectionPosition.x}px` }}>
          <div className="font-semibold text-base text-gray-800 mb-1 flex items-center gap-2">
            <Sparkles className="h-4 w-4 text-blue-500" /> AI Tools
          </div>
          <div className="grid grid-cols-2 gap-2">
            {researchAIOptions.filter(opt => [
              'improve-clarity', 'expand-point', 'suggest-citations', 'rephrase-academic', 'paraphrase', 'critique-argument', 'summarize', 'logical-fallacies'
            ].includes(opt.id)).map(opt => (
              <button key={opt.id} className={`flex items-center gap-2 px-2 py-1 rounded-md border hover:bg-gray-100 transition text-xs font-medium border-gray-200 shadow-sm group`} title={opt.description} onClick={() => handleAIAction(opt.id)}>
                <span className={`text-${opt.color}-600`}>{opt.icon}</span> {opt.name}
              </button>
            ))}
          </div>
          <div className="font-semibold text-xs text-gray-500 mt-2 mb-1">Section Generation</div>
          <div className="grid grid-cols-2 gap-2">
            {researchAIOptions.filter(opt => [
              'introduction', 'literature-review', 'methodology', 'results', 'discussion', 'conclusion', 'abstract'
            ].includes(opt.id)).map(opt => (
              <button key={opt.id} className={`flex items-center gap-2 px-2 py-1 rounded-md border hover:bg-gray-100 transition text-xs font-medium border-gray-200 shadow-sm group`} title={opt.description} onClick={() => handleAIAction(opt.id)}>
                <span className={`text-${opt.color}-600`}>{opt.icon}</span> {opt.name}
              </button>
            ))}
          </div>
          <div className="font-semibold text-xs text-gray-500 mt-2 mb-1">Research Assistance</div>
          <div className="grid grid-cols-2 gap-2">
            {researchAIOptions.filter(opt => [
              'generate-questions', 'related-work', 'suggest-figures', 'generate-title'
            ].includes(opt.id)).map(opt => (
              <button key={opt.id} className={`flex items-center gap-2 px-2 py-1 rounded-md border hover:bg-gray-100 transition text-xs font-medium border-gray-200 shadow-sm group`} title={opt.description} onClick={() => handleAIAction(opt.id)}>
                <span className={`text-${opt.color}-600`}>{opt.icon}</span> {opt.name}
              </button>
            ))}
          </div>
          <div className="mt-2 flex items-center justify-between">
            <button className="flex items-center gap-1 px-2 py-1 rounded border border-gray-300 text-xs hover:bg-gray-100 transition" onClick={() => setShowCustomPrompt(true)} title="Write your own custom AI prompt for advanced control">
              <Wand2 className="h-4 w-4 text-indigo-500" /> Custom AI Prompt
            </button>
            <button className="text-xs text-gray-400 hover:text-gray-600 px-2 py-1" onClick={() => setShowAIOptions(false)}>
              Close
            </button>
          </div>
        </div>
      )}
      
      {/* Export dialog */}
      <Dialog open={showExportDialog} onOpenChange={setShowExportDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Export Document</DialogTitle>
            <DialogDescription>
              Choose a format to export your document
            </DialogDescription>
          </DialogHeader>
          
          <Tabs defaultValue="docx" onValueChange={value => setExportFormat(value)}>
            <TabsList className="grid grid-cols-2">
              <TabsTrigger value="docx">Word Document</TabsTrigger>
              <TabsTrigger value="pdf">PDF</TabsTrigger>
            </TabsList>
            <TabsContent value="docx" className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">File name</label>
                <Input
                  value={docxFileName}
                  onChange={(e) => setDocxFileName(e.target.value)}
                  placeholder={`${title}.docx`}
                />
              </div>
              <Button onClick={exportToDocx} className="w-full">
                <Download className="h-4 w-4 mr-1.5" />
                Export to Word
              </Button>
            </TabsContent>
            <TabsContent value="pdf" className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">File name</label>
                <Input
                  value={pdfFileName}
                  onChange={(e) => setPdfFileName(e.target.value)}
                  placeholder={`${title}.pdf`}
                />
              </div>
              <Button onClick={exportToPdf} className="w-full">
                <Download className="h-4 w-4 mr-1.5" />
                Export to PDF
              </Button>
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>
      
      {/* Revision history dialog */}
      <Dialog open={showRevisionHistory} onOpenChange={setShowRevisionHistory}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Revision History</DialogTitle>
            <DialogDescription>
              View and restore previous versions of your document
            </DialogDescription>
          </DialogHeader>
          
          <div className="max-h-[60vh] overflow-y-auto">
            {revisionHistory.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Clock className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>No revision history yet</p>
                <p className="text-sm">Save your document to create revision points</p>
              </div>
            ) : (
              <div className="space-y-2">
                {revisionHistory.map((revision) => (
                  <Card key={revision.id} className="p-3">
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="font-medium">{revision.title}</h4>
                        <p className="text-sm text-muted-foreground">
                          {revision.timestamp.toLocaleString()} - {revision.description}
                        </p>
                      </div>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => revertToRevision(revision.id)}
                      >
                        <Undo className="h-3.5 w-3.5 mr-1.5" />
                        Restore
                      </Button>
                    </div>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Table insert dialog */}
      <Dialog open={showTableDialog} onOpenChange={setShowTableDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Insert Table</DialogTitle>
            <DialogDescription>Choose the number of rows and columns</DialogDescription>
          </DialogHeader>
          <div className="flex gap-4 items-center my-4">
            <div>
              <label className="block text-sm font-medium mb-1">Rows</label>
              <Input type="number" min={1} max={20} value={tableRows} onChange={e => setTableRows(Number(e.target.value))} />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Columns</label>
              <Input type="number" min={1} max={20} value={tableCols} onChange={e => setTableCols(Number(e.target.value))} />
            </div>
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="ghost" onClick={() => setShowTableDialog(false)}>Cancel</Button>
            <Button variant="default" onClick={confirmInsertTable}>Insert Table</Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
