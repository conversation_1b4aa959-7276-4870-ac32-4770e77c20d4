import { ChartType } from '@antv/gpt-vis';

export interface DataColumn {
  name: string;
  type: 'numeric' | 'categorical' | 'temporal' | 'text';
  uniqueValues: number;
  nullCount: number;
  sampleValues: any[];
}

export interface ChartRecommendation {
  chartType: ChartType;
  confidence: number;
  reasoning: string;
  config: any;
  title: string;
  description: string;
}

export interface DataAnalysis {
  columns: DataColumn[];
  rowCount: number;
  hasNumericData: boolean;
  hasCategoricalData: boolean;
  hasTemporalData: boolean;
  hasTextData: boolean;
}

export class ChartRecommendationService {
  /**
   * Analyze dataset structure and characteristics
   */
  static analyzeData(data: any[]): DataAnalysis {
    if (!data || data.length === 0) {
      throw new Error('Dataset is empty');
    }

    const columns: DataColumn[] = [];
    const firstRow = data[0];
    
    Object.keys(firstRow).forEach(columnName => {
      const values = data.map(row => row[columnName]).filter(val => val != null);
      const uniqueValues = new Set(values).size;
      const nullCount = data.length - values.length;
      
      // Determine column type
      let type: DataColumn['type'] = 'text';
      
      if (values.length > 0) {
        const sampleValue = values[0];
        
        // Check if numeric
        if (values.every(val => !isNaN(Number(val)) && isFinite(Number(val)))) {
          type = 'numeric';
        }
        // Check if temporal
        else if (values.every(val => !isNaN(Date.parse(val)))) {
          type = 'temporal';
        }
        // Check if categorical (limited unique values)
        else if (uniqueValues <= Math.min(20, data.length * 0.5)) {
          type = 'categorical';
        }
      }
      
      columns.push({
        name: columnName,
        type,
        uniqueValues,
        nullCount,
        sampleValues: values.slice(0, 5)
      });
    });

    return {
      columns,
      rowCount: data.length,
      hasNumericData: columns.some(col => col.type === 'numeric'),
      hasCategoricalData: columns.some(col => col.type === 'categorical'),
      hasTemporalData: columns.some(col => col.type === 'temporal'),
      hasTextData: columns.some(col => col.type === 'text')
    };
  }

  /**
   * Get chart recommendations based on data analysis
   */
  static getRecommendations(analysis: DataAnalysis): ChartRecommendation[] {
    const recommendations: ChartRecommendation[] = [];
    const { columns, rowCount } = analysis;
    
    const numericColumns = columns.filter(col => col.type === 'numeric');
    const categoricalColumns = columns.filter(col => col.type === 'categorical');
    const temporalColumns = columns.filter(col => col.type === 'temporal');
    const textColumns = columns.filter(col => col.type === 'text');

    // Line Chart - for time series or continuous data
    if (temporalColumns.length >= 1 && numericColumns.length >= 1) {
      recommendations.push({
        chartType: ChartType.Line,
        confidence: 0.9,
        reasoning: 'Time series data detected with numeric values - ideal for line charts',
        config: {
          xField: temporalColumns[0].name,
          yField: numericColumns[0].name,
          smooth: true
        },
        title: `${numericColumns[0].name} over ${temporalColumns[0].name}`,
        description: 'Shows trends and patterns over time'
      });
    }

    // Column Chart - for categorical comparisons
    if (categoricalColumns.length >= 1 && numericColumns.length >= 1) {
      recommendations.push({
        chartType: ChartType.Column,
        confidence: 0.85,
        reasoning: 'Categorical data with numeric values - perfect for column charts',
        config: {
          xField: categoricalColumns[0].name,
          yField: numericColumns[0].name,
          colorField: categoricalColumns.length > 1 ? categoricalColumns[1].name : undefined
        },
        title: `${numericColumns[0].name} by ${categoricalColumns[0].name}`,
        description: 'Compares values across different categories'
      });
    }

    // Pie Chart - for categorical proportions
    if (categoricalColumns.length >= 1 && numericColumns.length >= 1 && 
        categoricalColumns[0].uniqueValues <= 10) {
      recommendations.push({
        chartType: ChartType.Pie,
        confidence: 0.8,
        reasoning: 'Limited categories with numeric values - suitable for pie chart',
        config: {
          angleField: numericColumns[0].name,
          colorField: categoricalColumns[0].name
        },
        title: `Distribution of ${numericColumns[0].name}`,
        description: 'Shows proportional relationships between categories'
      });
    }

    // Scatter Plot - for correlation analysis
    if (numericColumns.length >= 2) {
      recommendations.push({
        chartType: ChartType.Scatter,
        confidence: 0.75,
        reasoning: 'Multiple numeric columns - good for correlation analysis',
        config: {
          xField: numericColumns[0].name,
          yField: numericColumns[1].name,
          colorField: categoricalColumns.length > 0 ? categoricalColumns[0].name : undefined,
          sizeField: numericColumns.length > 2 ? numericColumns[2].name : undefined
        },
        title: `${numericColumns[1].name} vs ${numericColumns[0].name}`,
        description: 'Reveals correlations and patterns between variables'
      });
    }

    // Bar Chart - alternative to column chart
    if (categoricalColumns.length >= 1 && numericColumns.length >= 1) {
      recommendations.push({
        chartType: ChartType.Bar,
        confidence: 0.7,
        reasoning: 'Categorical data with numeric values - horizontal bar chart alternative',
        config: {
          xField: numericColumns[0].name,
          yField: categoricalColumns[0].name,
          colorField: categoricalColumns.length > 1 ? categoricalColumns[1].name : undefined
        },
        title: `${numericColumns[0].name} by ${categoricalColumns[0].name}`,
        description: 'Horizontal comparison of values across categories'
      });
    }

    // Area Chart - for cumulative or stacked data
    if (temporalColumns.length >= 1 && numericColumns.length >= 1) {
      recommendations.push({
        chartType: ChartType.Area,
        confidence: 0.65,
        reasoning: 'Time series data - area chart shows cumulative trends',
        config: {
          xField: temporalColumns[0].name,
          yField: numericColumns[0].name,
          colorField: categoricalColumns.length > 0 ? categoricalColumns[0].name : undefined
        },
        title: `${numericColumns[0].name} Area over ${temporalColumns[0].name}`,
        description: 'Shows cumulative values and trends over time'
      });
    }

    // Histogram - for distribution analysis
    if (numericColumns.length >= 1) {
      recommendations.push({
        chartType: ChartType.Histogram,
        confidence: 0.6,
        reasoning: 'Numeric data available - histogram shows distribution',
        config: {
          binField: numericColumns[0].name,
          binWidth: undefined // Auto-calculate
        },
        title: `Distribution of ${numericColumns[0].name}`,
        description: 'Shows the frequency distribution of values'
      });
    }

    // Word Cloud - for text analysis
    if (textColumns.length >= 1) {
      recommendations.push({
        chartType: ChartType.WordCloud,
        confidence: 0.7,
        reasoning: 'Text data detected - word cloud visualizes text frequency',
        config: {
          wordField: textColumns[0].name,
          weightField: numericColumns.length > 0 ? numericColumns[0].name : undefined
        },
        title: `Word Cloud of ${textColumns[0].name}`,
        description: 'Visualizes text frequency and importance'
      });
    }

    // Treemap - for hierarchical data
    if (categoricalColumns.length >= 1 && numericColumns.length >= 1) {
      recommendations.push({
        chartType: ChartType.Treemap,
        confidence: 0.55,
        reasoning: 'Categorical and numeric data - treemap shows hierarchical proportions',
        config: {
          valueField: numericColumns[0].name,
          colorField: categoricalColumns[0].name
        },
        title: `Treemap of ${numericColumns[0].name} by ${categoricalColumns[0].name}`,
        description: 'Shows hierarchical data as nested rectangles'
      });
    }

    // Radar Chart - for multi-dimensional comparison
    if (numericColumns.length >= 3 && categoricalColumns.length >= 1) {
      recommendations.push({
        chartType: ChartType.Radar,
        confidence: 0.5,
        reasoning: 'Multiple numeric dimensions - radar chart for multi-variate analysis',
        config: {
          xField: categoricalColumns[0].name,
          yField: numericColumns.slice(0, 6).map(col => col.name) // Max 6 dimensions
        },
        title: `Multi-dimensional Analysis`,
        description: 'Compares multiple metrics across categories'
      });
    }

    // Sort by confidence and return top recommendations
    return recommendations
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, 5);
  }

  /**
   * Get the best chart recommendation
   */
  static getBestRecommendation(data: any[]): ChartRecommendation | null {
    try {
      const analysis = this.analyzeData(data);
      const recommendations = this.getRecommendations(analysis);
      return recommendations.length > 0 ? recommendations[0] : null;
    } catch (error) {
      console.error('Error getting chart recommendation:', error);
      return null;
    }
  }

  /**
   * Get chart recommendations for a specific query intent
   */
  static getRecommendationsForQuery(data: any[], query: string): ChartRecommendation[] {
    const analysis = this.analyzeData(data);
    const allRecommendations = this.getRecommendations(analysis);
    
    // Simple keyword-based filtering
    const queryLower = query.toLowerCase();
    
    if (queryLower.includes('trend') || queryLower.includes('time') || queryLower.includes('over time')) {
      return allRecommendations.filter(rec => 
        rec.chartType === ChartType.Line || rec.chartType === ChartType.Area
      );
    }
    
    if (queryLower.includes('compare') || queryLower.includes('comparison')) {
      return allRecommendations.filter(rec => 
        rec.chartType === ChartType.Column || rec.chartType === ChartType.Bar
      );
    }
    
    if (queryLower.includes('distribution') || queryLower.includes('spread')) {
      return allRecommendations.filter(rec => 
        rec.chartType === ChartType.Histogram || rec.chartType === ChartType.Scatter
      );
    }
    
    if (queryLower.includes('proportion') || queryLower.includes('percentage') || queryLower.includes('share')) {
      return allRecommendations.filter(rec => 
        rec.chartType === ChartType.Pie || rec.chartType === ChartType.Treemap
      );
    }
    
    if (queryLower.includes('correlation') || queryLower.includes('relationship')) {
      return allRecommendations.filter(rec => 
        rec.chartType === ChartType.Scatter
      );
    }
    
    return allRecommendations;
  }
}
