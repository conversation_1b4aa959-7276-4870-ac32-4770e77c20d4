import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import {
  Database,
  BarChart3,
  MessageSquare,
  Brain,
  Upload,
  FileText,
  TrendingUp,
  Lightbulb,
  Eye,
  Settings,
  RefreshCw,
  Download,
  Sparkles,
  CheckCircle,
  AlertTriangle,
  Clock,
  Zap,
  Target,
  Users,
  Calendar,
  Globe
} from "lucide-react";

import { FileUploader } from './FileUploader';
import { DataDescriptionForm } from './DataDescriptionForm';
import { DataPreview } from './DataPreview';
import { EnhancedAnalysisPipeline } from './EnhancedAnalysisPipeline';
import { EnhancedVisualizationGallery } from './EnhancedVisualizationGallery';
import { EnhancedQueryInterface } from './EnhancedQueryInterface';
import { GeminiApiTest } from './GeminiApiTest';
import { DataVisualizationErrorBoundary } from './ErrorBoundary';
import { useDataVisualizationStore } from '../stores/data-visualization.store';
import { UploadedFile, DataAnalysisResult } from '../types';

const EnhancedDataVisualizationPlatformContent: React.FC = () => {
  const {
    uploadedFiles,
    currentFile,
    currentAnalysis,
    activeTab,
    setActiveTab,
    setCurrentFile,
    isUploading,
    isAnalyzing,
    isQuerying,
    errors,
    clearErrors
  } = useDataVisualizationStore();

  const [stats, setStats] = useState({
    filesProcessed: 0,
    visualizationsCreated: 0,
    queriesAnswered: 0,
    insightsGenerated: 0
  });

  const [showApiTest, setShowApiTest] = useState(!import.meta.env.VITE_GEMINI_API_KEY);
  const [dataDescription, setDataDescription] = useState<string>('');

  useEffect(() => {
    setStats({
      filesProcessed: uploadedFiles.filter(f => f.status === 'ready').length,
      visualizationsCreated: currentAnalysis?.visualizations.length || 0,
      queriesAnswered: 0,
      insightsGenerated: currentAnalysis?.insights.keyFindings.length || 0
    });
  }, [uploadedFiles, currentAnalysis]);

  const handleFileProcessed = (fileId: string) => {
    console.log('Enhanced: File processed callback received:', fileId);
    console.log('Enhanced: Current uploaded files:', uploadedFiles);
    // Ensure uploadedFiles is an array before calling find
    const files = Array.isArray(uploadedFiles) ? uploadedFiles : [];
    const file = files.find(f => f.id === fileId);
    console.log('Enhanced: Found file:', file);
    if (file) {
      setCurrentFile(file);
      setActiveTab('preview');
      toast.success('File uploaded successfully! You can now preview your data.');
    } else {
      console.error('Enhanced: File not found in uploaded files');
    }
  };

  const handleAnalysisComplete = (result: DataAnalysisResult) => {
    setActiveTab('visualizations');
    toast.success('Analysis complete! Explore your beautiful visualizations.');
  };

  const getTabIcon = (tab: string) => {
    switch (tab) {
      case 'upload':
        return <Upload className="h-4 w-4" />;
      case 'preview':
        return <Eye className="h-4 w-4" />;
      case 'describe':
        return <FileText className="h-4 w-4" />;
      case 'analysis':
        return <Brain className="h-4 w-4" />;
      case 'visualizations':
        return <BarChart3 className="h-4 w-4" />;
      case 'query':
        return <MessageSquare className="h-4 w-4" />;
      default:
        return <Database className="h-4 w-4" />;
    }
  };

  const getTabStatus = (tab: string) => {
    switch (tab) {
      case 'upload':
        return uploadedFiles.length > 0 ? 'completed' : 'pending';
      case 'preview':
        return currentFile ? 'available' : 'disabled';
      case 'describe':
        return currentFile ? 'available' : 'disabled';
      case 'analysis':
        return currentAnalysis ? 'completed' : currentFile ? 'available' : 'disabled';
      case 'visualizations':
        return currentAnalysis?.visualizations.length ? 'completed' : 'disabled';
      case 'query':
        return currentFile ? 'available' : 'disabled';
      default:
        return 'pending';
    }
  };

  const isTabDisabled = (tab: string) => {
    // Always allow tab navigation to prevent navigation issues
    // Show appropriate empty states in tab content instead
    return false;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      {/* Enhanced Header */}
      <div className="sticky top-0 z-40 bg-white/90 backdrop-blur-xl border-b border-gray-200/50 shadow-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-20">
            <div className="flex items-center gap-6">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl blur opacity-20"></div>
                <div className="relative bg-gradient-to-r from-blue-600 to-purple-600 p-3 rounded-xl">
                  <Database className={`h-8 w-8 text-white ${(isUploading || isAnalyzing || isQuerying) ? 'animate-pulse' : ''}`} />
                </div>
                {(isUploading || isAnalyzing || isQuerying) && (
                  <div className="absolute -top-1 -right-1 h-4 w-4 bg-green-500 rounded-full animate-ping" />
                )}
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                  Data Visualization & Analysis
                </h1>
                <p className="text-sm text-gray-600 mt-1">
                  Transform your data into beautiful insights with AI-powered analysis
                </p>
              </div>
            </div>

            {/* Enhanced Stats */}
            <div className="hidden lg:flex items-center gap-8">
              <div className="flex items-center gap-6">
                <div className="text-center">
                  <div className="flex items-center gap-2 mb-1">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <p className="text-2xl font-bold text-blue-600">{stats.filesProcessed}</p>
                  </div>
                  <p className="text-xs text-gray-500 font-medium">Files Processed</p>
                </div>
                <div className="text-center">
                  <div className="flex items-center gap-2 mb-1">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <p className="text-2xl font-bold text-green-600">{stats.visualizationsCreated}</p>
                  </div>
                  <p className="text-xs text-gray-500 font-medium">Visualizations</p>
                </div>
                <div className="text-center">
                  <div className="flex items-center gap-2 mb-1">
                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                    <p className="text-2xl font-bold text-purple-600">{stats.insightsGenerated}</p>
                  </div>
                  <p className="text-xs text-gray-500 font-medium">AI Insights</p>
                </div>
              </div>

              <Separator orientation="vertical" className="h-12" />

              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowApiTest(!showApiTest)}
                className="flex items-center gap-2 hover:bg-gray-50"
              >
                <Settings className="h-4 w-4" />
                {showApiTest ? 'Hide' : 'Show'} API Test
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {errors.length > 0 && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-4">
          <Alert variant="destructive" className="border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-1">
                {errors.map((error, index) => (
                  <div key={index}>{error}</div>
                ))}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={clearErrors}
                className="mt-2"
              >
                Dismiss
              </Button>
            </AlertDescription>
          </Alert>
        </div>
      )}

      {/* API Test Panel */}
      {showApiTest && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-4">
          <GeminiApiTest />
        </div>
      )}

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs value={activeTab} onValueChange={(value: any) => setActiveTab(value)} className="space-y-8">
          {/* Enhanced Tab Navigation */}
          <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-sm">
            <CardContent className="p-6">
              <TabsList className="grid w-full grid-cols-6 h-14 bg-gray-100/50 rounded-xl p-1">
                <TabsTrigger 
                  value="upload" 
                  disabled={isTabDisabled('upload')}
                  className="flex flex-col items-center gap-1 h-12 data-[state=active]:bg-white data-[state=active]:shadow-sm"
                >
                  <div className="flex items-center gap-2">
                    {getTabIcon('upload')}
                    <span className="hidden sm:inline text-sm font-medium">Upload</span>
                  </div>
                  {getTabStatus('upload') === 'completed' && (
                    <CheckCircle className="h-3 w-3 text-green-500" />
                  )}
                </TabsTrigger>
                
                <TabsTrigger 
                  value="preview" 
                  disabled={isTabDisabled('preview')}
                  className="flex flex-col items-center gap-1 h-12 data-[state=active]:bg-white data-[state=active]:shadow-sm"
                >
                  <div className="flex items-center gap-2">
                    {getTabIcon('preview')}
                    <span className="hidden sm:inline text-sm font-medium">Preview</span>
                  </div>
                  {getTabStatus('preview') === 'available' && (
                    <div className="w-2 h-2 bg-blue-500 rounded-full" />
                  )}
                </TabsTrigger>

                <TabsTrigger 
                  value="describe" 
                  disabled={isTabDisabled('describe')}
                  className="flex flex-col items-center gap-1 h-12 data-[state=active]:bg-white data-[state=active]:shadow-sm"
                >
                  <div className="flex items-center gap-2">
                    {getTabIcon('describe')}
                    <span className="hidden sm:inline text-sm font-medium">Describe</span>
                  </div>
                  {(dataDescription || '') && (
                    <CheckCircle className="h-3 w-3 text-green-500" />
                  )}
                </TabsTrigger>
                
                <TabsTrigger 
                  value="analysis" 
                  disabled={isTabDisabled('analysis')}
                  className="flex flex-col items-center gap-1 h-12 data-[state=active]:bg-white data-[state=active]:shadow-sm"
                >
                  <div className="flex items-center gap-2">
                    {getTabIcon('analysis')}
                    <span className="hidden sm:inline text-sm font-medium">Analysis</span>
                  </div>
                  {isAnalyzing && <Clock className="h-3 w-3 text-blue-500 animate-spin" />}
                  {getTabStatus('analysis') === 'completed' && (
                    <CheckCircle className="h-3 w-3 text-green-500" />
                  )}
                </TabsTrigger>
                
                <TabsTrigger 
                  value="visualizations" 
                  disabled={isTabDisabled('visualizations')}
                  className="flex flex-col items-center gap-1 h-12 data-[state=active]:bg-white data-[state=active]:shadow-sm"
                >
                  <div className="flex items-center gap-2">
                    {getTabIcon('visualizations')}
                    <span className="hidden sm:inline text-sm font-medium">Charts</span>
                  </div>
                  {getTabStatus('visualizations') === 'completed' && (
                    <CheckCircle className="h-3 w-3 text-green-500" />
                  )}
                </TabsTrigger>
                
                <TabsTrigger 
                  value="query" 
                  disabled={isTabDisabled('query')}
                  className="flex flex-col items-center gap-1 h-12 data-[state=active]:bg-white data-[state=active]:shadow-sm"
                >
                  <div className="flex items-center gap-2">
                    {getTabIcon('query')}
                    <span className="hidden sm:inline text-sm font-medium">Ask AI</span>
                  </div>
                  {isQuerying && <Clock className="h-3 w-3 text-blue-500 animate-spin" />}
                </TabsTrigger>
              </TabsList>

              {/* Current File Info */}
              {currentFile && (
                <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200/50">
                  <div className="flex items-center gap-4">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <FileText className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-blue-900">{currentFile.name}</h3>
                      <p className="text-sm text-blue-700">
                        {currentFile.data?.length || 0} rows × {currentFile.headers?.length || 0} columns
                      </p>
                    </div>
                    <Badge 
                      variant="outline" 
                      className={`${
                        currentFile.status === 'ready' ? 'bg-green-50 text-green-700 border-green-200' :
                        currentFile.status === 'processing' ? 'bg-blue-50 text-blue-700 border-blue-200' :
                        'bg-red-50 text-red-700 border-red-200'
                      }`}
                    >
                      {currentFile.status}
                    </Badge>
                  </div>
                </div>
              )}

              {/* Debug Info */}
              <div className="mt-4 p-3 bg-gray-100 rounded-lg text-xs">
                <p><strong>Enhanced Debug:</strong></p>
                <p>Uploaded Files: {uploadedFiles.length}</p>
                <p>Current File: {currentFile ? currentFile.name : 'None'}</p>
                <p>Active Tab: {activeTab}</p>
                <p>Current Analysis: {currentAnalysis ? 'Available' : 'None'}</p>
                <p>All Tabs Enabled: Yes (Navigation always allowed)</p>
              </div>
            </CardContent>
          </Card>

          {/* Tab Content */}
          <TabsContent value="upload" className="space-y-6">
            <FileUploader onFileProcessed={handleFileProcessed} />
          </TabsContent>

          <TabsContent value="preview" className="space-y-6">
            {currentFile ? (
              <DataPreview file={currentFile} />
            ) : (
              <Card className="border-dashed border-2 border-gray-300">
                <CardContent className="flex items-center justify-center h-64">
                  <div className="text-center space-y-3">
                    <Eye className="h-12 w-12 text-gray-400 mx-auto" />
                    <div>
                      <h3 className="font-medium text-gray-900">No File Selected</h3>
                      <p className="text-sm text-gray-500 mt-1">Upload a data file to preview</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="describe" className="space-y-6">
            {currentFile ? (
              <DataDescriptionForm
                file={currentFile}
                description={dataDescription || ''}
                onDescriptionChange={setDataDescription}
              />
            ) : (
              <Card className="border-dashed border-2 border-gray-300">
                <CardContent className="flex items-center justify-center h-64">
                  <div className="text-center space-y-3">
                    <FileText className="h-12 w-12 text-gray-400 mx-auto" />
                    <div>
                      <h3 className="font-medium text-gray-900">No File Selected</h3>
                      <p className="text-sm text-gray-500 mt-1">Upload a data file to describe</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="analysis" className="space-y-6">
            {currentFile ? (
              <EnhancedAnalysisPipeline
                file={currentFile}
                dataDescription={dataDescription || ''}
                onAnalysisComplete={handleAnalysisComplete}
              />
            ) : (
              <Card className="border-dashed border-2 border-gray-300">
                <CardContent className="flex items-center justify-center h-64">
                  <div className="text-center space-y-3">
                    <Brain className="h-12 w-12 text-gray-400 mx-auto" />
                    <div>
                      <h3 className="font-medium text-gray-900">No File Selected</h3>
                      <p className="text-sm text-gray-500 mt-1">Upload a data file to start analysis</p>
                    </div>
                    <Button onClick={() => setActiveTab('upload')} variant="outline">
                      <Upload className="h-4 w-4 mr-2" />
                      Upload Data
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="visualizations" className="space-y-6">
            {currentAnalysis ? (
              <EnhancedVisualizationGallery analysisResult={currentAnalysis} />
            ) : (
              <Card className="border-dashed border-2 border-gray-300">
                <CardContent className="flex items-center justify-center h-64">
                  <div className="text-center space-y-3">
                    <BarChart3 className="h-12 w-12 text-gray-400 mx-auto" />
                    <div>
                      <h3 className="font-medium text-gray-900">No Analysis Available</h3>
                      <p className="text-sm text-gray-500 mt-1">Run analysis to generate visualizations</p>
                    </div>
                    <Button 
                      onClick={() => setActiveTab('analysis')} 
                      variant="outline"
                      disabled={!currentFile}
                    >
                      <Brain className="h-4 w-4 mr-2" />
                      Start Analysis
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="query" className="space-y-6">
            {currentFile ? (
              <EnhancedQueryInterface
                file={currentFile}
                dataDescription={dataDescription || ''}
              />
            ) : (
              <Card className="border-dashed border-2 border-gray-300">
                <CardContent className="flex items-center justify-center h-64">
                  <div className="text-center space-y-3">
                    <MessageSquare className="h-12 w-12 text-gray-400 mx-auto" />
                    <div>
                      <h3 className="font-medium text-gray-900">No File Selected</h3>
                      <p className="text-sm text-gray-500 mt-1">Upload a data file to ask questions</p>
                    </div>
                    <Button onClick={() => setActiveTab('upload')} variant="outline">
                      <Upload className="h-4 w-4 mr-2" />
                      Upload Data
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

// Main component wrapped with error boundary
export const EnhancedDataVisualizationPlatform: React.FC = () => {
  return (
    <DataVisualizationErrorBoundary>
      <EnhancedDataVisualizationPlatformContent />
    </DataVisualizationErrorBoundary>
  );
};
