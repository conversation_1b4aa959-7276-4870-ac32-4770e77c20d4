
import { useState, useEffect } from "react";
import { Sidebar } from "./Sidebar";
import { MainEditor } from "./MainEditor";
import { EnhancedMainEditor } from "./EnhancedMainEditor";
import { LiteratureSearch } from "./LiteratureSearch";
import { CitationManager } from "./CitationManager";
import { AIPaperGenerator } from "./AIPaperGenerator";
import { AIBookGenerator } from "./book-generator";
import { BookHistoryPanel } from "./book-generator/components/BookHistoryPanel";
import { AIArticleReviewerWithHistory as ArticleReviewerComponent } from "./article-reviewer";
import { ArticleRevisionSystem } from "./article-revision";
import { ResearchAnalysisPlatform, EnhancedResearchAnalysisPlatform } from "./research-analysis";
import { EnhancedDataVisualizationPlatform } from "./data-visualization";
import { ResearchSearchInterface } from "./research-search";
import { GoogleSearchInterface } from "./google-search";
import { FlowBuilder } from "./flow-builder";
import { FigureAnalysisPlatform } from "./figure-analysis";
import { OpenDeepResearch } from "./open-deep-research";
import { PresentationGenerator } from "./presentation-generator";
import { FlowchartFun } from "./flowchart-fun";
import { AITutor } from "./ai-tutor";
import { CareerExplorer } from "./career-explorer";
import { ArticleFinder } from "./article-finder";
import { AIHumanizer } from "./ai-humanizer";
import { AIPodcastGenerator } from "./ai-podcast";
import { AIWelcomeDashboard } from "./AIWelcomeDashboard";
import { Database } from "@/lib/database.types";

type Document = Database['public']['Tables']['user_documents']['Row'];

export type ActiveView = "welcome" | "editor" | "search" | "citations" | "chat" | "ai-generator" | "book-generator" | "book-library" | "research-analysis" | "data-visualization" | "figure-analysis" | "article-reviewer" | "article-revision" | "research-search" | "google-search" | "flow-builder" | "open-deep-research" | "presentation-generator" | "flowchart-fun" | "ai-tutor" | "career-explorer" | "article-finder" | "ai-humanizer" | "ai-podcast";

export function ResearchDashboard() {
  const [activeView, setActiveView] = useState<ActiveView>("welcome");
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const handleOpenDocument = (document: Document) => {
    setSelectedDocument(document);
    setActiveView("editor");
  };

  const handleSelectTool = (view: ActiveView) => {
    setActiveView(view);
  };

  // Listen for navigation events from book generator
  useEffect(() => {
    const handleNavigateToEditor = (event: CustomEvent) => {
      console.log('📝 Received navigation event to editor:', event.detail);
      setActiveView("editor");
      // Clear selected document so editor loads content from editorService
      setSelectedDocument(null);
    };

    window.addEventListener('navigateToEditor', handleNavigateToEditor as EventListener);

    return () => {
      window.removeEventListener('navigateToEditor', handleNavigateToEditor as EventListener);
    };
  }, []);

  return (
    <div className="min-h-screen flex bg-gray-50">
      {/* Sidebar */}
      <Sidebar
        activeView={activeView}
        onViewChange={setActiveView}
        onOpenDocument={handleOpenDocument}
        selectedDocumentId={selectedDocument?.id}
        collapsed={sidebarCollapsed}
        onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
      />

      {/* Main Content */}
      <div className={`flex-1 overflow-y-auto transition-all duration-300 ${sidebarCollapsed ? 'ml-0' : ''}`}>
        {activeView === "welcome" && (
          <AIWelcomeDashboard onSelectTool={handleSelectTool} />
        )}
        {activeView === "editor" && (
          <EnhancedMainEditor 
            initialDocumentId={selectedDocument?.id}
            initialTitle={selectedDocument?.title}
            initialContent={selectedDocument?.content || ''}
          />
        )}
        {activeView === "search" && <LiteratureSearch />}
        {activeView === "citations" && <CitationManager />}
        {activeView === "ai-generator" && <AIPaperGenerator />}
        {activeView === "book-generator" && <AIBookGenerator />}
        {activeView === "book-library" && (
          <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 py-12">
            <div className="max-w-7xl mx-auto px-6">
              <BookHistoryPanel
                onEditBook={(bookId) => {
                  // Navigate to book generator with loaded book
                  setActiveView("book-generator");
                }}
                onCreateNew={() => setActiveView("book-generator")}
              />
            </div>
          </div>
        )}
        {activeView === "article-reviewer" && <ArticleReviewerComponent />}
        {activeView === "article-revision" && <ArticleRevisionSystem />}
        {activeView === "research-analysis" && <EnhancedResearchAnalysisPlatform />}
        {activeView === "data-visualization" && <EnhancedDataVisualizationPlatform />}
        {activeView === "figure-analysis" && <FigureAnalysisPlatform />}
        {activeView === "research-search" && <ResearchSearchInterface />}
        {activeView === "google-search" && <GoogleSearchInterface />}
        {activeView === "flow-builder" && <FlowBuilder />}
        {activeView === "flowchart-fun" && <FlowchartFun />}
        {activeView === "open-deep-research" && <OpenDeepResearch />}
        {activeView === "presentation-generator" && <PresentationGenerator />}
        {activeView === "ai-tutor" && <AITutor />}
        {activeView === "career-explorer" && <CareerExplorer />}
        {activeView === "article-finder" && <ArticleFinder />}
        {activeView === "ai-humanizer" && <AIHumanizer />}
        {activeView === "ai-podcast" && <AIPodcastGenerator />}
        {activeView === "chat" && (
          <div className="p-8">
            <h2 className="text-2xl font-bold mb-4">AI Chat</h2>
            <p className="text-gray-600">AI Chat is now integrated into the Editor. Switch to the Editor tab to access AI writing assistance.</p>
          </div>
        )}
      </div>
    </div>
  );
}
