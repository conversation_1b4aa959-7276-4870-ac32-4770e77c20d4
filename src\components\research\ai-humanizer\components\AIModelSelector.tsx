/**
 * AI Model Selector Component
 * Allows users to select from available AI models for academic writing humanization
 */

import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Brain,
  Zap,
  Star,
  DollarSign,
  Clock,
  Target
} from "lucide-react";

import { HUMANIZER_AI_MODELS } from '../constants';
import { HumanizerAIModel } from '../types';

interface AIModelSelectorProps {
  selectedModel: string;
  onModelChange: (modelId: string) => void;
  className?: string;
}

export function AIModelSelector({ selectedModel, onModelChange, className }: AIModelSelectorProps) {
  const currentModel = HUMANIZER_AI_MODELS.find(m => m.id === selectedModel);

  /**
   * Get provider icon
   */
  const getProviderIcon = (provider: string) => {
    switch (provider.toLowerCase()) {
      case 'google': return '🤖';
      case 'anthropic': return '🧠';
      case 'openai': return '⚡';
      default: return '🔮';
    }
  };

  /**
   * Get cost color
   */
  const getCostColor = (cost: string) => {
    switch (cost) {
      case 'low': return 'text-green-600 bg-green-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'high': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  /**
   * Get cost icon
   */
  const getCostIcon = (cost: string) => {
    switch (cost) {
      case 'low': return <DollarSign className="h-3 w-3" />;
      case 'medium': return <Clock className="h-3 w-3" />;
      case 'high': return <Star className="h-3 w-3" />;
      default: return <Target className="h-3 w-3" />;
    }
  };

  return (
    <div className={className}>
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center gap-2">
            <Brain className="h-5 w-5 text-blue-600" />
            AI Model Selection
          </CardTitle>
          <CardDescription>
            Choose the AI model for academic writing style analysis and humanization
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Model Selector */}
          <div>
            <Select value={selectedModel} onValueChange={onModelChange}>
              <SelectTrigger className="w-full h-12 bg-white border-gray-300 hover:border-blue-400 focus:border-blue-500 rounded-lg">
                <SelectValue placeholder="Select AI model">
                  {currentModel && (
                    <div className="flex items-center gap-3">
                      <span className="text-lg">{getProviderIcon(currentModel.provider)}</span>
                      <div className="flex flex-col items-start">
                        <span className="font-semibold text-gray-900">{currentModel.name}</span>
                        <span className="text-xs text-gray-500">{currentModel.provider}</span>
                      </div>
                    </div>
                  )}
                </SelectValue>
              </SelectTrigger>

              <SelectContent className="w-full rounded-xl border-gray-200 shadow-xl bg-white">
                <div className="p-2">
                  <div className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-3 px-2">
                    Available Models
                  </div>
                  {HUMANIZER_AI_MODELS.map((model) => (
                    <SelectItem
                      key={model.id}
                      value={model.id}
                      className="rounded-lg p-3 mb-1 hover:bg-blue-50 focus:bg-blue-50 transition-all duration-200"
                    >
                      <div className="flex items-start gap-3 w-full">
                        <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0 shadow-sm">
                          <span className="text-lg">{getProviderIcon(model.provider)}</span>
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="font-semibold text-gray-900 truncate">{model.name}</span>
                            <Badge className={`text-xs px-2 py-0.5 ${getCostColor(model.cost)}`}>
                              {getCostIcon(model.cost)}
                              <span className="ml-1 capitalize">{model.cost}</span>
                            </Badge>
                          </div>
                          <p className="text-xs text-gray-600 mb-2 line-clamp-2">{model.description}</p>
                          <div className="flex flex-wrap gap-1">
                            {model.strengths.slice(0, 2).map((strength, index) => (
                              <Badge key={index} variant="outline" className="text-xs px-1.5 py-0.5">
                                {strength}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </div>
              </SelectContent>
            </Select>
          </div>

          {/* Current Model Info */}
          {currentModel && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0 shadow-sm">
                  <span className="text-xl">{getProviderIcon(currentModel.provider)}</span>
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <h3 className="font-semibold text-gray-900">{currentModel.name}</h3>
                    <Badge className={`text-xs ${getCostColor(currentModel.cost)}`}>
                      {getCostIcon(currentModel.cost)}
                      <span className="ml-1 capitalize">{currentModel.cost} cost</span>
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-700 mb-3">{currentModel.description}</p>
                  
                  <div className="space-y-2">
                    <div>
                      <span className="text-xs font-medium text-gray-600">Strengths:</span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {currentModel.strengths.map((strength, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {strength}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    
                    <div>
                      <span className="text-xs font-medium text-gray-600">Best for:</span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {currentModel.bestFor.map((use, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {use}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Model Comparison Info */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <h4 className="font-medium text-gray-800 mb-2 flex items-center gap-2">
              <Zap className="h-4 w-4 text-yellow-600" />
              Quick Guide
            </h4>
            <div className="space-y-1 text-xs text-gray-600">
              <div className="flex items-center gap-2">
                <span className="text-green-600">💚</span>
                <span><strong>Gemini 2.5 Flash:</strong> Fast, cost-effective, great for most academic content</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-blue-600">🧠</span>
                <span><strong>Claude 3.5 Sonnet:</strong> Best for long dissertations and complex analysis</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-purple-600">⚡</span>
                <span><strong>GPT-4o:</strong> Versatile, excellent for research papers and reports</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
