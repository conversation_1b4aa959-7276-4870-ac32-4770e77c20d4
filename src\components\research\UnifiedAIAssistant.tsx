import React, { useState, useRef, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import {
  Brain,
  Send,
  Replace,
  Plus,
  Eye,
  Loader2,
  Settings,
  Sparkles,
  ChevronDown,
  ChevronUp,
  Copy,
  Check,
  Wrench,
  Search,
  X,
  Minimize2,
  Maximize2,
  ExternalLink,
  Quote,
  Wand2,
  Edit,
  FileText,
  BookOpen,
  PenTool,
  Type,
  Lightbulb,
  Target,
  Zap,
  RefreshCw,
  CheckCircle,
  MessageSquare
} from 'lucide-react';
import { toast } from 'sonner';
import { enhancedAIService, AI_MODELS, AIModel, RESEARCH_TOOLS } from './paper-generator/enhanced-ai.service';
import { tavilySearchService } from './research-search/services/tavily-search.service';
import { SearchResultsPopup } from './SearchResultsPopup';
import './academic-interface.css';

interface UnifiedAIAssistantProps {
  onAIRequest: (prompt: string, text: string, mode: 'replace' | 'insert' | 'display') => void;
  selectedText: string;
  documentContent: string;
  aiLoading: boolean;
  aiResponse: string;
  className?: string;
  isVisible: boolean;
  onToggleVisibility: () => void;
}

export function UnifiedAIAssistant({
  onAIRequest,
  selectedText,
  documentContent,
  aiLoading,
  aiResponse,
  className = '',
  isVisible,
  onToggleVisibility
}: UnifiedAIAssistantProps) {
  const [prompt, setPrompt] = useState('');
  const [selectedMode, setSelectedMode] = useState<'replace' | 'insert' | 'display'>('display');
  const [selectedModel, setSelectedModel] = useState<string>(enhancedAIService.getDefaultModel());
  const [isExpanded, setIsExpanded] = useState(false);
  const [showModelSelector, setShowModelSelector] = useState(false);
  const [activeTab, setActiveTab] = useState<'chat' | 'tools' | 'search' | 'history'>('chat');
  const [activeToolMode, setActiveToolMode] = useState<'writing' | 'review' | 'research'>('writing');
  const [copied, setCopied] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [searchLoading, setSearchLoading] = useState(false);
  const [processingTool, setProcessingTool] = useState<string | null>(null);
  const [showSearchPopup, setShowSearchPopup] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showHistory, setShowHistory] = useState(false);
  const [conversationHistory, setConversationHistory] = useState<Array<{
    id: string;
    timestamp: Date;
    prompt: string;
    response: string;
    type: 'chat' | 'tool' | 'search';
    expanded?: boolean;
  }>>([]);
  const [incomingResult, setIncomingResult] = useState<{result: string, toolName: string} | null>(null);

  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = textareaRef.current.scrollHeight + 'px';
    }
  }, [prompt]);

  // Handle incoming results from floating toolbar
  useEffect(() => {
    if (aiResponse && aiResponse.trim()) {
      // Check if this is a new response (not from our own requests)
      const newEntry = {
        id: Date.now().toString(),
        timestamp: new Date(),
        prompt: incomingResult?.toolName || 'Quick Fix',
        response: aiResponse,
        type: 'tool' as const,
        expanded: true
      };

      setConversationHistory(prev => [newEntry, ...prev]);
      setActiveTab('chat'); // Switch to chat tab to show the result
      setIncomingResult(null); // Clear the incoming result
    }
  }, [aiResponse, incomingResult]);

  // Update conversation history when AI response changes
  useEffect(() => {
    if (aiResponse && conversationHistory.length > 0) {
      const latestEntry = conversationHistory[0];
      if (latestEntry && !latestEntry.response) {
        setConversationHistory(prev =>
          prev.map((entry, index) =>
            index === 0 ? { ...entry, response: aiResponse } : entry
          )
        );
      }
    }
  }, [aiResponse]);



  // Action mode options
  const actionModes = [
    { 
      id: 'replace', 
      label: 'Replace', 
      icon: <Replace className="h-3 w-3" />, 
      description: 'Replace the selected text',
      disabled: !selectedText.trim()
    },
    { 
      id: 'insert', 
      label: 'Insert', 
      icon: <Plus className="h-3 w-3" />, 
      description: 'Insert at cursor position'
    },
    { 
      id: 'display', 
      label: 'Display', 
      icon: <Eye className="h-3 w-3" />, 
      description: 'Show in assistant'
    }
  ];

  // Icon mapping for tools
  const iconMap: { [key: string]: React.ReactNode } = {
    'Type': <Type className="h-4 w-4" />,
    'FileText': <FileText className="h-4 w-4" />,
    'BookOpen': <BookOpen className="h-4 w-4" />,
    'PenTool': <PenTool className="h-4 w-4" />,
    'Sparkles': <Sparkles className="h-4 w-4" />,
    'Search': <Search className="h-4 w-4" />,
    'Lightbulb': <Lightbulb className="h-4 w-4" />,
    'Target': <Target className="h-4 w-4" />,
    'Zap': <Zap className="h-4 w-4" />,
    'RefreshCw': <RefreshCw className="h-4 w-4" />,
    'CheckCircle': <CheckCircle className="h-4 w-4" />,
    'Edit': <Edit className="h-4 w-4" />,
    'Wand2': <Wand2 className="h-4 w-4" />
  };



  // Get tools by mode with improved organization
  const getToolsByMode = (mode: string) => {
    switch (mode) {
      case 'writing':
        // Content generation and enhancement tools for writing
        return RESEARCH_TOOLS.filter(tool =>
          ['generation', 'enhancement'].includes(tool.category)
        );
      case 'review':
        // Review and analysis tools
        return RESEARCH_TOOLS.filter(tool =>
          ['review', 'analysis'].includes(tool.category)
        );
      case 'research':
        // Research-specific tools including citations, methodology, and literature
        return RESEARCH_TOOLS.filter(tool =>
          tool.id.includes('citation') ||
          tool.id.includes('reference') ||
          tool.id.includes('search') ||
          tool.id.includes('literature') ||
          tool.id.includes('methodology') ||
          tool.id.includes('similar') ||
          tool.id.includes('gaps') ||
          tool.id.includes('theoretical') ||
          tool.id.includes('framework')
        );
      default:
        return [];
    }
  };

  // Handle AI request with online search
  const handleAIRequestWithSearch = async () => {
    if (!prompt.trim() || aiLoading) return;

    try {
      setSearchLoading(true);
      
      // First, perform online search
      const searchQuery = selectedText.trim() 
        ? `${prompt} ${selectedText}` 
        : prompt;

      const searchResponse = await tavilySearchService.searchAcademic(searchQuery, {
        maxResults: 5,
        searchDepth: 'advanced',
        includeAnswer: true
      });

      // Store search results and show popup
      setSearchResults(searchResponse.results);
      setSearchQuery(prompt);
      setShowSearchPopup(true);

      // Create enhanced prompt with search results
      const searchContext = searchResponse.results
        .map((result, index) => `[${index + 1}] ${result.title}\n${result.content}\nSource: ${result.url}`)
        .join('\n\n');

      const enhancedPrompt = `
Based on the following current research and information, please provide a comprehensive academic response to: "${prompt}"

SEARCH RESULTS:
${searchContext}

CONTEXT: ${selectedText ? `Selected text: "${selectedText}"` : 'General document context'}

REQUIREMENTS:
1. Write in formal academic style
2. Include proper in-text citations (Author, Year) format
3. Synthesize information from multiple sources
4. Provide clear, well-structured response
5. Include a reference list at the end with clickable URLs
6. Ensure content is scholarly and well-referenced

Please provide a thorough, academic response with proper citations and references.

SOURCES FOUND:
${searchResponse.results.map((result, index) => `${index + 1}. ${result.title} - ${result.url}`).join('\n')}
`;

      // Use the enhanced prompt for AI generation
      const context = selectedText.trim() || documentContent.slice(0, 1000);

      // Add to conversation history
      const historyEntry = {
        id: Date.now().toString(),
        timestamp: new Date(),
        prompt: prompt,
        response: '', // Will be updated when response comes
        type: 'search' as const
      };
      setConversationHistory(prev => [historyEntry, ...prev]);

      onAIRequest(enhancedPrompt, context, selectedMode);

      setPrompt('');
      toast.success('AI request with online research submitted');
    } catch (error: any) {
      console.error('AI request with search error:', error);
      toast.error(error.message || 'Failed to submit AI request');
    } finally {
      setSearchLoading(false);
    }
  };

  // Handle regular AI request
  const handleRegularAIRequest = async () => {
    if (!prompt.trim() || aiLoading) return;

    try {
      const context = selectedText.trim() || documentContent.slice(0, 1000);
      const fullPrompt = selectedText.trim()
        ? `Context: "${selectedText}"\n\nRequest: ${prompt}`
        : prompt;

      // Add to conversation history
      const historyEntry = {
        id: Date.now().toString(),
        timestamp: new Date(),
        prompt: prompt,
        response: '', // Will be updated when response comes
        type: 'chat' as const
      };
      setConversationHistory(prev => [historyEntry, ...prev]);

      onAIRequest(fullPrompt, context, selectedMode);
      setPrompt('');
      toast.success('AI request submitted');
    } catch (error: any) {
      console.error('AI request error:', error);
      toast.error(error.message || 'Failed to submit AI request');
    }
  };

  // Handle tool execution
  const handleToolExecution = async (tool: any) => {
    if (tool.requiresSelection && !selectedText.trim()) {
      toast.error(`Please select text to use "${tool.name}"`);
      return;
    }

    if (aiLoading || processingTool) return;

    setProcessingTool(tool.id);

    try {
      const context = tool.requiresSelection ? selectedText : documentContent;
      const result = await enhancedAIService.executeResearchTool(tool.id, context);

      // Add to conversation history
      const historyEntry = {
        id: Date.now().toString(),
        timestamp: new Date(),
        prompt: tool.name,
        response: result,
        type: 'tool' as const
      };
      setConversationHistory(prev => [historyEntry, ...prev]);

      onAIRequest(tool.name, result, tool.mode || 'display');

      toast.success(`${tool.name} completed successfully`);
    } catch (error: any) {
      console.error('Tool execution error:', error);
      toast.error(error.message || `Failed to execute ${tool.name}`);
    } finally {
      setProcessingTool(null);
    }
  };

  // Handle review request
  const handleReviewRequest = async (reviewType: string) => {
    if (!documentContent?.trim()) {
      toast.error('No content to review');
      return;
    }

    if (aiLoading) return;

    try {
      const content = selectedText || documentContent;
      let prompt = '';

      switch (reviewType) {
        case 'comprehensive':
          prompt = `Please provide a comprehensive review of this academic document. Analyze:
1. Structure and organization
2. Argument strength and clarity
3. Writing style and tone
4. Areas for improvement
5. Strengths to maintain

Content: ${content.slice(0, 3000)}...`;
          break;
        case 'academic-style':
          prompt = `Review this text for academic writing style. Check:
1. Formal tone and language
2. Academic conventions
3. Sentence structure
4. Vocabulary appropriateness
5. Professional presentation

Content: ${content.slice(0, 3000)}...`;
          break;
        case 'clarity':
          prompt = `Review this text for clarity and flow. Analyze:
1. Logical progression of ideas
2. Transition quality
3. Sentence clarity
4. Paragraph structure
5. Overall readability

Content: ${content.slice(0, 3000)}...`;
          break;
        case 'citations':
          prompt = `Review this academic document for citations and references. Analyze:
1. Citation format consistency (APA, MLA, etc.)
2. In-text citation accuracy
3. Reference list completeness
4. Missing citations for claims
5. Citation quality and relevance
6. Suggestions for additional sources

Content: ${content.slice(0, 3000)}...`;
          break;
      }

      // Add to conversation history
      const historyEntry = {
        id: Date.now().toString(),
        timestamp: new Date(),
        prompt: `Review: ${reviewType}`,
        response: '', // Will be updated when response comes
        type: 'tool' as const
      };
      setConversationHistory(prev => [historyEntry, ...prev]);

      onAIRequest(prompt, content.slice(0, 1000), 'display');

      toast.success('Review request submitted');
    } catch (error: any) {
      console.error('Review request error:', error);
      toast.error(`Review failed: ${error.message}`);
    }
  };

  // Handle humanize text
  const handleHumanizeText = async () => {
    if (!selectedText.trim()) {
      toast.error('Please select text to humanize');
      return;
    }

    const humanizePrompt = `
Please humanize the following academic text to make it sound more natural and human-written while maintaining academic quality and tone:

ORIGINAL TEXT:
"${selectedText}"

HUMANIZATION REQUIREMENTS:
1. Maintain academic rigor and scholarly tone
2. Improve natural flow and readability
3. Vary sentence structure and length
4. Use more natural transitions between ideas
5. Reduce overly complex or robotic phrasing
6. Keep all factual content and citations intact
7. Ensure the text sounds like it was written by a knowledgeable human academic
8. Maintain formal academic style appropriate for scholarly work

Please provide the humanized version that sounds natural while preserving academic quality.
`;

    try {
      const result = await enhancedAIService.generateText(humanizePrompt, selectedModel);
      onAIRequest('', result, 'replace');
      toast.success('Text humanized successfully');
    } catch (error: any) {
      console.error('Humanize error:', error);
      toast.error(error.message || 'Failed to humanize text');
    }
  };

  // Handle Enter key submission
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      if (activeTab === 'search') {
        handleAIRequestWithSearch();
      } else {
        handleRegularAIRequest();
      }
    }
  };

  // Copy AI response to clipboard
  const handleCopyResponse = async () => {
    if (!aiResponse) return;
    
    try {
      await navigator.clipboard.writeText(aiResponse);
      setCopied(true);
      toast.success('Response copied to clipboard');
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      toast.error('Failed to copy response');
    }
  };

  // Get available models
  const availableModels = AI_MODELS.filter(model => 
    enhancedAIService.hasValidApiKey() || model.provider === 'Google'
  );

  if (!isVisible) {
    return (
      <div className="fixed bottom-6 right-6 z-50">
        <Button
          onClick={onToggleVisibility}
          className="h-14 w-14 rounded-full bg-blue-600 hover:bg-blue-700 shadow-lg"
          title="Open AI Assistant"
        >
          <Brain className="h-6 w-6 text-white" />
        </Button>
      </div>
    );
  }

  return (
    <div className={`fixed bottom-6 right-6 z-50 ${className}`}>
      <Card className={`floating-ai-assistant bg-white shadow-2xl border-gray-200 transition-all duration-300 backdrop-blur-sm ${
        isMinimized ? 'w-96 h-16' : 'w-96 h-[36rem]'
      } rounded-2xl overflow-hidden`}>
        {/* Enhanced Header */}
        <div className="flex items-center justify-between p-4 border-b bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-blue-100 to-purple-100 rounded-xl shadow-sm">
              <Brain className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h3 className="font-bold text-gray-900 text-sm">AI Assistant</h3>
              <div className="flex items-center gap-2 mt-1">
                {selectedText && (
                  <Badge variant="secondary" className="text-xs bg-blue-100 text-blue-700 border-blue-200">
                    {selectedText.length} chars selected
                  </Badge>
                )}
                {conversationHistory.length > 0 && (
                  <Badge variant="outline" className="text-xs text-gray-600">
                    {conversationHistory.length} conversations
                  </Badge>
                )}
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMinimized(!isMinimized)}
              className="h-8 w-8 p-0"
              title={isMinimized ? "Expand" : "Minimize"}
            >
              {isMinimized ? <Maximize2 className="h-4 w-4" /> : <Minimize2 className="h-4 w-4" />}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggleVisibility}
              className="h-8 w-8 p-0"
              title="Close"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {!isMinimized && (
          <>
            {/* Three-Section Layout Tabs */}
            <div className="flex border-b bg-gray-50">
              {[
                { id: 'chat', label: 'Chat & Search', icon: <Brain className="h-4 w-4" />, color: 'blue', description: 'AI chat with search functionality' },
                { id: 'tools', label: 'Writing Tools', icon: <Wrench className="h-4 w-4" />, color: 'green', description: 'Content generation tools' },
                { id: 'search', label: 'Review Tools', icon: <FileText className="h-4 w-4" />, color: 'orange', description: 'Analysis & feedback tools' }
              ].map((tab) => (
                <Button
                  key={tab.id}
                  variant="ghost"
                  size="sm"
                  onClick={() => setActiveTab(tab.id as 'chat' | 'tools' | 'search' | 'history')}
                  className={`flex-1 rounded-none h-12 transition-all duration-200 ${
                    activeTab === tab.id
                      ? 'bg-white shadow-sm border-b-2 border-blue-500 text-blue-600 font-semibold'
                      : 'hover:bg-white/50 text-gray-600 hover:text-gray-800'
                  }`}
                  title={tab.description}
                >
                  <div className="flex flex-col items-center gap-1">
                    {tab.icon}
                    <span className="text-xs font-medium">{tab.label}</span>
                  </div>
                </Button>
              ))}
            </div>

            {/* Content */}
            <div className="flex-1 flex flex-col overflow-hidden">
              {/* Model Selector */}
              {showModelSelector && (
                <div className="p-3 bg-gray-50 border-b">
                  <label className="text-xs font-medium text-gray-700 mb-2 block">
                    AI Model
                  </label>
                  <Select value={selectedModel} onValueChange={setSelectedModel}>
                    <SelectTrigger className="w-full h-8">
                      <SelectValue placeholder="Select AI model" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableModels.map((model) => (
                        <SelectItem key={model.id} value={model.id}>
                          <span className="text-sm">{model.name}</span>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              {/* Chat Tab */}
              {activeTab === 'chat' && (
                <div className="flex-1 flex flex-col p-3 space-y-3">
                  {/* Chat/Research Toggle - Internal to Chat */}
                  <div className="flex gap-1 p-1 bg-gray-100 rounded-lg">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setActiveToolMode('writing')}
                      className={`flex-1 h-7 text-xs ${activeToolMode === 'writing' ? 'bg-white shadow-sm' : ''}`}
                    >
                      <MessageSquare className="h-3 w-3 mr-1" />
                      Chat
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setActiveToolMode('research')}
                      className={`flex-1 h-7 text-xs ${activeToolMode === 'research' ? 'bg-white shadow-sm' : ''}`}
                    >
                      <Search className="h-3 w-3 mr-1" />
                      Research
                    </Button>
                  </div>

                  {/* Prompt Input */}
                  <div className="space-y-2">
                    <Textarea
                      ref={textareaRef}
                      value={prompt}
                      onChange={(e) => setPrompt(e.target.value)}
                      onKeyDown={handleKeyDown}
                      placeholder={
                        activeToolMode === 'research'
                          ? "Search for academic sources and research..."
                          : selectedText
                            ? "How can I help with the selected text?"
                            : "Ask me anything about your writing..."
                      }
                      className="ai-prompt-input min-h-[80px] max-h-[120px] resize-none text-sm text-gray-900 bg-white border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                      disabled={aiLoading}
                    />
                    
                    {/* Action Buttons */}
                    <div className="flex gap-1">
                      {actionModes.map((mode) => (
                        <Button
                          key={mode.id}
                          variant={selectedMode === mode.id ? "default" : "outline"}
                          size="sm"
                          onClick={() => setSelectedMode(mode.id as 'replace' | 'insert' | 'display')}
                          disabled={mode.disabled}
                          className="flex items-center gap-1 text-xs h-7"
                          title={mode.description}
                        >
                          {mode.icon}
                          {mode.label}
                        </Button>
                      ))}
                    </div>

                    {/* Submit Button */}
                    <Button
                      onClick={activeToolMode === 'research' ? handleAIRequestWithSearch : handleRegularAIRequest}
                      disabled={!prompt.trim() || aiLoading}
                      className="w-full h-8"
                      size="sm"
                    >
                      {aiLoading ? (
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      ) : activeToolMode === 'research' ? (
                        <Search className="h-4 w-4 mr-2" />
                      ) : (
                        <Send className="h-4 w-4 mr-2" />
                      )}
                      {activeToolMode === 'research' ? 'Research' : 'Send'}
                    </Button>
                  </div>

                  {/* Quick Actions */}
                  {selectedText && (
                    <div className="space-y-2">
                      <Separator />
                      <div className="text-xs font-medium text-gray-700">Quick Actions</div>
                      <div className="grid grid-cols-2 gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleHumanizeText}
                          className="h-8 text-xs"
                          disabled={aiLoading}
                        >
                          <Wand2 className="h-3 w-3 mr-1" />
                          Humanize
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setPrompt('Improve the clarity and flow of this text');
                            handleRegularAIRequest();
                          }}
                          className="h-8 text-xs"
                          disabled={aiLoading}
                        >
                          <Sparkles className="h-3 w-3 mr-1" />
                          Improve
                        </Button>
                      </div>
                    </div>
                  )}


                </div>
              )}

              {/* Tools Tab */}
              {activeTab === 'tools' && (
                <div className="flex-1 flex flex-col p-3 space-y-3">
                  {/* Tool Mode Toggle - Similar to Chat */}
                  <div className="flex gap-1 p-1 bg-gray-100 rounded-lg">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setActiveToolMode('writing')}
                      className={`flex-1 h-7 text-xs ${activeToolMode === 'writing' ? 'bg-white shadow-sm' : ''}`}
                    >
                      <PenTool className="h-3 w-3 mr-1" />
                      Writing
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setActiveToolMode('review')}
                      className={`flex-1 h-7 text-xs ${activeToolMode === 'review' ? 'bg-white shadow-sm' : ''}`}
                    >
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Review
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setActiveToolMode('research')}
                      className={`flex-1 h-7 text-xs ${activeToolMode === 'research' ? 'bg-white shadow-sm' : ''}`}
                    >
                      <Search className="h-3 w-3 mr-1" />
                      Research
                    </Button>
                  </div>

                  {/* Enhanced Tools Grid with Better Layout */}
                  <ScrollArea className="flex-1 h-80">
                    <div className="p-2">
                      <div className="grid grid-cols-3 gap-2">
                        {getToolsByMode(activeToolMode).map((tool) => (
                          <div
                            key={tool.id}
                            className={`relative group cursor-pointer transition-all duration-200 ${
                              processingTool === tool.id ? 'scale-95' : 'hover:scale-102'
                            }`}
                            onClick={() => handleToolExecution(tool)}
                            title={tool.description}
                          >
                            <div className={`
                              enhanced-tool-card h-16 p-2 rounded-lg transition-all duration-200
                              ${processingTool === tool.id
                                ? 'bg-blue-50 border-blue-200 shadow-inner'
                                : 'bg-white border border-gray-200 hover:border-blue-300 hover:shadow-sm'
                              }
                              ${(tool.requiresSelection && !selectedText.trim())
                                ? 'opacity-50 cursor-not-allowed'
                                : 'cursor-pointer'
                              }
                              flex flex-col items-center justify-center text-center
                            `}>
                              {/* Icon */}
                              <div className={`
                                w-6 h-6 rounded-md mb-1 flex items-center justify-center transition-all duration-200
                                ${processingTool === tool.id ? 'bg-blue-100' : 'bg-blue-50 group-hover:bg-blue-100'}
                              `}>
                                {processingTool === tool.id ? (
                                  <Loader2 className="h-3 w-3 animate-spin text-blue-600" />
                                ) : (
                                  <div className="text-blue-600 group-hover:text-blue-700">
                                    {iconMap[tool.icon] || <FileText className="h-3 w-3" />}
                                  </div>
                                )}
                              </div>

                              {/* Tool name */}
                              <div className="text-[10px] font-medium text-gray-700 leading-tight px-1">
                                {tool.name.length > 12 ? tool.name.substring(0, 12) + '...' : tool.name}
                              </div>

                              {/* Warning indicator */}
                              {tool.requiresSelection && !selectedText.trim() && (
                                <div className="absolute -top-1 -right-1 w-3 h-3 bg-amber-100 border border-amber-300 rounded-full flex items-center justify-center">
                                  <span className="text-[8px] text-amber-600">!</span>
                                </div>
                              )}

                              {/* Processing indicator */}
                              {processingTool === tool.id && (
                                <div className="absolute inset-0 bg-blue-50 bg-opacity-50 rounded-lg flex items-center justify-center">
                                  <div className="text-[8px] text-blue-600 font-medium">Processing...</div>
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>

                      {/* Show count of tools */}
                      <div className="text-center mt-3 text-xs text-gray-500">
                        {getToolsByMode(activeToolMode).length} tools available
                      </div>
                    </div>
                  </ScrollArea>
                </div>
              )}

              {/* Review Tab with Separate Output Section */}
              {activeTab === 'search' && (
                <div className="flex-1 flex flex-col">
                  {/* Review Tools Section */}
                  <div className="p-3 border-b bg-gray-50">
                    <div className="text-xs font-medium text-gray-700 mb-2">Review Tools</div>
                    <div className="grid grid-cols-2 gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleReviewRequest('comprehensive')}
                        disabled={aiLoading || !documentContent.trim()}
                        className="h-8 p-2 justify-start text-xs"
                      >
                        <FileText className="h-3 w-3 text-blue-600 mr-1" />
                        <span>Full Review</span>
                      </Button>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleReviewRequest('academic-style')}
                        disabled={aiLoading || !documentContent.trim()}
                        className="h-8 p-2 justify-start text-xs"
                      >
                        <BookOpen className="h-3 w-3 text-green-600 mr-1" />
                        <span>Style Check</span>
                      </Button>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleReviewRequest('clarity')}
                        disabled={aiLoading || !documentContent.trim()}
                        className="h-8 p-2 justify-start text-xs"
                      >
                        <Zap className="h-3 w-3 text-orange-600 mr-1" />
                        <span>Clarity</span>
                      </Button>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleReviewRequest('citations')}
                        disabled={aiLoading || !documentContent.trim()}
                        className="h-8 p-2 justify-start text-xs"
                      >
                        <Quote className="h-3 w-3 text-purple-600 mr-1" />
                        <span>Citations</span>
                      </Button>
                    </div>
                  </div>

                  {/* Review Output Section */}
                  <div className="flex-1 p-3">
                    <div className="text-xs font-medium text-gray-700 mb-2">Review Feedback</div>
                    <ScrollArea className="h-60 border rounded-lg bg-white">
                      <div className="p-3">
                        {aiLoading ? (
                          <div className="flex items-center justify-center py-8">
                            <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
                            <span className="ml-2 text-sm text-gray-600">Analyzing your document...</span>
                          </div>
                        ) : aiResponse ? (
                          <div className="prose prose-sm max-w-none">
                            <div className="text-sm text-gray-800 whitespace-pre-wrap">
                              {aiResponse}
                            </div>
                          </div>
                        ) : (
                          <div className="text-center py-8 text-gray-500">
                            <FileText className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                            <div className="text-sm">Select a review tool to analyze your document</div>
                            <div className="text-xs text-gray-400 mt-1">Feedback will appear here</div>
                          </div>
                        )}
                      </div>
                    </ScrollArea>
                  </div>
                </div>
              )}

              {/* History Tab */}
              {activeTab === 'history' && (
                <div className="flex-1 flex flex-col p-3 space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="text-xs font-medium text-gray-700">Conversation History</div>
                    {conversationHistory.length > 0 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setConversationHistory([])}
                        className="h-6 px-2 text-xs text-red-600 hover:text-red-700"
                      >
                        Clear All
                      </Button>
                    )}
                  </div>

                  <ScrollArea className="flex-1">
                    {conversationHistory.length === 0 ? (
                      <div className="flex flex-col items-center justify-center h-32 text-center">
                        <MessageSquare className="h-8 w-8 text-gray-300 mb-2" />
                        <div className="text-xs text-gray-500">No conversation history yet</div>
                        <div className="text-xs text-gray-400 mt-1">Start chatting to see your history here</div>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        {conversationHistory.map((entry) => (
                          <div
                            key={entry.id}
                            className="history-card rounded-xl p-3 cursor-pointer"
                          >
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center gap-2">
                                <div className={`w-3 h-3 rounded-full shadow-sm ${
                                  entry.type === 'chat' ? 'conversation-type-chat' :
                                  entry.type === 'tool' ? 'conversation-type-tool' : 'conversation-type-search'
                                }`} />
                                <span className="text-xs font-semibold text-gray-700 capitalize">
                                  {entry.type}
                                </span>
                              </div>
                              <span className="text-xs text-gray-500 font-medium">
                                {entry.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                              </span>
                            </div>
                            <div className="text-xs text-gray-900 font-semibold mb-2 line-clamp-2">
                              {entry.prompt}
                            </div>
                            {entry.response && (
                              <div className="text-xs text-gray-600 line-clamp-3 bg-gradient-to-r from-gray-50 to-blue-50 p-2 rounded-lg border border-gray-100">
                                {entry.response.substring(0, 120)}
                                {entry.response.length > 120 && '...'}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </ScrollArea>
                </div>
              )}

              {/* Enhanced Settings */}
              <div className="p-3 border-t bg-gradient-to-r from-gray-50 to-blue-50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowModelSelector(!showModelSelector)}
                      className="h-7 px-2 text-xs hover:bg-white/50 transition-colors"
                    >
                      <Settings className="h-3 w-3 mr-1" />
                      Model
                    </Button>

                    {conversationHistory.length > 0 && (
                      <Badge variant="outline" className="text-xs bg-white/50">
                        {conversationHistory.length}
                      </Badge>
                    )}
                  </div>

                  <div className="flex items-center gap-1">
                    {aiResponse && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleCopyResponse}
                        className="h-7 px-2 text-xs hover:bg-white/50 transition-colors"
                        title="Copy last response"
                      >
                        {copied ? <Check className="h-3 w-3 text-green-600" /> : <Copy className="h-3 w-3" />}
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </Card>

      {/* Search Results Popup */}
      <SearchResultsPopup
        isVisible={showSearchPopup}
        onClose={() => setShowSearchPopup(false)}
        searchResults={searchResults}
        searchQuery={searchQuery}
        aiResponse={aiResponse}
      />
    </div>
  );
}
