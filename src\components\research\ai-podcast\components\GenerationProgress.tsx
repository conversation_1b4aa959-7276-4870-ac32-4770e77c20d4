import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { 
  FileText, 
  Mic, 
  Volume2, 
  Clock,
  CheckCircle,
  Loader2
} from "lucide-react";

import { PodcastGenerationProgress } from '../types';

interface GenerationProgressProps {
  progress: PodcastGenerationProgress;
}

const STAGE_ICONS = {
  outline: FileText,
  script: Mic,
  audio: Volume2
};

const STAGE_COLORS = {
  outline: 'text-blue-500',
  script: 'text-purple-500',
  audio: 'text-green-500'
};

const STAGE_DESCRIPTIONS = {
  outline: 'Analyzing content and creating podcast structure',
  script: 'Writing engaging dialogue between hosts',
  audio: 'Converting script to high-quality audio'
};

export function GenerationProgress({ progress }: GenerationProgressProps) {
  const Icon = STAGE_ICONS[progress.stage];
  const colorClass = STAGE_COLORS[progress.stage];

  const formatTime = (seconds: number): string => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  return (
    <div className="space-y-4">
      {/* Current Stage */}
      <div className="flex items-center gap-3">
        <div className={`p-2 rounded-lg bg-gray-100 ${colorClass}`}>
          <Icon className="h-5 w-5" />
        </div>
        <div className="flex-1">
          <h3 className="font-semibold text-gray-900">
            {progress.stage === 'outline' && 'Creating Outline'}
            {progress.stage === 'script' && 'Writing Script'}
            {progress.stage === 'audio' && 'Generating Audio'}
          </h3>
          <p className="text-sm text-gray-600">{progress.message}</p>
        </div>
        <Badge variant="secondary" className="flex items-center gap-1">
          <Loader2 className="h-3 w-3 animate-spin" />
          {progress.progress}%
        </Badge>
      </div>

      {/* Progress Bar */}
      <div className="space-y-2">
        <Progress value={progress.progress} className="h-2" />
        <div className="flex justify-between text-xs text-gray-500">
          <span>Progress</span>
          {progress.estimatedTimeRemaining && (
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              <span>~{formatTime(progress.estimatedTimeRemaining)} remaining</span>
            </div>
          )}
        </div>
      </div>

      {/* Stage Timeline */}
      <div className="grid grid-cols-3 gap-2">
        {(['outline', 'script', 'audio'] as const).map((stage, index) => {
          const StageIcon = STAGE_ICONS[stage];
          const isActive = progress.stage === stage;
          const isCompleted = ['outline', 'script', 'audio'].indexOf(progress.stage) > index;
          
          return (
            <Card 
              key={stage}
              className={`border transition-all ${
                isActive 
                  ? 'border-blue-500 bg-blue-50' 
                  : isCompleted 
                    ? 'border-green-500 bg-green-50' 
                    : 'border-gray-200'
              }`}
            >
              <CardContent className="p-3 text-center">
                <div className={`mx-auto mb-2 p-2 rounded-lg w-fit ${
                  isActive 
                    ? 'bg-blue-100 text-blue-600' 
                    : isCompleted 
                      ? 'bg-green-100 text-green-600' 
                      : 'bg-gray-100 text-gray-400'
                }`}>
                  {isCompleted ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <StageIcon className="h-4 w-4" />
                  )}
                </div>
                <h4 className={`text-xs font-medium ${
                  isActive 
                    ? 'text-blue-900' 
                    : isCompleted 
                      ? 'text-green-900' 
                      : 'text-gray-600'
                }`}>
                  {stage === 'outline' && 'Outline'}
                  {stage === 'script' && 'Script'}
                  {stage === 'audio' && 'Audio'}
                </h4>
                <p className={`text-xs mt-1 ${
                  isActive 
                    ? 'text-blue-700' 
                    : isCompleted 
                      ? 'text-green-700' 
                      : 'text-gray-500'
                }`}>
                  {isCompleted ? 'Complete' : isActive ? 'In Progress' : 'Pending'}
                </p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Stage Details */}
      <Card className="border border-gray-200 bg-gray-50">
        <CardContent className="p-4">
          <h4 className="font-medium text-gray-900 mb-2">What's happening now?</h4>
          <p className="text-sm text-gray-700">
            {STAGE_DESCRIPTIONS[progress.stage]}
          </p>
          
          {progress.stage === 'outline' && (
            <div className="mt-3 space-y-1 text-xs text-gray-600">
              <p>• Identifying key topics and themes</p>
              <p>• Structuring conversation flow</p>
              <p>• Planning discussion points</p>
            </div>
          )}
          
          {progress.stage === 'script' && (
            <div className="mt-3 space-y-1 text-xs text-gray-600">
              <p>• Creating natural dialogue</p>
              <p>• Adding transitions and questions</p>
              <p>• Balancing host interactions</p>
            </div>
          )}
          
          {progress.stage === 'audio' && (
            <div className="mt-3 space-y-1 text-xs text-gray-600">
              <p>• Converting text to speech</p>
              <p>• Applying voice characteristics</p>
              <p>• Mixing and finalizing audio</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
