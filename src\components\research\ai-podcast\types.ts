export interface PodcastMetadata {
  title: string;
  description?: string;
  duration?: number;
  language: string;
  tags?: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface VoiceOption {
  id: string;
  name: string;
  gender: 'male' | 'female';
  language: string;
  provider: AudioProvider;
  previewUrl?: string;
  description?: string;
}

export interface AudioProvider {
  id: string;
  name: string;
  enabled: boolean;
  apiKey?: string;
  voices: VoiceOption[];
  maxDuration?: number;
  costPerMinute?: number;
}

export interface PodcastInputSource {
  type: 'topic' | 'link' | 'file' | 'text';
  content: string;
  metadata?: {
    fileName?: string;
    fileSize?: number;
    fileType?: string;
    url?: string;
    title?: string;
  };
}

export interface PodcastGeneration {
  id: string;
  userId: string;
  title: string;
  inputSource: PodcastInputSource;
  outline?: string;
  script?: string;
  audioUrl?: string;
  status: 'pending' | 'generating_outline' | 'generating_script' | 'generating_audio' | 'completed' | 'failed';
  progress: number;
  error?: string;
  metadata: PodcastMetadata;
  voices: {
    host1: VoiceOption;
    host2: VoiceOption;
  };
  provider: AudioProvider;
  createdAt: Date;
  updatedAt: Date;
}

export interface PodcastScript {
  segments: PodcastSegment[];
  totalDuration: number;
  wordCount: number;
}

export interface PodcastSegment {
  id: string;
  speaker: 'host1' | 'host2';
  text: string;
  duration?: number;
  timestamp?: number;
  emotion?: 'neutral' | 'excited' | 'curious' | 'thoughtful';
}

export interface PodcastOutline {
  introduction: string;
  mainPoints: OutlinePoint[];
  conclusion: string;
  estimatedDuration: number;
}

export interface OutlinePoint {
  title: string;
  description: string;
  keyTopics: string[];
  duration: number;
}

export interface AudioGenerationOptions {
  provider: AudioProvider;
  voices: {
    host1: VoiceOption;
    host2: VoiceOption;
  };
  outputFormat: 'mp3' | 'wav';
  quality: 'standard' | 'high';
  speed: number;
}

export interface PodcastHistory {
  podcasts: PodcastGeneration[];
  totalCount: number;
  page: number;
  pageSize: number;
}

export interface FileUploadProgress {
  fileId: string;
  fileName: string;
  progress: number;
  status: 'uploading' | 'processing' | 'completed' | 'failed';
  error?: string;
}

export interface PodcastGenerationProgress {
  stage: 'outline' | 'script' | 'audio';
  progress: number;
  message: string;
  estimatedTimeRemaining?: number;
}

export interface PodcastPlayerState {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  playbackRate: number;
}

export interface PodcastExportOptions {
  format: 'mp3' | 'wav';
  includeScript: boolean;
  includeOutline: boolean;
  quality: 'standard' | 'high';
}

// API Response types
export interface PodcastGenerationResponse {
  success: boolean;
  data?: PodcastGeneration;
  error?: string;
}

export interface PodcastHistoryResponse {
  success: boolean;
  data?: PodcastHistory;
  error?: string;
}

export interface VoiceListResponse {
  success: boolean;
  data?: VoiceOption[];
  error?: string;
}

// UI State types
export interface PodcastGeneratorState {
  currentTab: 'topic' | 'link' | 'file' | 'text';
  inputContent: string;
  selectedVoices: {
    host1?: VoiceOption;
    host2?: VoiceOption;
  };
  selectedProvider?: AudioProvider;
  outputLanguage: string;
  isGenerating: boolean;
  generationProgress?: PodcastGenerationProgress;
  currentPodcast?: PodcastGeneration;
  uploadProgress?: FileUploadProgress[];
  playerState?: PodcastPlayerState;
}

export interface PodcastFormData {
  title: string;
  inputSource: PodcastInputSource;
  voices: {
    host1: VoiceOption;
    host2: VoiceOption;
  };
  provider: AudioProvider;
  language: string;
  options: AudioGenerationOptions;
}
