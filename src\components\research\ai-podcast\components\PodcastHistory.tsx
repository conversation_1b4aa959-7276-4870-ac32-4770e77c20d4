import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Search,
  Play,
  Download,
  Trash2,
  Clock,
  Calendar,
  Filter,
  SortDesc,
  Mic,
  FileText,
  Link,
  Upload
} from "lucide-react";

import { PodcastGeneration } from '../types';

interface PodcastHistoryProps {
  onSelectPodcast: (podcast: PodcastGeneration) => void;
}

// Mock data for demonstration
const MOCK_PODCASTS: PodcastGeneration[] = [
  {
    id: '1',
    userId: 'user1',
    title: 'The Future of Artificial Intelligence in Healthcare',
    inputSource: {
      type: 'topic',
      content: 'The future of artificial intelligence in healthcare'
    },
    status: 'completed',
    progress: 100,
    metadata: {
      title: 'The Future of Artificial Intelligence in Healthcare',
      duration: 285,
      language: 'en',
      tags: ['AI', 'Healthcare', 'Technology'],
      createdAt: new Date('2024-01-15'),
      updatedAt: new Date('2024-01-15')
    },
    voices: {
      host1: {
        id: 'voice1',
        name: 'Professional Male',
        gender: 'male',
        language: 'en',
        provider: { id: 'fish', name: 'Fish Audio', enabled: true, voices: [] }
      },
      host2: {
        id: 'voice2',
        name: 'Friendly Female',
        gender: 'female',
        language: 'en',
        provider: { id: 'fish', name: 'Fish Audio', enabled: true, voices: [] }
      }
    },
    provider: { id: 'fish', name: 'Fish Audio', enabled: true, voices: [] },
    audioUrl: '/mock-audio-1.mp3',
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15')
  },
  {
    id: '2',
    userId: 'user1',
    title: 'Climate Change Solutions and Renewable Energy',
    inputSource: {
      type: 'file',
      content: 'Research paper content...',
      metadata: {
        fileName: 'climate-research.pdf',
        fileType: 'application/pdf'
      }
    },
    status: 'completed',
    progress: 100,
    metadata: {
      title: 'Climate Change Solutions and Renewable Energy',
      duration: 420,
      language: 'en',
      tags: ['Climate', 'Environment', 'Energy'],
      createdAt: new Date('2024-01-10'),
      updatedAt: new Date('2024-01-10')
    },
    voices: {
      host1: {
        id: 'voice1',
        name: 'Professional Male',
        gender: 'male',
        language: 'en',
        provider: { id: 'gemini', name: 'Google Gemini', enabled: true, voices: [] }
      },
      host2: {
        id: 'voice2',
        name: 'Host Female',
        gender: 'female',
        language: 'en',
        provider: { id: 'gemini', name: 'Google Gemini', enabled: true, voices: [] }
      }
    },
    provider: { id: 'gemini', name: 'Google Gemini', enabled: true, voices: [] },
    audioUrl: '/mock-audio-2.mp3',
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-10')
  }
];

export function PodcastHistory({ onSelectPodcast }: PodcastHistoryProps) {
  const [podcasts, setPodcasts] = useState<PodcastGeneration[]>(MOCK_PODCASTS);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'date' | 'title' | 'duration'>('date');
  const [filterBy, setFilterBy] = useState<'all' | 'topic' | 'link' | 'file' | 'text'>('all');

  const getInputTypeIcon = (type: string) => {
    switch (type) {
      case 'topic':
        return <Mic className="h-4 w-4" />;
      case 'link':
        return <Link className="h-4 w-4" />;
      case 'file':
        return <Upload className="h-4 w-4" />;
      case 'text':
        return <FileText className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const formatDate = (date: Date): string => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const filteredAndSortedPodcasts = podcasts
    .filter(podcast => {
      const matchesSearch = podcast.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           podcast.metadata.tags?.some(tag => 
                             tag.toLowerCase().includes(searchQuery.toLowerCase())
                           );
      const matchesFilter = filterBy === 'all' || podcast.inputSource.type === filterBy;
      return matchesSearch && matchesFilter;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'date':
          return b.createdAt.getTime() - a.createdAt.getTime();
        case 'title':
          return a.title.localeCompare(b.title);
        case 'duration':
          return (b.metadata.duration || 0) - (a.metadata.duration || 0);
        default:
          return 0;
      }
    });

  const handleDeletePodcast = (podcastId: string) => {
    setPodcasts(prev => prev.filter(p => p.id !== podcastId));
  };

  const handleDownloadPodcast = (podcast: PodcastGeneration) => {
    if (podcast.audioUrl) {
      const link = document.createElement('a');
      link.href = podcast.audioUrl;
      link.download = `${podcast.title}.mp3`;
      link.click();
    }
  };

  return (
    <div className="space-y-4">
      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search podcasts..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <div className="flex gap-2">
          <select
            value={filterBy}
            onChange={(e) => setFilterBy(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm"
          >
            <option value="all">All Types</option>
            <option value="topic">Topic</option>
            <option value="link">Link</option>
            <option value="file">File</option>
            <option value="text">Text</option>
          </select>
          
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm"
          >
            <option value="date">Sort by Date</option>
            <option value="title">Sort by Title</option>
            <option value="duration">Sort by Duration</option>
          </select>
        </div>
      </div>

      {/* Podcast List */}
      <ScrollArea className="h-96">
        <div className="space-y-3">
          {filteredAndSortedPodcasts.length === 0 ? (
            <Card className="border border-gray-200">
              <CardContent className="p-8 text-center">
                <Mic className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-600 mb-2">No podcasts found</h3>
                <p className="text-gray-500">
                  {searchQuery || filterBy !== 'all' 
                    ? 'Try adjusting your search or filters'
                    : 'Create your first podcast to see it here'
                  }
                </p>
              </CardContent>
            </Card>
          ) : (
            filteredAndSortedPodcasts.map((podcast) => (
              <Card key={podcast.id} className="border border-gray-200 hover:border-purple-300 transition-colors">
                <CardContent className="p-4">
                  <div className="flex items-start gap-4">
                    {/* Podcast Info */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between mb-2">
                        <h3 className="font-semibold text-gray-900 truncate pr-2">
                          {podcast.title}
                        </h3>
                        <div className="flex items-center gap-1 flex-shrink-0">
                          {getInputTypeIcon(podcast.inputSource.type)}
                          <Badge variant="outline" className="text-xs">
                            {podcast.inputSource.type}
                          </Badge>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          <span>{formatDuration(podcast.metadata.duration || 0)}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          <span>{formatDate(podcast.createdAt)}</span>
                        </div>
                        <Badge variant="secondary" className="text-xs">
                          {podcast.provider.name}
                        </Badge>
                      </div>
                      
                      {podcast.metadata.tags && (
                        <div className="flex flex-wrap gap-1 mb-3">
                          {podcast.metadata.tags.slice(0, 3).map((tag, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                          {podcast.metadata.tags.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{podcast.metadata.tags.length - 3}
                            </Badge>
                          )}
                        </div>
                      )}
                      
                      <div className="text-xs text-gray-500">
                        Voices: {podcast.voices.host1.name} & {podcast.voices.host2.name}
                      </div>
                    </div>
                    
                    {/* Action Buttons */}
                    <div className="flex flex-col gap-2 flex-shrink-0">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onSelectPodcast(podcast)}
                        className="flex items-center gap-2"
                      >
                        <Play className="h-3 w-3" />
                        Play
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDownloadPodcast(podcast)}
                        className="flex items-center gap-2"
                      >
                        <Download className="h-3 w-3" />
                        Download
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeletePodcast(podcast.id)}
                        className="flex items-center gap-2 text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-3 w-3" />
                        Delete
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </ScrollArea>

      {/* Summary */}
      {filteredAndSortedPodcasts.length > 0 && (
        <div className="text-sm text-gray-600 text-center">
          Showing {filteredAndSortedPodcasts.length} of {podcasts.length} podcasts
        </div>
      )}
    </div>
  );
}
