{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@antv/gpt-vis": "^0.5.2", "@babel/standalone": "^7.28.1", "@google/genai": "^1.9.0", "@google/generative-ai": "^0.24.1", "@hookform/resolvers": "^3.9.0", "@langchain/core": "^0.3.66", "@langchain/openai": "^0.6.2", "@monaco-editor/react": "^4.7.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.50.3", "@tanstack/react-query": "^5.56.2", "@tiptap/extension-bold": "^2.22.3", "@tiptap/extension-code": "^2.22.3", "@tiptap/extension-code-block": "^2.22.3", "@tiptap/extension-color": "^2.22.3", "@tiptap/extension-floating-menu": "^2.22.3", "@tiptap/extension-font-family": "^2.22.3", "@tiptap/extension-heading": "^2.22.3", "@tiptap/extension-highlight": "^2.22.3", "@tiptap/extension-image": "^2.23.0", "@tiptap/extension-italic": "^2.22.3", "@tiptap/extension-link": "^2.22.3", "@tiptap/extension-placeholder": "^2.22.3", "@tiptap/extension-strike": "^2.22.3", "@tiptap/extension-table": "^2.22.3", "@tiptap/extension-table-cell": "^2.22.3", "@tiptap/extension-table-header": "^2.22.3", "@tiptap/extension-table-row": "^2.22.3", "@tiptap/extension-text-align": "^2.22.3", "@tiptap/extension-text-style": "^2.22.3", "@tiptap/extension-typography": "^2.22.3", "@tiptap/extension-underline": "^2.22.3", "@tiptap/pm": "^2.22.3", "@tiptap/react": "^2.22.3", "@tiptap/starter-kit": "^2.22.3", "@tiptap/suggestion": "^2.23.0", "@types/d3": "^7.4.3", "@types/diff": "^7.0.2", "@types/mime": "^3.0.4", "@types/papaparse": "^5.3.16", "@types/react-plotly.js": "^2.6.3", "@types/react-syntax-highlighter": "^15.5.13", "@types/three": "^0.178.1", "@xyflow/react": "^12.8.2", "ai": "^4.3.19", "allotment": "^1.20.4", "buffer": "^6.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "cytoscape": "^3.32.1", "cytoscape-cose-bilkent": "^4.1.0", "cytoscape-dagre": "^2.5.0", "d3": "^7.9.0", "date-fns": "^3.6.0", "dayjs": "^1.11.13", "diff": "^8.0.2", "docx": "^9.5.1", "docx-pdf": "^0.0.1", "embla-carousel-react": "^8.3.0", "file-saver": "^2.0.5", "framer-motion": "^12.23.0", "html-to-text": "^9.0.5", "html2canvas": "^1.4.1", "immer": "^10.1.1", "input-otp": "^1.2.4", "jspdf": "^3.0.1", "lucide-react": "^0.462.0", "mammoth": "^1.9.1", "markdown-it": "^14.1.0", "mermaid": "^10.9.1", "mime": "^4.0.7", "nanoid": "^5.1.5", "next-themes": "^0.3.0", "openai": "^5.10.2", "papaparse": "^5.5.3", "pdf-parse": "^1.1.1", "pdfjs-dist": "^5.3.93", "plotly.js-dist-min": "^3.0.1", "prosemirror-commands": "^1.7.1", "prosemirror-model": "^1.25.1", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.40.0", "re-resizable": "^6.11.2", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-diff-viewer-continued": "^3.4.0", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.53.0", "react-markdown": "^10.1.0", "react-plotly.js": "^2.6.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "react-spring": "^10.0.1", "react-syntax-highlighter": "^15.6.1", "recharts": "^2.12.7", "remark-gfm": "^4.0.1", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "three": "^0.178.0", "tippy.js": "^6.3.7", "tiptap-extension-resizable-image": "^1.0.7", "tiptap-extension-resize-image": "^1.2.2", "vaul": "^0.9.3", "vega": "^6.1.2", "vega-embed": "^7.0.2", "vega-lite": "^6.2.0", "xlsx": "^0.18.5", "zod": "^3.23.8", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.9.0", "@playwright/test": "^1.54.1", "@tailwindcss/typography": "^0.5.15", "@types/node": "^22.5.5", "@types/pdf-parse": "^1.1.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/react-dropzone": "^4.2.2", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "lovable-tagger": "^1.1.7", "postcss": "^8.4.47", "tailwindcss": "^3.4.11", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1"}}