import React, { useState, useMemo } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Eye,
  Table,
  BarChart3,
  Info,
  Download,
  Search,
  Filter,
  ChevronLeft,
  ChevronRight,
  Database,
  Hash,
  Type,
  Calendar,
  ToggleLeft
} from "lucide-react";
import { UploadedFile } from '../types';
import { FileProcessingService } from '../services/file-processing.service';

interface DataPreviewProps {
  file: UploadedFile;
  className?: string;
}

export const DataPreview: React.FC<DataPreviewProps> = ({
  file,
  className = ""
}) => {
  const [currentPage, setCurrentPage] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const rowsPerPage = 10;

  // Calculate data types and basic stats
  const dataAnalysis = useMemo(() => {
    if (!file.data || !Array.isArray(file.data) || !file.headers || !Array.isArray(file.headers)) {
      return { dataTypes: {}, basicStats: {}, missingValues: {} };
    }

    const dataTypes = FileProcessingService.detectDataTypes(file.data, file.headers);
    const basicStats = FileProcessingService.calculateBasicStats(file.data, file.headers, dataTypes);

    // Count missing values
    const missingValues: Record<string, number> = {};
    file.headers.forEach((header, index) => {
      const columnData = file.data.map(row => Array.isArray(row) ? row[index] : null);
      missingValues[header] = columnData.filter(val => val == null || val === '').length;
    });

    return { dataTypes, basicStats, missingValues };
  }, [file]);

  // Filter data based on search
  const filteredData = useMemo(() => {
    if (!file.data || !Array.isArray(file.data)) return [];
    if (!searchTerm) return file.data;

    return file.data.filter(row =>
      Array.isArray(row) && row.some(cell =>
        String(cell).toLowerCase().includes(searchTerm.toLowerCase())
      )
    );
  }, [file.data, searchTerm]);

  // Paginate data
  const paginatedData = useMemo(() => {
    if (!Array.isArray(filteredData)) return [];
    const startIndex = currentPage * rowsPerPage;
    return filteredData.slice(startIndex, startIndex + rowsPerPage);
  }, [filteredData, currentPage]);

  const totalPages = Math.ceil(filteredData.length / rowsPerPage);

  const getDataTypeIcon = (type: string) => {
    switch (type) {
      case 'number':
      case 'integer':
        return <Hash className="h-4 w-4 text-blue-500" />;
      case 'string':
      case 'categorical':
        return <Type className="h-4 w-4 text-green-500" />;
      case 'date':
        return <Calendar className="h-4 w-4 text-purple-500" />;
      case 'boolean':
        return <ToggleLeft className="h-4 w-4 text-orange-500" />;
      default:
        return <Database className="h-4 w-4 text-gray-500" />;
    }
  };

  const getDataTypeColor = (type: string) => {
    switch (type) {
      case 'number':
      case 'integer':
        return 'bg-blue-50 text-blue-700 border-blue-200';
      case 'string':
      case 'categorical':
        return 'bg-green-50 text-green-700 border-green-200';
      case 'date':
        return 'bg-purple-50 text-purple-700 border-purple-200';
      case 'boolean':
        return 'bg-orange-50 text-orange-700 border-orange-200';
      default:
        return 'bg-gray-50 text-gray-700 border-gray-200';
    }
  };

  return (
    <div className={`space-y-8 ${className}`}>
      {/* Header */}
      <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-green-50/50">
        <CardHeader>
          <CardTitle className="flex items-center gap-3 text-2xl">
            <div className="p-3 bg-gradient-to-r from-green-600 to-teal-600 rounded-xl">
              <Eye className="h-8 w-8 text-white" />
            </div>
            <div>
              <span className="bg-gradient-to-r from-green-600 to-teal-600 bg-clip-text text-transparent">
                Data Preview
              </span>
              <p className="text-sm font-normal text-gray-600 mt-1">
                Explore your data structure and content before analysis
              </p>
            </div>
          </CardTitle>
        </CardHeader>
      </Card>

      {/* Data Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-blue-100/50">
          <CardContent className="p-6 text-center">
            <div className="text-3xl font-bold text-blue-600 mb-2">{file.data.length}</div>
            <div className="text-sm font-medium text-blue-800">Total Rows</div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-green-100/50">
          <CardContent className="p-6 text-center">
            <div className="text-3xl font-bold text-green-600 mb-2">{file.headers.length}</div>
            <div className="text-sm font-medium text-green-800">Columns</div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-50 to-purple-100/50">
          <CardContent className="p-6 text-center">
            <div className="text-3xl font-bold text-purple-600 mb-2">
              {Object.values(dataAnalysis.dataTypes).filter(type => type === 'number' || type === 'integer').length}
            </div>
            <div className="text-sm font-medium text-purple-800">Numeric Columns</div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-orange-50 to-orange-100/50">
          <CardContent className="p-6 text-center">
            <div className="text-3xl font-bold text-orange-600 mb-2">
              {(file.size / 1024).toFixed(1)}KB
            </div>
            <div className="text-sm font-medium text-orange-800">File Size</div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="data" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3 h-12 bg-gray-100/50 rounded-xl">
          <TabsTrigger value="data" className="flex items-center gap-2 h-10">
            <Table className="h-4 w-4" />
            Data Table
          </TabsTrigger>
          <TabsTrigger value="columns" className="flex items-center gap-2 h-10">
            <Info className="h-4 w-4" />
            Column Info
          </TabsTrigger>
          <TabsTrigger value="stats" className="flex items-center gap-2 h-10">
            <BarChart3 className="h-4 w-4" />
            Statistics
          </TabsTrigger>
        </TabsList>

        {/* Data Table View */}
        <TabsContent value="data">
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Table className="h-5 w-5 text-blue-600" />
                  Data Table
                </CardTitle>
                <div className="flex items-center gap-2">
                  <div className="relative">
                    <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search data..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 pr-4 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                </div>
              </div>
            </CardHeader>
            
            <CardContent>
              <div className="border rounded-lg overflow-hidden">
                <ScrollArea className="w-full">
                  <div className="min-w-full">
                    {/* Table Header */}
                    <div className="bg-gray-50 border-b">
                      <div className="grid gap-4 p-4" style={{ gridTemplateColumns: `repeat(${file.headers.length}, minmax(120px, 1fr))` }}>
                        {file.headers.map((header, index) => (
                          <div key={index} className="font-semibold text-gray-900 text-sm">
                            <div className="flex items-center gap-2">
                              {getDataTypeIcon(dataAnalysis.dataTypes[header])}
                              <span className="truncate">{header}</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                    
                    {/* Table Body */}
                    <div className="divide-y divide-gray-200">
                      {paginatedData.map((row, rowIndex) => (
                        <div 
                          key={rowIndex} 
                          className="grid gap-4 p-4 hover:bg-gray-50 transition-colors"
                          style={{ gridTemplateColumns: `repeat(${file.headers.length}, minmax(120px, 1fr))` }}
                        >
                          {row.map((cell, cellIndex) => (
                            <div key={cellIndex} className="text-sm text-gray-700 truncate">
                              {cell === null || cell === '' ? (
                                <span className="text-gray-400 italic">null</span>
                              ) : (
                                String(cell)
                              )}
                            </div>
                          ))}
                        </div>
                      ))}
                    </div>
                  </div>
                </ScrollArea>
              </div>

              {/* Pagination */}
              <div className="flex items-center justify-between mt-4">
                <div className="text-sm text-gray-600">
                  Showing {currentPage * rowsPerPage + 1} to {Math.min((currentPage + 1) * rowsPerPage, filteredData.length)} of {filteredData.length} rows
                  {searchTerm && ` (filtered from ${file.data.length} total)`}
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}
                    disabled={currentPage === 0}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <span className="text-sm text-gray-600">
                    Page {currentPage + 1} of {totalPages}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(Math.min(totalPages - 1, currentPage + 1))}
                    disabled={currentPage === totalPages - 1}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Column Information */}
        <TabsContent value="columns">
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Info className="h-5 w-5 text-green-600" />
                Column Information
              </CardTitle>
            </CardHeader>
            
            <CardContent>
              <div className="space-y-4">
                {file.headers.map((header, index) => (
                  <div key={index} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <h3 className="font-semibold text-gray-900">{header}</h3>
                        <Badge className={getDataTypeColor(dataAnalysis.dataTypes[header])}>
                          {dataAnalysis.dataTypes[header]}
                        </Badge>
                      </div>
                      <div className="text-sm text-gray-500">
                        Column {index + 1}
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">Missing Values:</span>
                        <div className="font-medium">{dataAnalysis.missingValues[header] || 0}</div>
                      </div>
                      <div>
                        <span className="text-gray-500">Data Type:</span>
                        <div className="font-medium capitalize">{dataAnalysis.dataTypes[header]}</div>
                      </div>
                      <div>
                        <span className="text-gray-500">Sample Value:</span>
                        <div className="font-medium truncate">
                          {file.data[0] && file.data[0][index] !== null ? String(file.data[0][index]) : 'N/A'}
                        </div>
                      </div>
                      <div>
                        <span className="text-gray-500">Completeness:</span>
                        <div className="font-medium">
                          {(((file.data.length - (dataAnalysis.missingValues[header] || 0)) / file.data.length) * 100).toFixed(1)}%
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Statistics */}
        <TabsContent value="stats">
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-purple-600" />
                Basic Statistics
              </CardTitle>
            </CardHeader>
            
            <CardContent>
              <div className="space-y-6">
                {Object.entries(dataAnalysis.basicStats).map(([column, stats]) => (
                  <div key={column} className="p-4 border rounded-lg">
                    <h3 className="font-semibold text-gray-900 mb-3">{column}</h3>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      {Object.entries(stats as any).map(([key, value]) => (
                        <div key={key}>
                          <span className="text-gray-500 capitalize">{key.replace('_', ' ')}:</span>
                          <div className="font-medium">
                            {typeof value === 'number' ? value.toFixed(2) : String(value)}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
