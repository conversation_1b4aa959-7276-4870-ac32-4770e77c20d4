import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import {
  Mic,
  Upload,
  Link,
  FileText,
  Play,
  Pause,
  Download,
  Settings,
  History,
  Sparkles,
  Volume2,
  Clock,
  Users
} from "lucide-react";

import {
  PodcastGeneratorState,
  PodcastGeneration,
  VoiceOption,
  AudioProvider,
  PodcastInputSource
} from './types';
import {
  DEFAULT_AUDIO_PROVIDERS,
  FISH_AUDIO_VOICES,
  GEMINI_VOICES,
  SUPPORTED_LANGUAGES,
  INPUT_TABS,
  DEFAULT_PODCAST_SETTINGS
} from './constants';

// Import components
import { TopicInput } from './components/TopicInput';
import { LinkInput } from './components/LinkInput';
import { FileUploader } from './components/FileUploader';
import { TextInput } from './components/TextInput';
import { VoiceSelector } from './components/VoiceSelector';
import { PodcastPlayer } from './components/PodcastPlayer';
import { GenerationProgress } from './components/GenerationProgress';
import { PodcastHistory } from './components/PodcastHistory';

// Import services
import {
  PodcastGenerationService,
  ContentProcessingService,
  PodcastStorageService
} from './services';

export function AIPodcastGenerator() {
  const [state, setState] = useState<PodcastGeneratorState>({
    currentTab: 'topic',
    inputContent: '',
    selectedVoices: {},
    outputLanguage: 'en',
    isGenerating: false
  });

  const [availableProviders, setAvailableProviders] = useState<AudioProvider[]>(DEFAULT_AUDIO_PROVIDERS);
  const [availableVoices, setAvailableVoices] = useState<VoiceOption[]>([
    ...FISH_AUDIO_VOICES,
    ...GEMINI_VOICES
  ]);
  const [showHistory, setShowHistory] = useState(false);

  // Initialize services
  const [podcastService] = useState(() => new PodcastGenerationService());
  const [contentService] = useState(() => new ContentProcessingService());
  const [storageService] = useState(() => new PodcastStorageService());

  // Initialize default voices
  useEffect(() => {
    if (availableVoices.length > 0 && !state.selectedVoices.host1) {
      const maleVoice = availableVoices.find(v => v.gender === 'male');
      const femaleVoice = availableVoices.find(v => v.gender === 'female');
      
      setState(prev => ({
        ...prev,
        selectedVoices: {
          host1: maleVoice || availableVoices[0],
          host2: femaleVoice || availableVoices[1]
        },
        selectedProvider: availableProviders[0]
      }));
    }
  }, [availableVoices, availableProviders]);

  const handleTabChange = (tab: string) => {
    setState(prev => ({
      ...prev,
      currentTab: tab as PodcastGeneratorState['currentTab'],
      inputContent: ''
    }));
  };

  const handleInputChange = (content: string, metadata?: any) => {
    setState(prev => ({
      ...prev,
      inputContent: content
    }));
  };

  const handleVoiceSelection = (hostNumber: 'host1' | 'host2', voice: VoiceOption) => {
    setState(prev => ({
      ...prev,
      selectedVoices: {
        ...prev.selectedVoices,
        [hostNumber]: voice
      }
    }));
  };

  const handleProviderChange = (provider: AudioProvider) => {
    setState(prev => ({
      ...prev,
      selectedProvider: provider
    }));
  };

  const handleLanguageChange = (language: string) => {
    setState(prev => ({
      ...prev,
      outputLanguage: language
    }));
  };

  const validateInputs = (): boolean => {
    if (!state.inputContent.trim()) {
      toast.error('Please provide content for the podcast');
      return false;
    }

    if (!state.selectedVoices.host1 || !state.selectedVoices.host2) {
      toast.error('Please select voices for both hosts');
      return false;
    }

    if (!state.selectedProvider) {
      toast.error('Please select an audio provider');
      return false;
    }

    return true;
  };

  const handleGeneratePodcast = async () => {
    if (!validateInputs()) return;

    setState(prev => ({ ...prev, isGenerating: true }));

    try {
      const inputSource: PodcastInputSource = {
        type: state.currentTab,
        content: state.inputContent
      };

      // Process content first
      const processedContent = await contentService.processContent(inputSource);
      inputSource.content = processedContent;

      toast.success('Podcast generation started!');

      // Generate podcast using the service
      const podcast = await podcastService.generatePodcast(
        inputSource,
        {
          host1: state.selectedVoices.host1!,
          host2: state.selectedVoices.host2!
        },
        state.selectedProvider!,
        state.outputLanguage,
        (progress) => {
          setState(prev => ({
            ...prev,
            generationProgress: progress
          }));
        }
      );

      // Save to storage
      await storageService.savePodcast(podcast);

      // Update state with generated podcast
      setState(prev => ({
        ...prev,
        currentPodcast: podcast,
        generationProgress: undefined
      }));

      toast.success('Podcast generated successfully!');

    } catch (error) {
      console.error('Error generating podcast:', error);
      toast.error('Failed to generate podcast. Please try again.');
      setState(prev => ({
        ...prev,
        generationProgress: undefined
      }));
    } finally {
      setState(prev => ({ ...prev, isGenerating: false }));
    }
  };

  const canGenerate = state.inputContent.trim() && 
                     state.selectedVoices.host1 && 
                     state.selectedVoices.host2 && 
                     state.selectedProvider &&
                     !state.isGenerating;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 py-8">
      <div className="max-w-7xl mx-auto px-6">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="p-3 bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl">
              <Mic className="h-8 w-8 text-white" />
            </div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
              AI Podcast Generator
            </h1>
          </div>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Create professional two-person podcasts from any source using our advanced AI Podcast Generator technology.
          </p>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Input and Configuration */}
          <div className="lg:col-span-2 space-y-6">
            {/* Input Tabs */}
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Upload className="h-5 w-5" />
                  Content Input
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Tabs value={state.currentTab} onValueChange={handleTabChange}>
                  <TabsList className="grid w-full grid-cols-4 mb-6">
                    {INPUT_TABS.map((tab) => (
                      <TabsTrigger 
                        key={tab.id} 
                        value={tab.id}
                        className="flex items-center gap-2"
                      >
                        <span>{tab.icon}</span>
                        {tab.name}
                      </TabsTrigger>
                    ))}
                  </TabsList>

                  <TabsContent value="topic">
                    <TopicInput 
                      value={state.inputContent}
                      onChange={handleInputChange}
                    />
                  </TabsContent>

                  <TabsContent value="link">
                    <LinkInput 
                      value={state.inputContent}
                      onChange={handleInputChange}
                    />
                  </TabsContent>

                  <TabsContent value="file">
                    <FileUploader 
                      onUpload={handleInputChange}
                    />
                  </TabsContent>

                  <TabsContent value="text">
                    <TextInput 
                      value={state.inputContent}
                      onChange={handleInputChange}
                    />
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>

            {/* Voice and Provider Selection */}
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Voice Configuration
                </CardTitle>
              </CardHeader>
              <CardContent>
                <VoiceSelector
                  availableVoices={availableVoices}
                  availableProviders={availableProviders}
                  selectedVoices={state.selectedVoices}
                  selectedProvider={state.selectedProvider}
                  outputLanguage={state.outputLanguage}
                  supportedLanguages={SUPPORTED_LANGUAGES}
                  onVoiceSelect={handleVoiceSelection}
                  onProviderChange={handleProviderChange}
                  onLanguageChange={handleLanguageChange}
                />
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Controls and Status */}
          <div className="space-y-6">
            {/* Generation Controls */}
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Sparkles className="h-5 w-5" />
                  Generate Podcast
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button
                  onClick={handleGeneratePodcast}
                  disabled={!canGenerate}
                  className="w-full bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600"
                  size="lg"
                >
                  {state.isGenerating ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Mic className="h-4 w-4 mr-2" />
                      Create Podcast
                    </>
                  )}
                </Button>

                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    onClick={() => setShowHistory(!showHistory)}
                    className="flex-1"
                  >
                    <History className="h-4 w-4 mr-2" />
                    History
                  </Button>
                  <Button variant="outline" className="flex-1">
                    <Settings className="h-4 w-4 mr-2" />
                    Settings
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Generation Progress */}
            {state.generationProgress && (
              <Card className="border-0 shadow-lg">
                <CardContent className="pt-6">
                  <GenerationProgress progress={state.generationProgress} />
                </CardContent>
              </Card>
            )}

            {/* Current Podcast Player */}
            {state.currentPodcast && (
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Volume2 className="h-5 w-5" />
                    Generated Podcast
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <PodcastPlayer podcast={state.currentPodcast} />
                </CardContent>
              </Card>
            )}
          </div>
        </div>

        {/* History Panel */}
        {showHistory && (
          <div className="mt-8">
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <History className="h-5 w-5" />
                  Podcast History
                </CardTitle>
              </CardHeader>
              <CardContent>
                <PodcastHistory onSelectPodcast={(podcast) => {
                  setState(prev => ({ ...prev, currentPodcast: podcast }));
                  setShowHistory(false);
                }} />
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
}
