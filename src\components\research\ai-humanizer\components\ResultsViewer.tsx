/**
 * Results Viewer Component
 * Displays the humanized content and analysis results
 */

import React, { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Sparkles,
  Copy,
  Download,
  Edit3,
  Eye,
  BarChart3,
  FileText,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  Share2
} from "lucide-react";
import { toast } from 'sonner';

import { useHumanizerStore } from '../stores/humanizer.store';
import { StyleAnalysisViewer } from './StyleAnalysisViewer';

export function ResultsViewer() {
  const {
    originalContent,
    humanizedContent,
    styleAnalysis,
    sampleTexts,
    setHumanizedContent,
    canProcessContent
  } = useHumanizerStore();

  const [isEditing, setIsEditing] = useState(false);
  const [editedContent, setEditedContent] = useState(humanizedContent);
  const [activeView, setActiveView] = useState<'side-by-side' | 'original' | 'humanized'>('side-by-side');

  /**
   * Handle copying content to clipboard
   */
  const handleCopyContent = async (content: string, type: string) => {
    try {
      await navigator.clipboard.writeText(content);
      toast.success(`${type} content copied to clipboard`);
    } catch (error) {
      toast.error('Failed to copy content');
    }
  };

  /**
   * Handle saving edited content
   */
  const handleSaveEdit = () => {
    setHumanizedContent(editedContent);
    setIsEditing(false);
    toast.success('Changes saved successfully');
  };

  /**
   * Handle canceling edit
   */
  const handleCancelEdit = () => {
    setEditedContent(humanizedContent);
    setIsEditing(false);
  };

  /**
   * Handle downloading content
   */
  const handleDownload = (content: string, filename: string) => {
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    toast.success('Content downloaded successfully');
  };

  /**
   * Calculate content statistics
   */
  const getContentStats = (content: string) => {
    const words = content.trim().split(/\s+/).length;
    const characters = content.length;
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0).length;
    const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim().length > 0).length;
    
    return { words, characters, sentences, paragraphs };
  };

  const originalStats = originalContent ? getContentStats(originalContent) : null;
  const humanizedStats = humanizedContent ? getContentStats(humanizedContent) : null;

  // Show empty state if no results
  if (!humanizedContent) {
    return (
      <div className="space-y-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-800 mb-2">Results</h2>
          <p className="text-gray-600">
            Your humanized content will appear here after processing.
          </p>
        </div>

        <Card className="border-dashed border-2 border-gray-300">
          <CardContent className="flex flex-col items-center justify-center py-12 text-center">
            <Sparkles className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-semibold text-gray-600 mb-2">
              No results yet
            </h3>
            <p className="text-gray-500 mb-4 max-w-md">
              {!canProcessContent()
                ? "Add sample texts and content to get started with humanization."
                : "Click 'Humanize Content' to process your content with your writing style."
              }
            </p>
            {canProcessContent() && (
              <Alert className="max-w-md">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Go back to the Content tab and click "Humanize Content" to start processing.
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-800 mb-2">Results</h2>
          <p className="text-gray-600">
            Your content has been successfully humanized using your writing style.
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleCopyContent(humanizedContent, 'Humanized')}
          >
            <Copy className="h-4 w-4 mr-2" />
            Copy Result
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleDownload(humanizedContent, 'humanized-content.txt')}
          >
            <Download className="h-4 w-4 mr-2" />
            Download
          </Button>
        </div>
      </div>

      {/* Content Comparison */}
      <Card className="shadow-lg">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg flex items-center gap-2">
              <Eye className="h-5 w-5 text-blue-600" />
              Content Comparison
            </CardTitle>
            <div className="flex gap-2">
              <Button
                variant={activeView === 'side-by-side' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setActiveView('side-by-side')}
              >
                Side by Side
              </Button>
              <Button
                variant={activeView === 'original' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setActiveView('original')}
              >
                Original
              </Button>
              <Button
                variant={activeView === 'humanized' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setActiveView('humanized')}
              >
                Humanized
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          {activeView === 'side-by-side' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Original Content */}
              <div>
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-semibold text-gray-700">Original Content</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleCopyContent(originalContent, 'Original')}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
                <div className="bg-gray-50 p-4 rounded-lg max-h-96 overflow-y-auto">
                  <pre className="whitespace-pre-wrap text-sm text-gray-700 font-sans">
                    {originalContent}
                  </pre>
                </div>
                {originalStats && (
                  <div className="flex gap-4 mt-2 text-xs text-gray-500">
                    <span>{originalStats.words} words</span>
                    <span>{originalStats.sentences} sentences</span>
                    <span>{originalStats.paragraphs} paragraphs</span>
                  </div>
                )}
              </div>

              {/* Humanized Content */}
              <div>
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-semibold text-gray-700">Humanized Content</h3>
                  <div className="flex gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setIsEditing(!isEditing)}
                    >
                      <Edit3 className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleCopyContent(humanizedContent, 'Humanized')}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                
                {isEditing ? (
                  <div className="space-y-3">
                    <Textarea
                      value={editedContent}
                      onChange={(e) => setEditedContent(e.target.value)}
                      rows={12}
                      className="resize-none"
                    />
                    <div className="flex gap-2">
                      <Button size="sm" onClick={handleSaveEdit}>
                        <CheckCircle className="h-4 w-4 mr-2" />
                        Save
                      </Button>
                      <Button variant="outline" size="sm" onClick={handleCancelEdit}>
                        Cancel
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="bg-blue-50 p-4 rounded-lg max-h-96 overflow-y-auto border-2 border-blue-200">
                    <pre className="whitespace-pre-wrap text-sm text-gray-700 font-sans">
                      {humanizedContent}
                    </pre>
                  </div>
                )}
                
                {humanizedStats && (
                  <div className="flex gap-4 mt-2 text-xs text-gray-500">
                    <span>{humanizedStats.words} words</span>
                    <span>{humanizedStats.sentences} sentences</span>
                    <span>{humanizedStats.paragraphs} paragraphs</span>
                  </div>
                )}
              </div>
            </div>
          )}

          {activeView === 'original' && (
            <div>
              <div className="bg-gray-50 p-6 rounded-lg">
                <pre className="whitespace-pre-wrap text-sm text-gray-700 font-sans">
                  {originalContent}
                </pre>
              </div>
              {originalStats && (
                <div className="flex gap-6 mt-4 text-sm text-gray-600">
                  <span><strong>{originalStats.words}</strong> words</span>
                  <span><strong>{originalStats.sentences}</strong> sentences</span>
                  <span><strong>{originalStats.paragraphs}</strong> paragraphs</span>
                  <span><strong>{originalStats.characters}</strong> characters</span>
                </div>
              )}
            </div>
          )}

          {activeView === 'humanized' && (
            <div>
              {isEditing ? (
                <div className="space-y-3">
                  <Textarea
                    value={editedContent}
                    onChange={(e) => setEditedContent(e.target.value)}
                    rows={16}
                    className="resize-none"
                  />
                  <div className="flex gap-2">
                    <Button onClick={handleSaveEdit}>
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Save Changes
                    </Button>
                    <Button variant="outline" onClick={handleCancelEdit}>
                      Cancel
                    </Button>
                  </div>
                </div>
              ) : (
                <div>
                  <div className="bg-blue-50 p-6 rounded-lg border-2 border-blue-200">
                    <pre className="whitespace-pre-wrap text-sm text-gray-700 font-sans">
                      {humanizedContent}
                    </pre>
                  </div>
                  <div className="flex items-center justify-between mt-4">
                    {humanizedStats && (
                      <div className="flex gap-6 text-sm text-gray-600">
                        <span><strong>{humanizedStats.words}</strong> words</span>
                        <span><strong>{humanizedStats.sentences}</strong> sentences</span>
                        <span><strong>{humanizedStats.paragraphs}</strong> paragraphs</span>
                        <span><strong>{humanizedStats.characters}</strong> characters</span>
                      </div>
                    )}
                    <Button variant="outline" onClick={() => setIsEditing(true)}>
                      <Edit3 className="h-4 w-4 mr-2" />
                      Edit
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Style Analysis Results */}
      {styleAnalysis && (
        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-purple-600" />
              Style Analysis
            </CardTitle>
            <CardDescription>
              Analysis of your writing style based on {sampleTexts.length} sample text(s)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <StyleAnalysisViewer analysis={styleAnalysis} />
          </CardContent>
        </Card>
      )}

      {/* Quick Actions */}
      <Card className="bg-green-50 border-green-200">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            What's Next?
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button
              variant="outline"
              className="h-auto p-4 justify-start"
              onClick={() => handleCopyContent(humanizedContent, 'Humanized')}
            >
              <div className="text-left">
                <div className="font-medium flex items-center gap-2">
                  <Copy className="h-4 w-4" />
                  Copy Result
                </div>
                <div className="text-sm text-gray-500 mt-1">
                  Copy to use elsewhere
                </div>
              </div>
            </Button>

            <Button
              variant="outline"
              className="h-auto p-4 justify-start"
              onClick={() => handleDownload(humanizedContent, 'humanized-content.txt')}
            >
              <div className="text-left">
                <div className="font-medium flex items-center gap-2">
                  <Download className="h-4 w-4" />
                  Download
                </div>
                <div className="text-sm text-gray-500 mt-1">
                  Save as text file
                </div>
              </div>
            </Button>

            <Button
              variant="outline"
              className="h-auto p-4 justify-start"
              onClick={() => setIsEditing(true)}
            >
              <div className="text-left">
                <div className="font-medium flex items-center gap-2">
                  <Edit3 className="h-4 w-4" />
                  Edit Result
                </div>
                <div className="text-sm text-gray-500 mt-1">
                  Make manual adjustments
                </div>
              </div>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
