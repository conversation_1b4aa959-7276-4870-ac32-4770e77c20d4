import React, { memo, useCallback, useMemo } from 'react';
import {
  GPTVis,
  ChartType,
  Line,
  Column,
  Pie,
  Area,
  Bar,
  Histogram,
  Scatter,
  WordCloud,
  Treemap,
  DualAxes,
  Radar,
  PinMap,
  PathMap,
  HeatMap,
  MindMap,
  FlowDiagram,
  NetworkGraph,
  OrganizationChart,
  IndentedTree,
  FishboneDiagram,
  VisText,
  withDefaultChartCode
} from '@antv/gpt-vis';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Download, Copy, Maximize2 } from 'lucide-react';
import { toast } from 'sonner';

export interface GPTVisChartProps {
  chartType: ChartType;
  data: any[];
  config?: any;
  title?: string;
  description?: string;
  className?: string;
  onExport?: (format: 'png' | 'svg' | 'pdf') => void;
  onFullscreen?: () => void;
}

// Enhanced chart components mapping
const CHART_COMPONENTS = {
  [ChartType.Line]: Line,
  [ChartType.Column]: Column,
  [ChartType.Pie]: Pie,
  [ChartType.Area]: Area,
  [ChartType.Bar]: Bar,
  [ChartType.Histogram]: Histogram,
  [ChartType.Scatter]: Scatter,
  [ChartType.WordCloud]: WordCloud,
  [ChartType.Treemap]: Treemap,
  [ChartType.DualAxes]: DualAxes,
  [ChartType.Radar]: Radar,
  [ChartType.PinMap]: PinMap,
  [ChartType.PathMap]: PathMap,
  [ChartType.HeatMap]: HeatMap,
  [ChartType.MindMap]: MindMap,
  [ChartType.FlowDiagram]: FlowDiagram,
  [ChartType.NetworkGraph]: NetworkGraph,
  [ChartType.OrganizationChart]: OrganizationChart,
  [ChartType.IndentedTree]: IndentedTree,
  [ChartType.FishboneDiagram]: FishboneDiagram,
  [ChartType.VisText]: VisText,
};

const GPTVisChart: React.FC<GPTVisChartProps> = ({
  chartType,
  data,
  config = {},
  title,
  description,
  className = '',
  onExport,
  onFullscreen
}) => {
  // Get the appropriate chart component
  const ChartComponent = CHART_COMPONENTS[chartType];

  // Generate chart specification
  const chartSpec = useMemo(() => {
    const baseSpec = {
      type: chartType,
      data,
      ...config
    };

    // Add chart-specific configurations
    switch (chartType) {
      case ChartType.Line:
      case ChartType.Area:
        return {
          ...baseSpec,
          xField: config.xField || 'x',
          yField: config.yField || 'y',
          smooth: config.smooth || false,
        };
      
      case ChartType.Column:
      case ChartType.Bar:
        return {
          ...baseSpec,
          xField: config.xField || 'x',
          yField: config.yField || 'y',
          colorField: config.colorField,
        };
      
      case ChartType.Pie:
        return {
          ...baseSpec,
          angleField: config.angleField || 'value',
          colorField: config.colorField || 'category',
        };
      
      case ChartType.Scatter:
        return {
          ...baseSpec,
          xField: config.xField || 'x',
          yField: config.yField || 'y',
          sizeField: config.sizeField,
          colorField: config.colorField,
        };
      
      case ChartType.WordCloud:
        return {
          ...baseSpec,
          wordField: config.wordField || 'word',
          weightField: config.weightField || 'weight',
        };
      
      case ChartType.Treemap:
        return {
          ...baseSpec,
          valueField: config.valueField || 'value',
          colorField: config.colorField || 'category',
        };
      
      default:
        return baseSpec;
    }
  }, [chartType, data, config]);

  // Handle copy chart specification
  const handleCopySpec = useCallback(() => {
    const specText = JSON.stringify(chartSpec, null, 2);
    navigator.clipboard.writeText(specText).then(() => {
      toast.success('Chart specification copied to clipboard');
    }).catch(() => {
      toast.error('Failed to copy chart specification');
    });
  }, [chartSpec]);

  // Handle export
  const handleExport = useCallback((format: 'png' | 'svg' | 'pdf') => {
    if (onExport) {
      onExport(format);
    } else {
      toast.info(`Export to ${format.toUpperCase()} functionality coming soon`);
    }
  }, [onExport]);

  if (!ChartComponent) {
    return (
      <Card className={`w-full ${className}`}>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            <p>Chart type "{chartType}" is not supported</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`w-full ${className}`}>
      {(title || description) && (
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div>
              {title && <CardTitle className="text-lg font-semibold">{title}</CardTitle>}
              {description && <p className="text-sm text-muted-foreground mt-1">{description}</p>}
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleCopySpec}
                className="h-8 w-8 p-0"
              >
                <Copy className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleExport('png')}
                className="h-8 w-8 p-0"
              >
                <Download className="h-4 w-4" />
              </Button>
              {onFullscreen && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onFullscreen}
                  className="h-8 w-8 p-0"
                >
                  <Maximize2 className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
      )}
      <CardContent className="p-6">
        <div className="w-full min-h-[300px]">
          <ChartComponent {...chartSpec} />
        </div>
      </CardContent>
    </Card>
  );
};

const MemoizedGPTVisChart = memo(GPTVisChart);

// Export both as named and default for flexibility
export { MemoizedGPTVisChart as GPTVisChart };
export default MemoizedGPTVisChart;
