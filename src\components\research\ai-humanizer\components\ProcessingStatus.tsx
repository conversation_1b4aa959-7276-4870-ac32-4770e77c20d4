/**
 * Processing Status Component
 * Shows real-time processing status and progress
 */

import React from 'react';
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import {
  Loader2,
  Brain,
  Wand2,
  CheckCircle,
  Clock,
  Sparkles
} from "lucide-react";

import { useHumanizerStore } from '../stores/humanizer.store';
import { PROCESSING_STEPS } from '../constants';

export function ProcessingStatus() {
  const { processingStatus, isProcessing } = useHumanizerStore();

  if (!isProcessing && processingStatus.state === 'idle') {
    return null;
  }

  /**
   * Get icon for current processing state
   */
  const getStateIcon = () => {
    switch (processingStatus.state) {
      case 'analyzing-samples':
        return <Brain className="h-5 w-5 text-blue-600 animate-pulse" />;
      case 'generating-profile':
        return <Wand2 className="h-5 w-5 text-purple-600 animate-pulse" />;
      case 'humanizing-content':
        return <Sparkles className="h-5 w-5 text-green-600 animate-pulse" />;
      case 'complete':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      default:
        return <Loader2 className="h-5 w-5 text-blue-600 animate-spin" />;
    }
  };

  /**
   * Get status color based on state
   */
  const getStatusColor = () => {
    switch (processingStatus.state) {
      case 'analyzing-samples':
        return 'bg-blue-50 border-blue-200';
      case 'generating-profile':
        return 'bg-purple-50 border-purple-200';
      case 'humanizing-content':
        return 'bg-green-50 border-green-200';
      case 'complete':
        return 'bg-green-50 border-green-200';
      case 'error':
        return 'bg-red-50 border-red-200';
      default:
        return 'bg-blue-50 border-blue-200';
    }
  };

  /**
   * Get step name from constants
   */
  const getStepName = () => {
    switch (processingStatus.state) {
      case 'analyzing-samples':
        return PROCESSING_STEPS.ANALYZING_SAMPLES.name;
      case 'generating-profile':
        return PROCESSING_STEPS.GENERATING_PROFILE.name;
      case 'humanizing-content':
        return PROCESSING_STEPS.HUMANIZING_CONTENT.name;
      case 'complete':
        return 'Processing Complete';
      default:
        return 'Processing...';
    }
  };

  /**
   * Format time remaining
   */
  const formatTimeRemaining = (seconds?: number) => {
    if (!seconds || seconds <= 0) return '';
    
    if (seconds < 60) {
      return `${seconds}s remaining`;
    } else {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes}m ${remainingSeconds}s remaining`;
    }
  };

  return (
    <div className={`p-6 rounded-lg border-2 ${getStatusColor()}`}>
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {getStateIcon()}
            <div>
              <h3 className="font-semibold text-gray-800">
                {getStepName()}
              </h3>
              <p className="text-sm text-gray-600">
                {processingStatus.currentStep || 'Processing your request...'}
              </p>
            </div>
          </div>
          
          {processingStatus.state !== 'complete' && (
            <Badge variant="secondary" className="animate-pulse">
              Processing
            </Badge>
          )}
          
          {processingStatus.state === 'complete' && (
            <Badge className="bg-green-100 text-green-800">
              Complete
            </Badge>
          )}
        </div>

        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-gray-700">
              Progress
            </span>
            <span className="text-sm text-gray-600">
              {Math.round(processingStatus.progress)}%
            </span>
          </div>
          <Progress 
            value={processingStatus.progress} 
            className="h-3"
          />
        </div>

        {/* Time Remaining */}
        {processingStatus.estimatedTimeRemaining && processingStatus.estimatedTimeRemaining > 0 && (
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Clock className="h-4 w-4" />
            <span>{formatTimeRemaining(processingStatus.estimatedTimeRemaining)}</span>
          </div>
        )}

        {/* Processing Steps Indicator */}
        <div className="grid grid-cols-4 gap-2 mt-4">
          {[
            { key: 'analyzing-samples', label: 'Analyze', icon: Brain },
            { key: 'generating-profile', label: 'Profile', icon: Wand2 },
            { key: 'humanizing-content', label: 'Humanize', icon: Sparkles },
            { key: 'complete', label: 'Complete', icon: CheckCircle }
          ].map((step, index) => {
            const Icon = step.icon;
            const isActive = processingStatus.state === step.key;
            const isCompleted = ['analyzing-samples', 'generating-profile', 'humanizing-content', 'complete']
              .indexOf(processingStatus.state) > index;
            const isCurrent = processingStatus.state === step.key;

            return (
              <div
                key={step.key}
                className={`flex flex-col items-center p-2 rounded-lg text-center transition-all ${
                  isCompleted || isCurrent
                    ? 'bg-white shadow-sm border'
                    : 'bg-gray-100'
                }`}
              >
                <Icon
                  className={`h-4 w-4 mb-1 ${
                    isCompleted
                      ? 'text-green-600'
                      : isCurrent
                      ? 'text-blue-600 animate-pulse'
                      : 'text-gray-400'
                  }`}
                />
                <span
                  className={`text-xs font-medium ${
                    isCompleted
                      ? 'text-green-700'
                      : isCurrent
                      ? 'text-blue-700'
                      : 'text-gray-500'
                  }`}
                >
                  {step.label}
                </span>
              </div>
            );
          })}
        </div>

        {/* Additional Info */}
        {processingStatus.state === 'analyzing-samples' && (
          <div className="text-xs text-blue-700 bg-blue-100 p-2 rounded">
            💡 Analyzing your writing samples to understand your unique style and voice patterns...
          </div>
        )}

        {processingStatus.state === 'generating-profile' && (
          <div className="text-xs text-purple-700 bg-purple-100 p-2 rounded">
            🧠 Creating a personalized style profile based on your writing characteristics...
          </div>
        )}

        {processingStatus.state === 'humanizing-content' && (
          <div className="text-xs text-green-700 bg-green-100 p-2 rounded">
            ✨ Rewriting your content to match your personal writing style and voice...
          </div>
        )}

        {processingStatus.state === 'complete' && (
          <div className="text-xs text-green-700 bg-green-100 p-2 rounded">
            🎉 Your content has been successfully humanized! Check the Results tab to see the output.
          </div>
        )}
      </div>
    </div>
  );
}
