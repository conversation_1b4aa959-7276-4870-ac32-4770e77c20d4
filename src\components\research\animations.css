/* Subtle Animation System for Academic Writing Editor */

/* Button Animations - Subtle and Professional */
.tool-button {
  transition: all 0.2s ease-in-out;
  transform: scale(1);
}

.tool-button:hover {
  transform: scale(1.02);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tool-button:active {
  transform: scale(0.98);
}

/* Panel Transitions - Smooth and Clean */
.panel-enter {
  opacity: 0;
  transform: translateY(-10px);
}

.panel-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: all 0.3s ease-out;
}

.panel-exit {
  opacity: 1;
  transform: translateY(0);
}

.panel-exit-active {
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.2s ease-in;
}

/* AI Assistant Animations */
.ai-assistant-expand {
  animation: expandDown 0.3s ease-out;
}

.ai-assistant-collapse {
  animation: collapseUp 0.2s ease-in;
}

@keyframes expandDown {
  from {
    opacity: 0;
    max-height: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    max-height: 500px;
    transform: translateY(0);
  }
}

@keyframes collapseUp {
  from {
    opacity: 1;
    max-height: 500px;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    max-height: 0;
    transform: translateY(-10px);
  }
}

/* Floating Toolbar Animations */
.floating-toolbar-enter {
  opacity: 0;
  transform: translateY(10px) scale(0.95);
}

.floating-toolbar-enter-active {
  opacity: 1;
  transform: translateY(0) scale(1);
  transition: all 0.2s ease-out;
}

.floating-toolbar-exit {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.floating-toolbar-exit-active {
  opacity: 0;
  transform: translateY(10px) scale(0.95);
  transition: all 0.15s ease-in;
}

/* Popup Modal Animations */
.popup-backdrop-enter {
  opacity: 0;
}

.popup-backdrop-enter-active {
  opacity: 1;
  transition: opacity 0.2s ease-out;
}

.popup-backdrop-exit {
  opacity: 1;
}

.popup-backdrop-exit-active {
  opacity: 0;
  transition: opacity 0.15s ease-in;
}

.popup-content-enter {
  opacity: 0;
  transform: scale(0.95) translateY(-20px);
}

.popup-content-enter-active {
  opacity: 1;
  transform: scale(1) translateY(0);
  transition: all 0.3s ease-out;
}

.popup-content-exit {
  opacity: 1;
  transform: scale(1) translateY(0);
}

.popup-content-exit-active {
  opacity: 0;
  transform: scale(0.95) translateY(-20px);
  transition: all 0.2s ease-in;
}

/* Formatting Toolbar Toggle Animation */
.toolbar-slide-down {
  animation: slideDown 0.3s ease-out;
}

.toolbar-slide-up {
  animation: slideUp 0.2s ease-in;
}

@keyframes slideDown {
  from {
    opacity: 0;
    max-height: 0;
    transform: translateY(-100%);
  }
  to {
    opacity: 1;
    max-height: 60px;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 1;
    max-height: 60px;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    max-height: 0;
    transform: translateY(-100%);
  }
}

/* Loading Animations - Subtle Pulse */
.ai-loading-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Icon Animations */
.icon-rotate {
  transition: transform 0.2s ease-in-out;
}

.icon-rotate:hover {
  transform: rotate(5deg);
}

.icon-bounce {
  animation: bounce 0.5s ease-in-out;
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -5px, 0);
  }
  70% {
    transform: translate3d(0, -3px, 0);
  }
  90% {
    transform: translate3d(0, -1px, 0);
  }
}

/* Text Selection Bubble Animations */
.selection-bubble-enter {
  opacity: 0;
  transform: translateY(5px) scale(0.9);
}

.selection-bubble-enter-active {
  opacity: 1;
  transform: translateY(0) scale(1);
  transition: all 0.2s ease-out;
}

.selection-bubble-exit {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.selection-bubble-exit-active {
  opacity: 0;
  transform: translateY(5px) scale(0.9);
  transition: all 0.15s ease-in;
}

/* Search Panel Animations */
.search-panel-slide-in {
  animation: slideInFromRight 0.3s ease-out;
}

.search-panel-slide-out {
  animation: slideOutToRight 0.2s ease-in;
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideOutToRight {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(100%);
  }
}

/* Hover Effects for Interactive Elements */
.interactive-hover {
  transition: all 0.2s ease-in-out;
}

.interactive-hover:hover {
  background-color: rgba(59, 130, 246, 0.05);
  border-color: rgba(59, 130, 246, 0.2);
}

/* Focus Animations */
.focus-ring {
  transition: box-shadow 0.2s ease-in-out;
}

.focus-ring:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Success/Error State Animations */
.success-flash {
  animation: successFlash 0.6s ease-out;
}

.error-shake {
  animation: errorShake 0.5s ease-in-out;
}

@keyframes successFlash {
  0% { background-color: transparent; }
  50% { background-color: rgba(34, 197, 94, 0.1); }
  100% { background-color: transparent; }
}

@keyframes errorShake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
  20%, 40%, 60%, 80% { transform: translateX(2px); }
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
