import { ChartType } from '@antv/gpt-vis';
import { toast } from 'sonner';

export interface ExportOptions {
  format: 'png' | 'svg' | 'pdf' | 'csv' | 'json' | 'excel';
  filename?: string;
  quality?: number; // For image exports (0.1 - 1.0)
  width?: number;
  height?: number;
  includeData?: boolean;
  includeMetadata?: boolean;
}

export interface ExportData {
  chartType: ChartType;
  data: any[];
  config: any;
  title?: string;
  description?: string;
  timestamp: Date;
}

export class ExportService {
  /**
   * Export chart as image (PNG/SVG)
   */
  static async exportAsImage(
    chartElement: HTMLElement,
    options: ExportOptions
  ): Promise<void> {
    try {
      const { format = 'png', filename, quality = 1.0, width, height } = options;
      
      // Find the chart container (usually an SVG or Canvas)
      const chartContainer = chartElement.querySelector('svg, canvas') as HTMLElement;
      if (!chartContainer) {
        throw new Error('No chart element found for export');
      }

      if (format === 'svg' && chartContainer.tagName.toLowerCase() === 'svg') {
        // Export SVG directly
        await this.exportSVG(chartContainer as SVGElement, filename);
      } else {
        // Convert to canvas and export as PNG
        await this.exportPNG(chartContainer, filename, quality, width, height);
      }

      toast.success(`Chart exported as ${format.toUpperCase()}`);
    } catch (error) {
      console.error('Export error:', error);
      toast.error(`Failed to export chart: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Export data as CSV
   */
  static async exportAsCSV(data: any[], filename?: string): Promise<void> {
    try {
      if (!data || data.length === 0) {
        throw new Error('No data to export');
      }

      // Get headers from first object
      const headers = Object.keys(data[0]);
      
      // Create CSV content
      const csvContent = [
        headers.join(','), // Header row
        ...data.map(row => 
          headers.map(header => {
            const value = row[header];
            // Escape commas and quotes in values
            if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
              return `"${value.replace(/"/g, '""')}"`;
            }
            return value ?? '';
          }).join(',')
        )
      ].join('\n');

      // Download file
      this.downloadFile(csvContent, filename || 'data.csv', 'text/csv');
      toast.success('Data exported as CSV');
    } catch (error) {
      console.error('CSV export error:', error);
      toast.error(`Failed to export CSV: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Export data as JSON
   */
  static async exportAsJSON(exportData: ExportData, options: ExportOptions): Promise<void> {
    try {
      const { filename, includeMetadata = true } = options;
      
      const jsonData = {
        ...(includeMetadata && {
          metadata: {
            chartType: exportData.chartType,
            title: exportData.title,
            description: exportData.description,
            exportedAt: exportData.timestamp.toISOString(),
            recordCount: exportData.data.length
          }
        }),
        config: exportData.config,
        data: exportData.data
      };

      const jsonContent = JSON.stringify(jsonData, null, 2);
      this.downloadFile(jsonContent, filename || 'chart-data.json', 'application/json');
      toast.success('Data exported as JSON');
    } catch (error) {
      console.error('JSON export error:', error);
      toast.error(`Failed to export JSON: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Export data as Excel
   */
  static async exportAsExcel(data: any[], filename?: string): Promise<void> {
    try {
      // For Excel export, we'll use a simple CSV format with .xlsx extension
      // In a real implementation, you'd use a library like xlsx or exceljs
      await this.exportAsCSV(data, filename?.replace('.xlsx', '.csv') || 'data.csv');
      toast.info('Excel export converted to CSV format');
    } catch (error) {
      console.error('Excel export error:', error);
      toast.error(`Failed to export Excel: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Export comprehensive report (PDF)
   */
  static async exportAsPDF(exportData: ExportData, chartElement: HTMLElement, options: ExportOptions): Promise<void> {
    try {
      // For PDF export, we'll create an HTML report and use browser's print functionality
      const reportHTML = this.generateReportHTML(exportData, chartElement);
      
      // Open in new window for printing
      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.write(reportHTML);
        printWindow.document.close();
        printWindow.focus();
        
        // Trigger print dialog
        setTimeout(() => {
          printWindow.print();
        }, 500);
        
        toast.success('PDF report opened for printing');
      } else {
        throw new Error('Unable to open print window');
      }
    } catch (error) {
      console.error('PDF export error:', error);
      toast.error(`Failed to export PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Copy chart to clipboard
   */
  static async copyToClipboard(chartElement: HTMLElement): Promise<void> {
    try {
      const chartContainer = chartElement.querySelector('svg, canvas') as HTMLElement;
      if (!chartContainer) {
        throw new Error('No chart element found');
      }

      if (chartContainer.tagName.toLowerCase() === 'svg') {
        // Copy SVG as text
        const svgData = new XMLSerializer().serializeToString(chartContainer as SVGElement);
        await navigator.clipboard.writeText(svgData);
      } else {
        // Convert canvas to blob and copy as image
        const canvas = chartContainer as HTMLCanvasElement;
        canvas.toBlob(async (blob) => {
          if (blob) {
            await navigator.clipboard.write([
              new ClipboardItem({ 'image/png': blob })
            ]);
          }
        });
      }

      toast.success('Chart copied to clipboard');
    } catch (error) {
      console.error('Clipboard error:', error);
      toast.error(`Failed to copy to clipboard: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Export SVG
   */
  private static async exportSVG(svgElement: SVGElement, filename?: string): Promise<void> {
    const svgData = new XMLSerializer().serializeToString(svgElement);
    const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
    const url = URL.createObjectURL(svgBlob);
    
    this.downloadURL(url, filename || 'chart.svg');
    URL.revokeObjectURL(url);
  }

  /**
   * Export PNG
   */
  private static async exportPNG(
    element: HTMLElement,
    filename?: string,
    quality: number = 1.0,
    width?: number,
    height?: number
  ): Promise<void> {
    // Create canvas
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) throw new Error('Unable to create canvas context');

    // Set canvas size
    const rect = element.getBoundingClientRect();
    canvas.width = width || rect.width;
    canvas.height = height || rect.height;

    // For SVG elements, convert to image first
    if (element.tagName.toLowerCase() === 'svg') {
      const svgData = new XMLSerializer().serializeToString(element as SVGElement);
      const img = new Image();
      
      return new Promise((resolve, reject) => {
        img.onload = () => {
          ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
          canvas.toBlob((blob) => {
            if (blob) {
              const url = URL.createObjectURL(blob);
              this.downloadURL(url, filename || 'chart.png');
              URL.revokeObjectURL(url);
              resolve();
            } else {
              reject(new Error('Failed to create image blob'));
            }
          }, 'image/png', quality);
        };
        
        img.onerror = () => reject(new Error('Failed to load SVG as image'));
        img.src = 'data:image/svg+xml;base64,' + btoa(svgData);
      });
    } else if (element.tagName.toLowerCase() === 'canvas') {
      // For canvas elements, copy directly
      const sourceCanvas = element as HTMLCanvasElement;
      ctx.drawImage(sourceCanvas, 0, 0, canvas.width, canvas.height);
      
      canvas.toBlob((blob) => {
        if (blob) {
          const url = URL.createObjectURL(blob);
          this.downloadURL(url, filename || 'chart.png');
          URL.revokeObjectURL(url);
        }
      }, 'image/png', quality);
    }
  }

  /**
   * Generate HTML report for PDF export
   */
  private static generateReportHTML(exportData: ExportData, chartElement: HTMLElement): string {
    const chartHTML = chartElement.outerHTML;
    const timestamp = new Date().toLocaleString();
    
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Data Visualization Report</title>
          <style>
            body { 
              font-family: Arial, sans-serif; 
              margin: 20px; 
              background: white;
            }
            .header { 
              text-align: center; 
              margin-bottom: 30px; 
              border-bottom: 2px solid #333;
              padding-bottom: 20px;
            }
            .chart-container { 
              text-align: center; 
              margin: 30px 0; 
              page-break-inside: avoid;
            }
            .metadata { 
              background: #f5f5f5; 
              padding: 15px; 
              border-radius: 5px; 
              margin: 20px 0;
            }
            .data-summary {
              margin: 20px 0;
            }
            table { 
              width: 100%; 
              border-collapse: collapse; 
              margin: 10px 0;
            }
            th, td { 
              border: 1px solid #ddd; 
              padding: 8px; 
              text-align: left;
            }
            th { 
              background-color: #f2f2f2; 
            }
            @media print {
              body { margin: 0; }
              .no-print { display: none; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>Data Visualization Report</h1>
            <p>Generated on ${timestamp}</p>
          </div>
          
          <div class="metadata">
            <h2>Chart Information</h2>
            <p><strong>Type:</strong> ${exportData.chartType}</p>
            ${exportData.title ? `<p><strong>Title:</strong> ${exportData.title}</p>` : ''}
            ${exportData.description ? `<p><strong>Description:</strong> ${exportData.description}</p>` : ''}
            <p><strong>Data Points:</strong> ${exportData.data.length}</p>
          </div>
          
          <div class="chart-container">
            <h2>Visualization</h2>
            ${chartHTML}
          </div>
          
          <div class="data-summary">
            <h2>Data Summary</h2>
            <p>Total records: ${exportData.data.length}</p>
            ${exportData.data.length > 0 ? `
              <p>Columns: ${Object.keys(exportData.data[0]).join(', ')}</p>
            ` : ''}
          </div>
        </body>
      </html>
    `;
  }

  /**
   * Download file with content
   */
  private static downloadFile(content: string, filename: string, mimeType: string): void {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    this.downloadURL(url, filename);
    URL.revokeObjectURL(url);
  }

  /**
   * Download file from URL
   */
  private static downloadURL(url: string, filename: string): void {
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  /**
   * Get appropriate filename with timestamp
   */
  static generateFilename(baseName: string, extension: string): string {
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:.]/g, '-');
    return `${baseName}-${timestamp}.${extension}`;
  }

  /**
   * Validate export options
   */
  static validateExportOptions(options: ExportOptions): string[] {
    const errors: string[] = [];
    
    if (options.quality && (options.quality < 0.1 || options.quality > 1.0)) {
      errors.push('Quality must be between 0.1 and 1.0');
    }
    
    if (options.width && options.width < 100) {
      errors.push('Width must be at least 100 pixels');
    }
    
    if (options.height && options.height < 100) {
      errors.push('Height must be at least 100 pixels');
    }
    
    return errors;
  }
}

export default ExportService;
