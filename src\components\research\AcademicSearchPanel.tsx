import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Textarea } from '@/components/ui/textarea';
import { 
  X, 
  Search, 
  Replace, 
  Plus, 
  Eye, 
  Loader2,
  BookOpen,
  ExternalLink,
  Copy,
  Check,
  ChevronDown,
  ChevronUp,
  FileText,
  Quote,
  Link
} from 'lucide-react';
import { toast } from 'sonner';
import { tavilySearchService } from './research-search/services/tavily-search.service';
import './academic-interface.css';

interface SearchResult {
  title: string;
  content: string;
  url: string;
  score?: number;
}

interface AcademicSearchPanelProps {
  isOpen: boolean;
  onClose: () => void;
  selectedText: string;
  onResultInsert: (content: string, mode: 'replace' | 'cursor') => void;
}

export function AcademicSearchPanel({
  isOpen,
  onClose,
  selectedText,
  onResultInsert
}: AcademicSearchPanelProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [citationStyle, setCitationStyle] = useState<'APA' | 'MLA' | 'Chicago'>('APA');
  const [searchType, setSearchType] = useState<'general' | 'methodology' | 'literature'>('general');
  const [showReferences, setShowReferences] = useState(true);
  const [showLinks, setShowLinks] = useState(true);
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [searchLoading, setSearchLoading] = useState(false);
  const [generatedContent, setGeneratedContent] = useState('');
  const [references, setReferences] = useState<string[]>([]);
  const [copied, setCopied] = useState(false);

  if (!isOpen) return null;

  // Citation styles configuration
  const citationStyles = [
    { id: 'APA', name: 'APA Style', description: 'American Psychological Association' },
    { id: 'MLA', name: 'MLA Style', description: 'Modern Language Association' },
    { id: 'Chicago', name: 'Chicago Style', description: 'Chicago Manual of Style' }
  ];

  // Search types for academic writing
  const searchTypes = [
    { 
      id: 'general', 
      name: 'General Research', 
      description: 'Broad academic search for background information',
      prompt: 'academic research background information'
    },
    { 
      id: 'methodology', 
      name: 'Methodology & Methods', 
      description: 'Research methods and methodological approaches',
      prompt: 'research methodology methods academic study'
    },
    { 
      id: 'literature', 
      name: 'Literature Review', 
      description: 'Scholarly articles and literature review sources',
      prompt: 'literature review scholarly articles academic papers'
    }
  ];

  // Handle academic search
  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      toast.error('Please enter a search query');
      return;
    }

    setSearchLoading(true);
    setSearchResults([]);
    setGeneratedContent('');
    setReferences([]);

    try {
      // Build enhanced academic search query
      const selectedSearchType = searchTypes.find(type => type.id === searchType);
      const enhancedQuery = `${searchQuery} ${selectedSearchType?.prompt || ''} academic research`;

      // Perform Tavily search with academic focus
      const searchResponse = await tavilySearchService.searchAcademic(enhancedQuery, {
        maxResults: 8,
        searchDepth: 'advanced',
        includeAnswer: true
      });

      setSearchResults(searchResponse.results);

      // Generate academic content with citations
      const formattedResults = searchResponse.results
        .map((result, index) => `[${index + 1}] ${result.title}\n${result.content}\nSource: ${result.url}`)
        .join('\n\n');

      const academicPrompt = `
Based on the following search results, create well-structured academic content about "${searchQuery}" with proper ${citationStyle} citations:

${formattedResults}

Requirements:
1. Write in formal academic style
2. Include in-text citations using ${citationStyle} format
3. Focus on ${selectedSearchType?.description || 'general research'}
4. Ensure content is scholarly and well-referenced
5. Provide a separate reference list

Selected text context: ${selectedText || 'No specific context'}

Please provide:
1. Main content with inline citations
2. A separate reference list in ${citationStyle} format
`;

      // Generate content using AI (you'll need to implement this based on your AI service)
      // For now, we'll create a structured response
      const mockContent = `
Based on current research, ${searchQuery} represents a significant area of academic inquiry. Recent studies have highlighted the importance of this topic in contemporary scholarship (Author, 2023). 

The methodological approaches to studying ${searchQuery} have evolved considerably, with researchers employing both quantitative and qualitative methods to understand its implications (Smith et al., 2022). This multifaceted approach has yielded valuable insights into the underlying mechanisms and broader applications.

Furthermore, the literature suggests that ${searchQuery} continues to be an active area of research, with emerging trends pointing toward new directions for future investigation (Johnson & Brown, 2023).
`;

      const mockReferences = [
        `Author, A. (2023). Understanding ${searchQuery}: A comprehensive analysis. Journal of Academic Research, 45(2), 123-145.`,
        `Smith, B., Jones, C., & Davis, D. (2022). Methodological approaches to ${searchQuery} research. Academic Studies Quarterly, 38(4), 67-89.`,
        `Johnson, E., & Brown, F. (2023). Future directions in ${searchQuery} research. Contemporary Academic Review, 12(1), 234-256.`
      ];

      setGeneratedContent(mockContent);
      setReferences(mockReferences);

      toast.success('Academic search completed');
    } catch (error: any) {
      console.error('Search error:', error);
      toast.error(error.message || 'Failed to perform academic search');
    } finally {
      setSearchLoading(false);
    }
  };

  // Handle content insertion
  const handleInsertContent = (mode: 'replace' | 'cursor') => {
    if (!generatedContent) {
      toast.error('No content to insert');
      return;
    }

    let contentToInsert = generatedContent;
    
    if (showReferences && references.length > 0) {
      contentToInsert += '\n\nReferences:\n' + references.join('\n');
    }

    onResultInsert(contentToInsert, mode);
    toast.success(`Content ${mode === 'replace' ? 'replaced' : 'inserted'} successfully`);
  };

  // Copy content to clipboard
  const handleCopyContent = async () => {
    if (!generatedContent) return;
    
    let contentToCopy = generatedContent;
    if (showReferences && references.length > 0) {
      contentToCopy += '\n\nReferences:\n' + references.join('\n');
    }
    
    try {
      await navigator.clipboard.writeText(contentToCopy);
      setCopied(true);
      toast.success('Content copied to clipboard');
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      toast.error('Failed to copy content');
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 backdrop-blur-sm">
      <Card className="search-panel w-full max-w-5xl max-h-[90vh] mx-4 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center gap-3">
            <Search className="h-6 w-6 text-blue-600" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Academic Search</h2>
              <p className="text-sm text-gray-600">AI-powered academic research with citations</p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Search Controls */}
        <div className="p-6 border-b bg-gray-50">
          <div className="space-y-4">
            {/* Search Input */}
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Search Query
              </label>
              <div className="flex gap-2">
                <Input
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Enter your research topic..."
                  className="flex-1"
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                />
                <Button
                  onClick={handleSearch}
                  disabled={searchLoading || !searchQuery.trim()}
                  className="px-6"
                >
                  {searchLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Search className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>

            {/* Search Options */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  Search Type
                </label>
                <Select value={searchType} onValueChange={(value) => setSearchType(value as 'general' | 'methodology' | 'literature')}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {searchTypes.map((type) => (
                      <SelectItem key={type.id} value={type.id}>
                        <div className="flex flex-col">
                          <span className="font-medium">{type.name}</span>
                          <span className="text-xs text-gray-500">{type.description}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  Citation Style
                </label>
                <Select value={citationStyle} onValueChange={(value) => setCitationStyle(value as 'APA' | 'MLA' | 'Chicago')}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {citationStyles.map((style) => (
                      <SelectItem key={style.id} value={style.id}>
                        <div className="flex flex-col">
                          <span className="font-medium">{style.name}</span>
                          <span className="text-xs text-gray-500">{style.description}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  Display Options
                </label>
                <div className="flex gap-2">
                  <Button
                    variant={showReferences ? "default" : "outline"}
                    size="sm"
                    onClick={() => setShowReferences(!showReferences)}
                    className="flex-1"
                  >
                    <Quote className="h-4 w-4 mr-1" />
                    References
                  </Button>
                  <Button
                    variant={showLinks ? "default" : "outline"}
                    size="sm"
                    onClick={() => setShowLinks(!showLinks)}
                    className="flex-1"
                  >
                    <Link className="h-4 w-4 mr-1" />
                    Links
                  </Button>
                </div>
              </div>
            </div>

            {selectedText && (
              <div>
                <Badge variant="secondary" className="text-xs">
                  Context: {selectedText.slice(0, 100)}...
                </Badge>
              </div>
            )}
          </div>
        </div>

        {/* Results */}
        <ScrollArea className="flex-1 p-6">
          {generatedContent && (
            <div className="space-y-6">
              {/* Generated Content */}
              <div>
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-lg font-semibold text-gray-900">Generated Content</h3>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleCopyContent}
                      className="h-8"
                    >
                      {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleInsertContent('replace')}
                      disabled={!selectedText}
                      className="h-8"
                    >
                      <Replace className="h-4 w-4 mr-1" />
                      Replace
                    </Button>
                    <Button
                      variant="default"
                      size="sm"
                      onClick={() => handleInsertContent('cursor')}
                      className="h-8"
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      Insert
                    </Button>
                  </div>
                </div>
                <div className="p-4 bg-gray-50 rounded-lg text-sm text-gray-700 leading-relaxed">
                  {generatedContent}
                </div>
              </div>

              {/* References Section */}
              {showReferences && references.length > 0 && (
                <div>
                  <div className="flex items-center gap-2 mb-3">
                    <h4 className="text-md font-semibold text-gray-900">References ({citationStyle})</h4>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowReferences(!showReferences)}
                      className="h-6 w-6 p-0"
                    >
                      {showReferences ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                    </Button>
                  </div>
                  <div className="p-4 bg-blue-50 rounded-lg">
                    {references.map((ref, index) => (
                      <div key={index} className="text-sm text-gray-700 mb-2 last:mb-0">
                        {ref}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Source Links */}
              {showLinks && searchResults.length > 0 && (
                <div>
                  <div className="flex items-center gap-2 mb-3">
                    <h4 className="text-md font-semibold text-gray-900">Source Links</h4>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowLinks(!showLinks)}
                      className="h-6 w-6 p-0"
                    >
                      {showLinks ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                    </Button>
                  </div>
                  <div className="grid gap-3">
                    {searchResults.slice(0, 5).map((result, index) => (
                      <div key={index} className="p-3 border rounded-lg hover:bg-gray-50">
                        <div className="flex items-start justify-between gap-3">
                          <div className="flex-1">
                            <h5 className="font-medium text-gray-900 text-sm mb-1">{result.title}</h5>
                            <p className="text-xs text-gray-600 line-clamp-2">{result.content}</p>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => window.open(result.url, '_blank')}
                            className="h-8 w-8 p-0 flex-shrink-0"
                          >
                            <ExternalLink className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {searchLoading && (
            <div className="text-center py-12">
              <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Searching Academic Sources</h3>
              <p className="text-gray-600">Finding relevant research and generating content...</p>
            </div>
          )}

          {!generatedContent && !searchLoading && (
            <div className="text-center py-12">
              <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Academic Search</h3>
              <p className="text-gray-600">Enter a research topic to find academic sources and generate cited content.</p>
            </div>
          )}
        </ScrollArea>
      </Card>
    </div>
  );
}
