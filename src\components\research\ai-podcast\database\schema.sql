-- AI Podcast Generator Database Schema
-- This file contains the SQL schema for the AI Podcast Generator module

-- Table for storing podcast generations
CREATE TABLE IF NOT EXISTS ai_podcasts (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    title TEXT NOT NULL,
    input_source JSONB NOT NULL,
    outline TEXT,
    script TEXT,
    audio_url TEXT,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'generating_outline', 'generating_script', 'generating_audio', 'completed', 'failed')),
    progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
    error_message TEXT,
    metadata JSONB NOT NULL DEFAULT '{}',
    voices JSONB NOT NULL,
    provider JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for storing uploaded files related to podcasts
CREATE TABLE IF NOT EXISTS ai_podcast_files (
    id SERIAL PRIMARY KEY,
    file_path TEXT NOT NULL,
    original_name TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type TEXT NOT NULL,
    user_id TEXT NOT NULL,
    podcast_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    FOREIGN KEY (podcast_id) REFERENCES ai_podcasts(id) ON DELETE CASCADE
);

-- Indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_ai_podcasts_user_id ON ai_podcasts(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_podcasts_status ON ai_podcasts(status);
CREATE INDEX IF NOT EXISTS idx_ai_podcasts_created_at ON ai_podcasts(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_ai_podcasts_user_created ON ai_podcasts(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_ai_podcast_files_user_id ON ai_podcast_files(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_podcast_files_podcast_id ON ai_podcast_files(podcast_id);

-- GIN index for JSONB columns to enable efficient searching
CREATE INDEX IF NOT EXISTS idx_ai_podcasts_metadata_gin ON ai_podcasts USING GIN (metadata);
CREATE INDEX IF NOT EXISTS idx_ai_podcasts_input_source_gin ON ai_podcasts USING GIN (input_source);

-- Function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to automatically update updated_at on ai_podcasts table
DROP TRIGGER IF EXISTS update_ai_podcasts_updated_at ON ai_podcasts;
CREATE TRIGGER update_ai_podcasts_updated_at
    BEFORE UPDATE ON ai_podcasts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) policies
ALTER TABLE ai_podcasts ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_podcast_files ENABLE ROW LEVEL SECURITY;

-- Policy for ai_podcasts: Users can only access their own podcasts
CREATE POLICY "Users can view their own podcasts" ON ai_podcasts
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can insert their own podcasts" ON ai_podcasts
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update their own podcasts" ON ai_podcasts
    FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY "Users can delete their own podcasts" ON ai_podcasts
    FOR DELETE USING (auth.uid()::text = user_id);

-- Policy for ai_podcast_files: Users can only access their own files
CREATE POLICY "Users can view their own podcast files" ON ai_podcast_files
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can insert their own podcast files" ON ai_podcast_files
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update their own podcast files" ON ai_podcast_files
    FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY "Users can delete their own podcast files" ON ai_podcast_files
    FOR DELETE USING (auth.uid()::text = user_id);

-- Storage bucket for podcast files
INSERT INTO storage.buckets (id, name, public) 
VALUES ('podcast-files', 'podcast-files', true)
ON CONFLICT (id) DO NOTHING;

-- Storage policies for podcast files bucket
CREATE POLICY "Users can upload their own podcast files" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'podcast-files' AND 
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can view their own podcast files" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'podcast-files' AND 
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can update their own podcast files" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'podcast-files' AND 
        auth.uid()::text = (storage.foldername(name))[1]
    );

CREATE POLICY "Users can delete their own podcast files" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'podcast-files' AND 
        auth.uid()::text = (storage.foldername(name))[1]
    );

-- Views for common queries
CREATE OR REPLACE VIEW user_podcast_stats AS
SELECT 
    user_id,
    COUNT(*) as total_podcasts,
    COUNT(*) FILTER (WHERE status = 'completed') as completed_podcasts,
    COUNT(*) FILTER (WHERE status = 'failed') as failed_podcasts,
    SUM((metadata->>'duration')::integer) FILTER (WHERE metadata->>'duration' IS NOT NULL) as total_duration,
    MAX(created_at) as last_podcast_created
FROM ai_podcasts
GROUP BY user_id;

-- Function to get user podcast statistics
CREATE OR REPLACE FUNCTION get_user_podcast_stats(p_user_id TEXT)
RETURNS TABLE (
    total_podcasts BIGINT,
    completed_podcasts BIGINT,
    failed_podcasts BIGINT,
    total_duration BIGINT,
    last_podcast_created TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COALESCE(ups.total_podcasts, 0),
        COALESCE(ups.completed_podcasts, 0),
        COALESCE(ups.failed_podcasts, 0),
        COALESCE(ups.total_duration, 0),
        ups.last_podcast_created
    FROM user_podcast_stats ups
    WHERE ups.user_id = p_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to search podcasts
CREATE OR REPLACE FUNCTION search_user_podcasts(
    p_user_id TEXT,
    p_search_query TEXT DEFAULT NULL,
    p_limit INTEGER DEFAULT 10,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
    id TEXT,
    title TEXT,
    status TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB,
    input_source JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ap.id,
        ap.title,
        ap.status,
        ap.created_at,
        ap.metadata,
        ap.input_source
    FROM ai_podcasts ap
    WHERE ap.user_id = p_user_id
    AND (
        p_search_query IS NULL OR
        ap.title ILIKE '%' || p_search_query || '%' OR
        ap.metadata->>'tags' ILIKE '%' || p_search_query || '%'
    )
    ORDER BY ap.created_at DESC
    LIMIT p_limit
    OFFSET p_offset;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Comments for documentation
COMMENT ON TABLE ai_podcasts IS 'Stores AI-generated podcast data including scripts, audio URLs, and metadata';
COMMENT ON TABLE ai_podcast_files IS 'Stores uploaded files related to podcast generation';
COMMENT ON COLUMN ai_podcasts.input_source IS 'JSONB containing the source content and type (topic, link, file, text)';
COMMENT ON COLUMN ai_podcasts.metadata IS 'JSONB containing podcast metadata like duration, language, tags';
COMMENT ON COLUMN ai_podcasts.voices IS 'JSONB containing selected voice options for both hosts';
COMMENT ON COLUMN ai_podcasts.provider IS 'JSONB containing audio provider information';
