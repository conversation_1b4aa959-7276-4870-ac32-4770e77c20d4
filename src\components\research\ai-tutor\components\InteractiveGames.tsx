/**
 * Interactive Games Component
 * Provides engaging educational games and interactive learning experiences
 */

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON>ton } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Gamepad2, 
  Trophy, 
  Target, 
  Brain,
  BookOpen,
  Zap,
  Award,
  Medal,
  Crown,
  Flame,
  CheckCircle,
  Lock,
  Calendar,
  TrendingUp,
  Users,
  Globe,
  Lightbulb,
  Rocket,
  Shield,
  Heart,
  Play,
  Star,
  Timer,
  RotateCcw,
  Shuffle
} from "lucide-react";
import { ResearchDocument } from '../types';
import { integratedTutorService } from '../services/integrated-tutor.service';
import { toast } from 'sonner';

interface InteractiveGamesProps {
  selectedDocument: ResearchDocument | null;
  uploadedDocuments: ResearchDocument[];
}

interface GameCard {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  estimatedTime: string;
  category: 'Memory' | 'Logic' | 'Knowledge' | 'Speed' | 'Creative';
  requiresDocument: boolean;
  color: string;
}

const availableGames: GameCard[] = [
  {
    id: 'concept-mastery',
    title: 'Concept Mastery Challenge',
    description: 'Interactive learning game that adapts to your topic with multiple question types, explanations, and progressive difficulty',
    icon: Brain,
    difficulty: 'Medium',
    estimatedTime: '10-15 min',
    category: 'Knowledge',
    requiresDocument: false,
    color: 'bg-blue-500'
  },
  {
    id: 'knowledge-explorer',
    title: 'Knowledge Explorer',
    description: 'Explore and connect key concepts through an interactive journey with real-time AI guidance and discovery challenges',
    icon: Target,
    difficulty: 'Medium',
    estimatedTime: '15-20 min',
    category: 'Logic',
    requiresDocument: false,
    color: 'bg-green-500'
  }
];

export function InteractiveGames({ selectedDocument, uploadedDocuments }: InteractiveGamesProps) {
  const [activeGame, setActiveGame] = useState<string | null>(null);
  const [gameContent, setGameContent] = useState<any>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [gameProgress, setGameProgress] = useState(0);
  const [gameTopic, setGameTopic] = useState('');
  const [gameSource, setGameSource] = useState<'ai' | 'search' | 'documents'>('ai');
  const [gameStats, setGameStats] = useState({
    gamesPlayed: 0,
    totalScore: 0,
    averageScore: 0,
    streak: 0,
    achievements: 0
  });

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Easy': return 'bg-green-100 text-green-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'Hard': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'Memory': return 'bg-blue-100 text-blue-800';
      case 'Logic': return 'bg-green-100 text-green-800';
      case 'Knowledge': return 'bg-purple-100 text-purple-800';
      case 'Speed': return 'bg-orange-100 text-orange-800';
      case 'Creative': return 'bg-pink-100 text-pink-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const generateGameContent = async (gameId: string) => {
    const game = availableGames.find(g => g.id === gameId);
    if (!game) {
      toast.error('Game not found');
      return null;
    }

    if (!gameTopic.trim()) {
      toast.error('Please enter a topic for the game');
      return null;
    }

    setIsGenerating(true);
    setGameProgress(0);

    try {
      let content = null;
      const progressSteps = [20, 40, 60, 80, 100];

      for (let i = 0; i < progressSteps.length; i++) {
        setGameProgress(progressSteps[i]);
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      switch (gameId) {
        case 'concept-mastery':
          content = await generateConceptMasteryGame();
          break;
        case 'knowledge-explorer':
          content = await generateKnowledgeExplorerGame();
          break;
        default:
          throw new Error(`Game type "${gameId}" is not supported yet`);
      }

      if (!content) {
        throw new Error('No game content was generated');
      }

      setGameContent(content);
      toast.success(`${game.title} generated successfully!`);
      return content;
    } catch (error) {
      console.error('Game generation failed:', error);
      const errorMessage = error.message || 'Failed to generate game content. Please check your configuration and try again.';
      toast.error(errorMessage);
      return null;
    } finally {
      setIsGenerating(false);
    }
  };

  const handlePlayGame = async (gameId: string) => {
    const game = availableGames.find(g => g.id === gameId);
    if (!game) return;

    if (game.requiresDocument && uploadedDocuments.length === 0) {
      toast.error('This game requires uploaded documents. Please upload a document first.');
      return;
    }

    setActiveGame(gameId);
    toast.success(`Starting ${game.title}...`);

    // Generate actual game content
    const content = await generateGameContent(gameId);
    if (!content) {
      setActiveGame(null);
      return;
    }
  };

  const handleGameComplete = (score: number = 150) => {
    setActiveGame(null);
    setGameContent(null);
    toast.success(`Game completed! +${score} XP earned`);
    setGameStats(prev => {
      const newGamesPlayed = prev.gamesPlayed + 1;
      const newTotalScore = prev.totalScore + score;
      const newAverageScore = Math.round(newTotalScore / newGamesPlayed);

      return {
        ...prev,
        gamesPlayed: newGamesPlayed,
        totalScore: newTotalScore,
        averageScore: newAverageScore,
        streak: prev.streak + 1,
        achievements: score > 200 ? prev.achievements + 1 : prev.achievements
      };
    });
  };

  // Game generation functions
  const generateConceptMasteryGame = async () => {
    if (!gameTopic) {
      throw new Error('Please enter a topic for the Concept Mastery Challenge');
    }

    try {
      // Generate comprehensive questions with multiple types
      const questions = await integratedTutorService.generatePracticeQuestions(
        gameTopic,
        'intermediate',
        8, // More questions for a comprehensive challenge
        'medium',
        gameSource,
        uploadedDocuments
      );

      if (!questions || questions.length === 0) {
        throw new Error(`No questions were generated for topic "${gameTopic}"`);
      }

      // Create a progressive difficulty game structure
      const gameQuestions = questions.map((q, index) => ({
        id: `q_${index}`,
        question: q.question,
        type: q.type,
        options: q.options || [],
        correctAnswer: q.correctAnswer,
        explanation: q.explanation,
        difficulty: index < 3 ? 'easy' : index < 6 ? 'medium' : 'hard',
        points: index < 3 ? 10 : index < 6 ? 15 : 20
      }));

      return {
        type: 'concept-mastery',
        title: `Concept Mastery: ${gameTopic}`,
        instructions: `Master ${gameTopic} through progressive challenges! Answer questions correctly to advance through difficulty levels.`,
        topic: gameTopic,
        source: gameSource,
        questions: gameQuestions,
        totalPoints: gameQuestions.reduce((sum, q) => sum + q.points, 0),
        levels: [
          { name: 'Foundation', range: [0, 2], color: 'bg-green-500' },
          { name: 'Intermediate', range: [3, 5], color: 'bg-yellow-500' },
          { name: 'Advanced', range: [6, 7], color: 'bg-red-500' }
        ]
      };
    } catch (error) {
      console.error('Concept mastery game generation failed:', error);
      throw new Error(`Failed to generate Concept Mastery Challenge for "${gameTopic}": ${error.message}`);
    }
  };

  const generateKnowledgeExplorerGame = async () => {
    if (!gameTopic) {
      throw new Error('Please enter a topic for the Knowledge Explorer');
    }

    try {
      // Generate exploration challenges using AI
      const questions = await integratedTutorService.generatePracticeQuestions(
        gameTopic,
        'intermediate',
        6,
        'medium',
        gameSource,
        uploadedDocuments
      );

      if (!questions || questions.length === 0) {
        throw new Error(`No exploration content was generated for topic "${gameTopic}"`);
      }

      // Create exploration challenges with discovery elements
      const explorationChallenges = questions.map((q, index) => ({
        id: `challenge_${index}`,
        title: `Discovery ${index + 1}`,
        question: q.question,
        type: 'exploration',
        hint: `Think about how this relates to ${gameTopic}...`,
        correctAnswer: q.correctAnswer,
        explanation: q.explanation,
        discoveryPoints: (index + 1) * 5,
        connections: index > 0 ? [`challenge_${index - 1}`] : []
      }));

      return {
        type: 'knowledge-explorer',
        title: `Knowledge Explorer: ${gameTopic}`,
        instructions: `Embark on a journey of discovery! Each challenge you complete unlocks new knowledge and connections in ${gameTopic}.`,
        topic: gameTopic,
        source: gameSource,
        challenges: explorationChallenges,
        totalDiscoveryPoints: explorationChallenges.reduce((sum, c) => sum + c.discoveryPoints, 0),
        journeyMap: {
          start: 'challenge_0',
          milestones: explorationChallenges.map(c => c.id),
          connections: explorationChallenges.map(c => c.connections).flat()
        }
      };
    } catch (error) {
      console.error('Knowledge explorer game generation failed:', error);
      throw new Error(`Failed to generate Knowledge Explorer for "${gameTopic}": ${error.message}`);
    }
  };



  if (activeGame) {
    const game = availableGames.find(g => g.id === activeGame);

    if (isGenerating) {
      return (
        <div className="h-full flex items-center justify-center">
          <Card className="w-full max-w-md">
            <CardContent className="p-8 text-center">
              <div className="mb-6">
                {game && <game.icon className="w-16 h-16 mx-auto text-blue-500 mb-4" />}
                <h3 className="text-xl font-bold mb-2">Generating {game?.title}</h3>
                <p className="text-gray-600">Creating your personalized game...</p>
              </div>
              <div className="space-y-4">
                <Progress value={gameProgress} className="w-full" />
                <p className="text-sm text-gray-500">{gameProgress}% complete</p>
                <Button
                  variant="outline"
                  onClick={() => {
                    setActiveGame(null);
                    setIsGenerating(false);
                  }}
                  className="w-full"
                >
                  Cancel
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    if (gameContent) {
      return (
        <div className="h-full p-6 overflow-y-auto">
          <Card className="w-full max-w-4xl mx-auto">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {game && <game.icon className="w-8 h-8 text-blue-500" />}
                  <div>
                    <CardTitle className="text-2xl">{gameContent.title || game?.title}</CardTitle>
                    <p className="text-gray-600">{gameContent.instructions}</p>
                  </div>
                </div>
                <Button
                  variant="outline"
                  onClick={() => setActiveGame(null)}
                >
                  Exit Game
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Game Content Display */}
                {gameContent.type === 'word-association' && gameContent.pairs && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {gameContent.pairs.map((pair: any, index: number) => (
                      <Card key={index} className="p-4 hover:shadow-md transition-shadow cursor-pointer hover:bg-blue-50">
                        <div className="flex justify-between items-center mb-2">
                          <span className="font-medium text-blue-600">{pair.word1}</span>
                          <span className="text-gray-400 text-xl">↔</span>
                          <span className="font-medium text-green-600">{pair.word2}</span>
                        </div>
                        <p className="text-sm text-gray-600">{pair.explanation}</p>
                      </Card>
                    ))}
                  </div>
                )}

                {gameContent.type === 'concept-builder' && gameContent.concepts && (
                  <div className="text-center">
                    <div className="mb-6">
                      <div className="inline-block p-4 bg-blue-100 rounded-lg shadow-md">
                        <h3 className="text-lg font-bold text-blue-800">{gameContent.centralConcept}</h3>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      {gameContent.concepts.map((concept: string, index: number) => (
                        <div key={index} className="p-3 bg-gray-100 rounded-lg hover:bg-blue-100 cursor-pointer transition-colors shadow-sm">
                          <span className="text-sm font-medium">{concept}</span>
                        </div>
                      ))}
                    </div>
                    <p className="text-sm text-gray-600 mt-4">Click on concepts to explore connections!</p>
                  </div>
                )}

                {gameContent.type === 'quick-quiz' && gameContent.questions && (
                  <div className="space-y-4">
                    {gameContent.questions.map((q: any, index: number) => (
                      <Card key={index} className="p-4">
                        <h4 className="font-medium mb-3">Question {index + 1}: {q.question}</h4>
                        {q.options && (
                          <div className="grid grid-cols-1 gap-2">
                            {q.options.map((option: string, optIndex: number) => (
                              <Button
                                key={optIndex}
                                variant="outline"
                                className="justify-start hover:bg-blue-50"
                                onClick={() => {
                                  const isCorrect = option === q.correct;
                                  toast.success(isCorrect ? `Correct! ${q.explanation || ''}` : `Incorrect. The correct answer is: ${q.correct}. ${q.explanation || ''}`);
                                }}
                              >
                                {option}
                              </Button>
                            ))}
                          </div>
                        )}
                      </Card>
                    ))}
                  </div>
                )}

                {gameContent.type === 'memory-palace' && gameContent.items && (
                  <div className="space-y-6">
                    <div className="text-center">
                      <h3 className="text-xl font-bold mb-2">Topic: {gameContent.topic}</h3>
                      <p className="text-gray-600 mb-4">Memorize these items using the memory palace technique</p>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                      {gameContent.items.map((item: string, index: number) => (
                        <div key={index} className="p-3 bg-purple-100 rounded-lg text-center">
                          <span className="font-medium">{index + 1}. {item}</span>
                        </div>
                      ))}
                    </div>
                    <div className="bg-yellow-50 p-4 rounded-lg">
                      <h4 className="font-medium mb-2">Memory Aid:</h4>
                      <p className="text-sm text-gray-700">{gameContent.mnemonic}</p>
                    </div>
                  </div>
                )}

                {gameContent.type === 'document-detective' && gameContent.challenges && (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {gameContent.challenges.map((challenge: string, index: number) => (
                        <Card key={index} className="p-4 hover:shadow-md transition-shadow">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
                              {index + 1}
                            </div>
                            <span className="font-medium">{challenge}</span>
                          </div>
                        </Card>
                      ))}
                    </div>
                    <p className="text-sm text-gray-600 text-center">Complete these challenges by analyzing your uploaded documents!</p>
                  </div>
                )}

                {gameContent.type === 'creative-synthesis' && gameContent.theme && (
                  <div className="space-y-6">
                    <div className="text-center">
                      <h3 className="text-xl font-bold mb-2">Theme: {gameContent.theme}</h3>
                      <p className="text-gray-600 mb-4">{gameContent.prompt}</p>
                    </div>
                    {gameContent.examples && (
                      <div className="bg-green-50 p-4 rounded-lg">
                        <h4 className="font-medium mb-2">Example Combinations:</h4>
                        <ul className="list-disc list-inside space-y-1">
                          {gameContent.examples.map((example: string, index: number) => (
                            <li key={index} className="text-sm text-gray-700">{example}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                    <div className="text-center">
                      <p className="text-sm text-gray-600">Think creatively and come up with your own unique combinations!</p>
                    </div>
                  </div>
                )}

                {/* Default content display for unstructured content */}
                {gameContent.content && !gameContent.pairs && !gameContent.concepts && !gameContent.questions && !gameContent.items && !gameContent.challenges && !gameContent.theme && (
                  <div className="prose max-w-none">
                    <div className="whitespace-pre-wrap bg-gray-50 p-4 rounded-lg">{gameContent.content}</div>
                  </div>
                )}

                <div className="flex justify-center pt-6">
                  <Button
                    onClick={handleGameComplete}
                    className="bg-green-600 hover:bg-green-700 text-white px-8 py-2"
                  >
                    <Trophy className="w-4 h-4 mr-2" />
                    Complete Game
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    return (
      <div className="h-full flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-8 text-center">
            <div className="mb-6">
              {game && <game.icon className="w-16 h-16 mx-auto text-blue-500 mb-4" />}
              <h3 className="text-xl font-bold mb-2">{game?.title}</h3>
              <p className="text-gray-600">Loading game content...</p>
            </div>
            <Button
              variant="outline"
              onClick={() => setActiveGame(null)}
              className="w-full"
            >
              Exit Game
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Interactive Learning Games</h1>
        <p className="text-gray-600">Engage with educational content through fun, interactive experiences</p>
      </div>

      {/* Topic and Source Selection */}
      <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Game Topic (Optional)
            </label>
            <input
              type="text"
              value={gameTopic}
              onChange={(e) => setGameTopic(e.target.value)}
              placeholder="e.g., Biology, Physics, History, Mathematics..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <p className="text-xs text-gray-500 mt-1">Leave empty for general knowledge games</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Knowledge Source
            </label>
            <select
              value={gameSource}
              onChange={(e) => setGameSource(e.target.value as 'ai' | 'search' | 'documents')}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="ai">AI Knowledge</option>
              <option value="search">Web Search</option>
              <option value="documents" disabled={uploadedDocuments.length === 0}>
                Uploaded Documents {uploadedDocuments.length === 0 ? '(None available)' : `(${uploadedDocuments.length})`}
              </option>
            </select>
            <p className="text-xs text-gray-500 mt-1">
              {gameSource === 'documents' && uploadedDocuments.length === 0
                ? 'Upload documents to use this source'
                : 'Games will be generated based on this source'}
            </p>
          </div>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <Trophy className="w-8 h-8 text-yellow-500 mx-auto mb-2" />
            <p className="text-2xl font-bold">{gameStats.gamesPlayed}</p>
            <p className="text-sm text-gray-600">Games Played</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <Star className="w-8 h-8 text-blue-500 mx-auto mb-2" />
            <p className="text-2xl font-bold">{gameStats.totalScore}</p>
            <p className="text-sm text-gray-600">Total Score</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <Target className="w-8 h-8 text-green-500 mx-auto mb-2" />
            <p className="text-2xl font-bold">{gameStats.averageScore}%</p>
            <p className="text-sm text-gray-600">Avg Score</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <Flame className="w-8 h-8 text-orange-500 mx-auto mb-2" />
            <p className="text-2xl font-bold">{gameStats.streak}</p>
            <p className="text-sm text-gray-600">Day Streak</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <Award className="w-8 h-8 text-purple-500 mx-auto mb-2" />
            <p className="text-2xl font-bold">{gameStats.achievements}</p>
            <p className="text-sm text-gray-600">Achievements</p>
          </CardContent>
        </Card>
      </div>

      {/* Games Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {availableGames.map((game) => (
          <Card key={game.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className={`p-3 rounded-lg ${game.color}`}>
                  <game.icon className="w-6 h-6 text-white" />
                </div>
                <div className="flex space-x-2">
                  <Badge className={getDifficultyColor(game.difficulty)}>
                    {game.difficulty}
                  </Badge>
                  <Badge className={getCategoryColor(game.category)}>
                    {game.category}
                  </Badge>
                </div>
              </div>
              <CardTitle className="text-lg">{game.title}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-4">{game.description}</p>
              <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                <div className="flex items-center">
                  <Timer className="w-4 h-4 mr-1" />
                  {game.estimatedTime}
                </div>
                {game.requiresDocument && (
                  <div className="flex items-center">
                    <BookOpen className="w-4 h-4 mr-1" />
                    Requires Document
                  </div>
                )}
              </div>
              <Button 
                onClick={() => handlePlayGame(game.id)}
                className="w-full"
                disabled={game.requiresDocument && uploadedDocuments.length === 0}
              >
                <Play className="w-4 h-4 mr-2" />
                Play Game
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
