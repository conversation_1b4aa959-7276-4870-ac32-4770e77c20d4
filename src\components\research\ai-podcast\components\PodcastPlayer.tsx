import React, { useState, useRef, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { 
  Play, 
  Pause, 
  SkipBack, 
  SkipForward,
  Volume2,
  VolumeX,
  Download,
  Share,
  MoreHorizontal,
  Clock,
  FileText,
  Eye
} from "lucide-react";

import { PodcastGeneration, PodcastPlayerState } from '../types';

interface PodcastPlayerProps {
  podcast: PodcastGeneration;
  onStateChange?: (state: PodcastPlayerState) => void;
}

export function PodcastPlayer({ podcast, onStateChange }: PodcastPlayerProps) {
  const audioRef = useRef<HTMLAudioElement>(null);
  const [playerState, setPlayerState] = useState<PodcastPlayerState>({
    isPlaying: false,
    currentTime: 0,
    duration: 0,
    volume: 1,
    playbackRate: 1
  });
  const [showScript, setShowScript] = useState(false);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const updateTime = () => {
      setPlayerState(prev => ({
        ...prev,
        currentTime: audio.currentTime,
        duration: audio.duration || 0
      }));
    };

    const updatePlayState = () => {
      setPlayerState(prev => ({
        ...prev,
        isPlaying: !audio.paused
      }));
    };

    audio.addEventListener('timeupdate', updateTime);
    audio.addEventListener('loadedmetadata', updateTime);
    audio.addEventListener('play', updatePlayState);
    audio.addEventListener('pause', updatePlayState);
    audio.addEventListener('ended', updatePlayState);

    return () => {
      audio.removeEventListener('timeupdate', updateTime);
      audio.removeEventListener('loadedmetadata', updateTime);
      audio.removeEventListener('play', updatePlayState);
      audio.removeEventListener('pause', updatePlayState);
      audio.removeEventListener('ended', updatePlayState);
    };
  }, []);

  useEffect(() => {
    onStateChange?.(playerState);
  }, [playerState, onStateChange]);

  const formatTime = (seconds: number): string => {
    if (isNaN(seconds)) return '0:00';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handlePlayPause = () => {
    const audio = audioRef.current;
    if (!audio) return;

    if (playerState.isPlaying) {
      audio.pause();
    } else {
      audio.play();
    }
  };

  const handleSeek = (value: number[]) => {
    const audio = audioRef.current;
    if (!audio) return;

    const newTime = value[0];
    audio.currentTime = newTime;
    setPlayerState(prev => ({ ...prev, currentTime: newTime }));
  };

  const handleVolumeChange = (value: number[]) => {
    const audio = audioRef.current;
    if (!audio) return;

    const newVolume = value[0];
    audio.volume = newVolume;
    setPlayerState(prev => ({ ...prev, volume: newVolume }));
  };

  const handleSkip = (seconds: number) => {
    const audio = audioRef.current;
    if (!audio) return;

    const newTime = Math.max(0, Math.min(playerState.duration, playerState.currentTime + seconds));
    audio.currentTime = newTime;
  };

  const handleDownload = () => {
    if (podcast.audioUrl) {
      const link = document.createElement('a');
      link.href = podcast.audioUrl;
      link.download = `${podcast.title}.mp3`;
      link.click();
    }
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: podcast.title,
          text: `Listen to this AI-generated podcast: ${podcast.title}`,
          url: window.location.href
        });
      } catch (error) {
        console.error('Error sharing:', error);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
    }
  };

  return (
    <div className="space-y-4">
      {/* Audio Element */}
      <audio
        ref={audioRef}
        src={podcast.audioUrl}
        preload="metadata"
      />

      {/* Podcast Info */}
      <div className="space-y-2">
        <h3 className="font-semibold text-lg">{podcast.title}</h3>
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <Clock className="h-4 w-4" />
          <span>{formatTime(playerState.duration)}</span>
          <Badge variant="secondary" className="text-xs">
            {podcast.metadata.language.toUpperCase()}
          </Badge>
          <Badge variant="outline" className="text-xs">
            {podcast.provider.name}
          </Badge>
        </div>
      </div>

      {/* Main Player Controls */}
      <Card className="border border-gray-200">
        <CardContent className="p-4">
          {/* Progress Bar */}
          <div className="space-y-2 mb-4">
            <Slider
              value={[playerState.currentTime]}
              max={playerState.duration || 100}
              step={1}
              onValueChange={handleSeek}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-gray-500">
              <span>{formatTime(playerState.currentTime)}</span>
              <span>{formatTime(playerState.duration)}</span>
            </div>
          </div>

          {/* Control Buttons */}
          <div className="flex items-center justify-center gap-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleSkip(-10)}
              className="h-8 w-8 p-0"
            >
              <SkipBack className="h-4 w-4" />
            </Button>

            <Button
              onClick={handlePlayPause}
              className="h-12 w-12 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600"
            >
              {playerState.isPlaying ? (
                <Pause className="h-6 w-6 text-white" />
              ) : (
                <Play className="h-6 w-6 text-white ml-1" />
              )}
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => handleSkip(10)}
              className="h-8 w-8 p-0"
            >
              <SkipForward className="h-4 w-4" />
            </Button>
          </div>

          {/* Volume Control */}
          <div className="flex items-center gap-2 mt-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleVolumeChange([playerState.volume > 0 ? 0 : 1])}
              className="h-8 w-8 p-0"
            >
              {playerState.volume > 0 ? (
                <Volume2 className="h-4 w-4" />
              ) : (
                <VolumeX className="h-4 w-4" />
              )}
            </Button>
            <Slider
              value={[playerState.volume]}
              max={1}
              step={0.1}
              onValueChange={handleVolumeChange}
              className="flex-1"
            />
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex gap-2">
        <Button
          variant="outline"
          onClick={() => setShowScript(!showScript)}
          className="flex-1 flex items-center gap-2"
        >
          <FileText className="h-4 w-4" />
          {showScript ? 'Hide Script' : 'View Script'}
        </Button>
        <Button
          variant="outline"
          onClick={handleDownload}
          className="flex-1 flex items-center gap-2"
        >
          <Download className="h-4 w-4" />
          Download
        </Button>
        <Button
          variant="outline"
          onClick={handleShare}
          className="flex items-center gap-2"
        >
          <Share className="h-4 w-4" />
          Share
        </Button>
      </div>

      {/* Script Display */}
      {showScript && podcast.script && (
        <Card className="border border-gray-200">
          <CardHeader>
            <CardTitle className="text-base flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Podcast Script
            </CardTitle>
          </CardHeader>
          <CardContent className="max-h-96 overflow-y-auto">
            <div className="space-y-4 text-sm">
              {podcast.script.split('\n\n').map((paragraph, index) => {
                const isHost1 = paragraph.startsWith('Host 1:') || paragraph.startsWith('**Host 1**');
                const isHost2 = paragraph.startsWith('Host 2:') || paragraph.startsWith('**Host 2**');
                
                return (
                  <div 
                    key={index}
                    className={`p-3 rounded-lg ${
                      isHost1 ? 'bg-blue-50 border-l-4 border-blue-500' :
                      isHost2 ? 'bg-purple-50 border-l-4 border-purple-500' :
                      'bg-gray-50'
                    }`}
                  >
                    {isHost1 && (
                      <div className="flex items-center gap-2 mb-2">
                        <Badge variant="secondary" className="text-xs">
                          {podcast.voices.host1.name}
                        </Badge>
                      </div>
                    )}
                    {isHost2 && (
                      <div className="flex items-center gap-2 mb-2">
                        <Badge variant="secondary" className="text-xs">
                          {podcast.voices.host2.name}
                        </Badge>
                      </div>
                    )}
                    <p className="leading-relaxed">
                      {paragraph.replace(/^\*\*Host [12]\*\*:?\s*/, '').replace(/^Host [12]:?\s*/, '')}
                    </p>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Podcast Outline */}
      {podcast.outline && (
        <Card className="border border-gray-200">
          <CardHeader>
            <CardTitle className="text-base flex items-center gap-2">
              <Eye className="h-4 w-4" />
              Podcast Outline
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="prose prose-sm max-w-none">
              <div className="whitespace-pre-wrap text-sm text-gray-700">
                {podcast.outline}
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
