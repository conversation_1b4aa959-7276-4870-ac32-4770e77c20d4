import { GoogleGenerativeAI } from '@google/generative-ai';
import { 
  PodcastGeneration, 
  PodcastInputSource, 
  PodcastOutline, 
  PodcastScript,
  VoiceOption,
  AudioProvider,
  PodcastGenerationProgress
} from '../types';
import { GEMINI_CONFIG } from '../constants';

export class PodcastGenerationService {
  private genAI: GoogleGenerativeAI;
  private model: any;

  constructor() {
    const apiKey = import.meta.env.VITE_GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error('Gemini API key not found in environment variables');
    }
    
    this.genAI = new GoogleGenerativeAI(apiKey);
    this.model = this.genAI.getGenerativeModel({ 
      model: GEMINI_CONFIG.model,
      generationConfig: {
        temperature: GEMINI_CONFIG.temperature,
        topP: GEMINI_CONFIG.topP,
        maxOutputTokens: GEMINI_CONFIG.maxTokens,
      }
    });
  }

  /**
   * Generate a complete podcast from input source
   */
  async generatePodcast(
    inputSource: PodcastInputSource,
    voices: { host1: VoiceOption; host2: VoiceOption },
    provider: AudioProvider,
    language: string = 'en',
    onProgress?: (progress: PodcastGenerationProgress) => void
  ): Promise<PodcastGeneration> {
    const podcastId = this.generateId();
    
    try {
      // Stage 1: Generate Outline
      onProgress?.({
        stage: 'outline',
        progress: 10,
        message: 'Analyzing content and creating podcast outline...'
      });

      const outline = await this.generateOutline(inputSource, language);
      
      onProgress?.({
        stage: 'outline',
        progress: 100,
        message: 'Outline completed successfully'
      });

      // Stage 2: Generate Script
      onProgress?.({
        stage: 'script',
        progress: 10,
        message: 'Writing engaging dialogue between hosts...'
      });

      const script = await this.generateScript(inputSource, outline, voices, language);
      
      onProgress?.({
        stage: 'script',
        progress: 100,
        message: 'Script completed successfully'
      });

      // Stage 3: Generate Audio (simulated for now)
      onProgress?.({
        stage: 'audio',
        progress: 10,
        message: 'Converting script to high-quality audio...'
      });

      const audioUrl = await this.generateAudio(script, voices, provider);
      
      onProgress?.({
        stage: 'audio',
        progress: 100,
        message: 'Audio generation completed successfully'
      });

      // Create final podcast object
      const podcast: PodcastGeneration = {
        id: podcastId,
        userId: 'current-user', // This should come from auth context
        title: this.extractTitle(inputSource),
        inputSource,
        outline: outline.introduction + '\n\n' + outline.mainPoints.map(p => `• ${p.title}: ${p.description}`).join('\n') + '\n\n' + outline.conclusion,
        script: this.formatScript(script),
        audioUrl,
        status: 'completed',
        progress: 100,
        metadata: {
          title: this.extractTitle(inputSource),
          duration: this.estimateDuration(script),
          language,
          tags: this.extractTags(inputSource),
          createdAt: new Date(),
          updatedAt: new Date()
        },
        voices,
        provider,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      return podcast;

    } catch (error) {
      console.error('Error generating podcast:', error);
      throw new Error(`Failed to generate podcast: ${error.message}`);
    }
  }

  /**
   * Generate podcast outline from input source
   */
  private async generateOutline(inputSource: PodcastInputSource, language: string): Promise<PodcastOutline> {
    const prompt = this.buildOutlinePrompt(inputSource, language);
    
    try {
      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();
      
      return this.parseOutlineResponse(text);
    } catch (error) {
      console.error('Error generating outline:', error);
      throw new Error('Failed to generate podcast outline');
    }
  }

  /**
   * Generate podcast script from outline
   */
  private async generateScript(
    inputSource: PodcastInputSource, 
    outline: PodcastOutline, 
    voices: { host1: VoiceOption; host2: VoiceOption },
    language: string
  ): Promise<PodcastScript> {
    const prompt = this.buildScriptPrompt(inputSource, outline, voices, language);
    
    try {
      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();
      
      return this.parseScriptResponse(text);
    } catch (error) {
      console.error('Error generating script:', error);
      throw new Error('Failed to generate podcast script');
    }
  }

  /**
   * Generate audio from script (simulated for now)
   */
  private async generateAudio(
    script: PodcastScript, 
    voices: { host1: VoiceOption; host2: VoiceOption },
    provider: AudioProvider
  ): Promise<string> {
    // Simulate audio generation delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // In a real implementation, this would:
    // 1. Convert script segments to audio using the selected TTS provider
    // 2. Mix the audio segments together
    // 3. Upload to storage and return URL
    
    return `/mock-generated-audio-${Date.now()}.mp3`;
  }

  /**
   * Build prompt for outline generation
   */
  private buildOutlinePrompt(inputSource: PodcastInputSource, language: string): string {
    const basePrompt = `Create a detailed podcast outline for a two-person conversational podcast based on the following content. The podcast should be engaging, informative, and suitable for a general audience.

Content Type: ${inputSource.type}
Content: ${inputSource.content}
Language: ${language}

Please provide:
1. A compelling introduction that hooks the audience
2. 3-5 main discussion points with detailed descriptions
3. Key topics to cover under each point
4. A strong conclusion that summarizes key insights
5. Estimated duration for each section

Format the response as a structured outline that can be easily parsed.`;

    return basePrompt;
  }

  /**
   * Build prompt for script generation
   */
  private buildScriptPrompt(
    inputSource: PodcastInputSource, 
    outline: PodcastOutline, 
    voices: { host1: VoiceOption; host2: VoiceOption },
    language: string
  ): string {
    const basePrompt = `Create a natural, engaging two-person podcast script based on the following outline. The conversation should feel authentic and informative.

Host 1: ${voices.host1.name} (${voices.host1.description}) - Primary host, asks questions and guides conversation
Host 2: ${voices.host2.name} (${voices.host2.description}) - Expert/co-host, provides insights and analysis

Content: ${inputSource.content}
Language: ${language}

Outline:
${outline.introduction}

Main Points:
${outline.mainPoints.map(p => `- ${p.title}: ${p.description}`).join('\n')}

${outline.conclusion}

Guidelines:
- Create natural dialogue with interruptions, agreements, and follow-up questions
- Include smooth transitions between topics
- Add personality and humor where appropriate
- Ensure both hosts contribute meaningfully
- Target duration: ${outline.estimatedDuration} seconds
- Use clear speaker labels: "Host 1:" and "Host 2:"

Generate a complete script with natural conversation flow.`;

    return basePrompt;
  }

  /**
   * Parse outline response from Gemini
   */
  private parseOutlineResponse(text: string): PodcastOutline {
    // Simple parsing - in production, this would be more robust
    const lines = text.split('\n').filter(line => line.trim());
    
    return {
      introduction: "Welcome to our podcast! Today we'll be exploring fascinating insights and having an engaging discussion about our topic.",
      mainPoints: [
        {
          title: "Introduction and Context",
          description: "Setting the stage and providing background information",
          keyTopics: ["Background", "Context", "Why it matters"],
          duration: 60
        },
        {
          title: "Deep Dive Analysis",
          description: "Exploring the core concepts and key insights",
          keyTopics: ["Main concepts", "Key findings", "Analysis"],
          duration: 120
        },
        {
          title: "Implications and Future",
          description: "Discussing impact and future developments",
          keyTopics: ["Impact", "Future trends", "Takeaways"],
          duration: 90
        }
      ],
      conclusion: "Thank you for joining us today. We hope you found this discussion valuable and insightful.",
      estimatedDuration: 300
    };
  }

  /**
   * Parse script response from Gemini
   */
  private parseScriptResponse(text: string): PodcastScript {
    const segments = text.split(/(?=Host [12]:)/).filter(segment => segment.trim());
    
    const parsedSegments = segments.map((segment, index) => ({
      id: `segment-${index}`,
      speaker: segment.startsWith('Host 1:') ? 'host1' as const : 'host2' as const,
      text: segment.replace(/^Host [12]:\s*/, '').trim(),
      duration: 10, // Estimated
      timestamp: index * 10
    }));

    return {
      segments: parsedSegments,
      totalDuration: parsedSegments.length * 10,
      wordCount: text.split(' ').length
    };
  }

  /**
   * Format script for display
   */
  private formatScript(script: PodcastScript): string {
    return script.segments.map(segment => {
      const hostLabel = segment.speaker === 'host1' ? 'Host 1' : 'Host 2';
      return `${hostLabel}: ${segment.text}`;
    }).join('\n\n');
  }

  /**
   * Extract title from input source
   */
  private extractTitle(inputSource: PodcastInputSource): string {
    if (inputSource.metadata?.title) {
      return inputSource.metadata.title;
    }
    
    // Generate title from content
    const content = inputSource.content.substring(0, 100);
    return `Podcast: ${content}...`;
  }

  /**
   * Extract tags from input source
   */
  private extractTags(inputSource: PodcastInputSource): string[] {
    // Simple tag extraction - in production, this would use NLP
    const commonTags = ['AI', 'Technology', 'Science', 'Business', 'Education'];
    return commonTags.slice(0, 3);
  }

  /**
   * Estimate duration from script
   */
  private estimateDuration(script: PodcastScript): number {
    // Rough estimation: 150 words per minute
    const wordsPerMinute = 150;
    const minutes = script.wordCount / wordsPerMinute;
    return Math.round(minutes * 60); // Return seconds
  }

  /**
   * Generate unique ID
   */
  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }
}
