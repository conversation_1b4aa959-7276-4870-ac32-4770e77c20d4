import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import {
  Brain,
  Send,
  Replace,
  Plus,
  Eye,
  Loader2,
  Settings,
  Sparkles,
  ChevronDown,
  ChevronUp,
  Copy,
  Check,
  Wrench,
  Search
} from 'lucide-react';
import { toast } from 'sonner';
import { enhancedAIService, AI_MODELS, AIModel } from './paper-generator/enhanced-ai.service';
import './academic-interface.css';

interface PrimaryAIAssistantProps {
  onAIRequest: (prompt: string, text: string, mode: 'replace' | 'insert' | 'display') => void;
  selectedText: string;
  documentContent: string;
  aiLoading: boolean;
  aiResponse: string;
  className?: string;
  onToolsOpen?: () => void;
  onSearchOpen?: () => void;
}

export function PrimaryAIAssistant({
  onAIRequest,
  selectedText,
  documentContent,
  aiLoading,
  aiResponse,
  className = '',
  onToolsOpen,
  onSearchOpen
}: PrimaryAIAssistantProps) {
  const [prompt, setPrompt] = useState('');
  const [selectedMode, setSelectedMode] = useState<'replace' | 'insert' | 'display'>('display');
  const [selectedModel, setSelectedModel] = useState<string>(enhancedAIService.getDefaultModel());
  const [isExpanded, setIsExpanded] = useState(false);
  const [showModelSelector, setShowModelSelector] = useState(false);
  const [copied, setCopied] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = textareaRef.current.scrollHeight + 'px';
    }
  }, [prompt]);

  // Action mode options
  const actionModes = [
    { 
      id: 'replace', 
      label: 'Replace Selected', 
      icon: <Replace className="h-4 w-4" />, 
      description: 'Replace the selected text with AI response',
      disabled: !selectedText.trim()
    },
    { 
      id: 'insert', 
      label: 'Add to Cursor', 
      icon: <Plus className="h-4 w-4" />, 
      description: 'Insert AI response at cursor position'
    },
    { 
      id: 'display', 
      label: 'Display Only', 
      icon: <Eye className="h-4 w-4" />, 
      description: 'Show AI response in popup for review'
    }
  ];

  // Handle AI request submission
  const handleSubmit = async () => {
    if (!prompt.trim() || aiLoading) return;

    try {
      // Use the selected text as context if available
      const context = selectedText.trim() || documentContent.slice(0, 1000);
      const fullPrompt = selectedText.trim() 
        ? `Context: "${selectedText}"\n\nRequest: ${prompt}`
        : prompt;

      onAIRequest(fullPrompt, context, selectedMode);
      setPrompt('');
      
      // Collapse if not in display mode
      if (selectedMode !== 'display') {
        setIsExpanded(false);
      }
      
      toast.success('AI request submitted');
    } catch (error: any) {
      console.error('AI request error:', error);
      toast.error(error.message || 'Failed to submit AI request');
    }
  };

  // Handle Enter key submission
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      handleSubmit();
    }
  };

  // Copy AI response to clipboard
  const handleCopyResponse = async () => {
    if (!aiResponse) return;
    
    try {
      await navigator.clipboard.writeText(aiResponse);
      setCopied(true);
      toast.success('Response copied to clipboard');
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      toast.error('Failed to copy response');
    }
  };

  // Get available models
  const availableModels = AI_MODELS.filter(model => 
    enhancedAIService.hasValidApiKey() || model.provider === 'Google'
  );

  return (
    <Card className={`primary-ai-assistant transition-all duration-300 ${className}`}>
      {/* Main AI Interface */}
      <div className="p-4">
        <div className="flex items-center gap-3 mb-3">
          <div className="flex items-center gap-2">
            <Brain className="h-5 w-5 text-blue-600" />
            <span className="font-medium text-gray-900">AI Assistant</span>
          </div>
          
          {selectedText && (
            <Badge variant="secondary" className="text-xs">
              {selectedText.length} chars selected
            </Badge>
          )}
          
          <div className="ml-auto flex items-center gap-2">
            {/* Tools Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={onToolsOpen}
              className="h-8 px-3"
              title="Research Tools"
            >
              <Wrench className="h-4 w-4 mr-1" />
              Tools
            </Button>

            {/* Search Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={onSearchOpen}
              className="h-8 px-3"
              title="Academic Search"
            >
              <Search className="h-4 w-4 mr-1" />
              Search
            </Button>

            {/* Model Selector Toggle */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowModelSelector(!showModelSelector)}
              className="h-8 px-2"
              title="AI Model Settings"
            >
              <Settings className="h-4 w-4" />
            </Button>

            {/* Expand/Collapse Toggle */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="h-8 px-2"
              title={isExpanded ? "Collapse" : "Expand"}
            >
              {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </Button>
          </div>
        </div>

        {/* Model Selector */}
        {showModelSelector && (
          <div className="mb-3 p-3 bg-gray-50 rounded-lg">
            <label className="text-sm font-medium text-gray-700 mb-2 block">
              AI Model
            </label>
            <Select value={selectedModel} onValueChange={setSelectedModel}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select AI model" />
              </SelectTrigger>
              <SelectContent>
                {availableModels.map((model) => (
                  <SelectItem key={model.id} value={model.id}>
                    <div className="flex flex-col">
                      <span className="font-medium">{model.name}</span>
                      <span className="text-xs text-gray-500">{model.description}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        {/* Prompt Input */}
        <div className="space-y-3">
          <div className="relative">
            <Textarea
              ref={textareaRef}
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={selectedText
                ? "How would you like to enhance the selected text?"
                : "Ask AI to help with your writing..."
              }
              className="ai-prompt-input min-h-[60px] max-h-[200px] resize-none pr-12"
              disabled={aiLoading}
            />
            <Button
              onClick={handleSubmit}
              disabled={!prompt.trim() || aiLoading}
              size="sm"
              className="absolute bottom-2 right-2 h-8 w-8 p-0"
              title="Send (Ctrl+Enter)"
            >
              {aiLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>

          {/* Action Mode Selector */}
          <div className="flex gap-2">
            {actionModes.map((mode) => (
              <Button
                key={mode.id}
                variant={selectedMode === mode.id ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedMode(mode.id as 'replace' | 'insert' | 'display')}
                disabled={mode.disabled}
                className={`action-mode-button flex items-center gap-2 text-xs ${selectedMode === mode.id ? 'selected' : ''}`}
                title={mode.description}
              >
                {mode.icon}
                {mode.label}
              </Button>
            ))}
          </div>
        </div>

        {/* AI Response Display */}
        {isExpanded && aiResponse && (
          <>
            <Separator className="my-4" />
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium text-gray-900">AI Response</h4>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleCopyResponse}
                  className="h-8 px-2"
                  title="Copy response"
                >
                  {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                </Button>
              </div>
              <div className="p-3 bg-gray-50 rounded-lg text-sm text-gray-700 max-h-[300px] overflow-y-auto">
                {aiResponse}
              </div>
            </div>
          </>
        )}
      </div>
    </Card>
  );
}
