import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import {
  Trophy,
  Clock,
  Target,
  CheckCircle,
  XCircle,
  BookOpen,
  Download,
  FileText,
  RotateCcw,
  ArrowLeft
} from "lucide-react";
import { Quiz } from '../types';
import { quizExportService } from '../services/quiz-export.service';
import { toast } from 'sonner';

interface QuizResults {
  score: number;
  totalQuestions: number;
  correctAnswers: number;
  timeSpent: number;
  answers: Array<{
    questionId: string;
    answer: string;
    isCorrect: boolean;
    timeSpent: number;
  }>;
}

interface QuizResultsProps {
  quiz: Quiz;
  results: QuizResults;
  onRetakeQuiz: () => void;
  onBackToQuizzes: () => void;
}

export function QuizResults({ quiz, results, onRetakeQuiz, onBackToQuizzes }: QuizResultsProps) {
  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const getScoreColor = (score: number): string => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBadgeColor = (score: number): string => {
    if (score >= 90) return 'bg-green-100 text-green-800';
    if (score >= 70) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  const downloadResults = async (format: 'word' | 'pdf') => {
    try {
      if (format === 'word') {
        await quizExportService.exportToWord(quiz, results);
        toast.success('Quiz results downloaded as Word document!');
      } else {
        await quizExportService.exportToPDF(quiz, results);
        toast.success('Quiz results exported to PDF!');
      }
    } catch (error) {
      console.error('Export failed:', error);
      toast.error(`Failed to export quiz results: ${error.message}`);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center space-x-3">
          <Trophy className="w-8 h-8 text-yellow-500" />
          <h1 className="text-3xl font-bold">Quiz Complete!</h1>
        </div>
        <p className="text-gray-600 text-lg">{quiz.title}</p>
      </div>

      {/* Score Overview */}
      <Card className="border-2">
        <CardHeader className="text-center">
          <CardTitle className="flex items-center justify-center space-x-2">
            <Target className="w-6 h-6" />
            <span>Your Score</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="text-center">
            <div className={`text-6xl font-bold ${getScoreColor(results.score)}`}>
              {results.score}%
            </div>
            <Badge className={`mt-2 ${getScoreBadgeColor(results.score)}`}>
              {results.correctAnswers} out of {results.totalQuestions} correct
            </Badge>
          </div>
          
          <Progress value={results.score} className="w-full h-3" />
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
            <div className="space-y-2">
              <div className="flex items-center justify-center space-x-2">
                <CheckCircle className="w-5 h-5 text-green-500" />
                <span className="font-medium">Correct</span>
              </div>
              <div className="text-2xl font-bold text-green-600">{results.correctAnswers}</div>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-center space-x-2">
                <XCircle className="w-5 h-5 text-red-500" />
                <span className="font-medium">Incorrect</span>
              </div>
              <div className="text-2xl font-bold text-red-600">
                {results.totalQuestions - results.correctAnswers}
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-center space-x-2">
                <Clock className="w-5 h-5 text-blue-500" />
                <span className="font-medium">Time</span>
              </div>
              <div className="text-2xl font-bold text-blue-600">{formatTime(results.timeSpent)}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Performance Analysis */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BookOpen className="w-5 h-5" />
            <span>Performance Analysis</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="font-medium">Difficulty Level</div>
              <Badge variant="outline">{quiz.difficulty}</Badge>
            </div>
            
            <div className="space-y-2">
              <div className="font-medium">Average Time per Question</div>
              <div className="text-lg">{formatTime(Math.round(results.timeSpent / results.totalQuestions))}</div>
            </div>
          </div>
          
          <Separator />
          
          <div className="space-y-2">
            <div className="font-medium">Performance Feedback</div>
            <div className="text-gray-600">
              {results.score >= 90 && "Excellent work! You have a strong understanding of this topic."}
              {results.score >= 70 && results.score < 90 && "Good job! You have a solid grasp of the material with room for improvement."}
              {results.score >= 50 && results.score < 70 && "Fair performance. Consider reviewing the material and trying again."}
              {results.score < 50 && "Keep practicing! Review the explanations and try the quiz again to improve your understanding."}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-4 justify-center">
        <Button onClick={() => downloadResults('word')} className="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700">
          <FileText className="w-4 h-4" />
          <span>Download Word</span>
        </Button>
        
        <Button onClick={() => downloadResults('pdf')} className="flex items-center space-x-2 bg-red-600 hover:bg-red-700">
          <Download className="w-4 h-4" />
          <span>Download PDF</span>
        </Button>
        
        <Button onClick={onRetakeQuiz} variant="outline" className="flex items-center space-x-2">
          <RotateCcw className="w-4 h-4" />
          <span>Retake Quiz</span>
        </Button>
        
        <Button onClick={onBackToQuizzes} variant="outline" className="flex items-center space-x-2">
          <ArrowLeft className="w-4 h-4" />
          <span>Back to Quizzes</span>
        </Button>
      </div>

      {/* Detailed Question Review */}
      <Card>
        <CardHeader>
          <CardTitle>Question Review</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {quiz.questions.map((question, index) => {
            const userAnswer = results.answers.find(a => a.questionId === question.id);
            const isCorrect = userAnswer?.isCorrect || false;
            
            return (
              <div key={question.id} className="border rounded-lg p-4 space-y-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="font-medium">Question {index + 1}</span>
                      {isCorrect ? (
                        <CheckCircle className="w-5 h-5 text-green-500" />
                      ) : (
                        <XCircle className="w-5 h-5 text-red-500" />
                      )}
                    </div>
                    <p className="text-gray-800 mb-3">{question.question}</p>
                    
                    {question.options && question.options.length > 0 && (
                      <div className="space-y-1 mb-3">
                        {question.options.map((option, optionIndex) => {
                          const isSelected = userAnswer?.answer === option;
                          const isCorrectOption = option === question.correctAnswer;
                          
                          return (
                            <div
                              key={optionIndex}
                              className={`p-2 rounded text-sm ${
                                isSelected && isCorrect
                                  ? 'bg-green-100 text-green-800'
                                  : isSelected && !isCorrect
                                  ? 'bg-red-100 text-red-800'
                                  : isCorrectOption
                                  ? 'bg-blue-100 text-blue-800'
                                  : 'bg-gray-50'
                              }`}
                            >
                              {isSelected && '→ '}{option}
                              {isCorrectOption && ' ✓'}
                            </div>
                          );
                        })}
                      </div>
                    )}
                    
                    {(question.type === 'short-answer' || question.type === 'essay') && (
                      <div className="mb-3">
                        <div className="font-medium text-sm text-gray-600">Your Answer:</div>
                        <div className="p-2 bg-gray-50 rounded text-sm">
                          {userAnswer?.answer || 'No answer provided'}
                        </div>
                      </div>
                    )}
                    
                    <div className="text-sm">
                      <div className="font-medium text-gray-600 mb-1">Explanation:</div>
                      <div className="text-gray-700">{question.explanation}</div>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </CardContent>
      </Card>
    </div>
  );
}
