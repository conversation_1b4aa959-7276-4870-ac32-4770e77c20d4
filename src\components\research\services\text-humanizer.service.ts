/**
 * Academic Text Humanizer Service
 * Transforms AI-generated academic text to sound more natural and human-written
 * while maintaining academic rigor and scholarly tone.
 */

export interface HumanizationOptions {
  usePassiveVoice?: boolean;
  useSynonymReplacement?: boolean;
  addTransitions?: boolean;
  maintainCitations?: boolean;
  academicLevel?: 'undergraduate' | 'graduate' | 'professional';
}

export class TextHumanizerService {
  private contractionMap: Record<string, string> = {
    "n't": " not",
    "'re": " are", 
    "'s": " is",
    "'ll": " will",
    "'ve": " have",
    "'d": " would",
    "'m": " am"
  };

  private academicTransitions: string[] = [
    "Moreover,",
    "Additionally,", 
    "Furthermore,",
    "Hence,",
    "Therefore,",
    "Consequently,",
    "Nonetheless,",
    "Nevertheless,",
    "In addition,",
    "Similarly,",
    "Conversely,",
    "However,",
    "On the other hand,",
    "In contrast,",
    "Subsequently,",
    "As a result,",
    "In particular,",
    "Notably,",
    "Importantly,",
    "Significantly,"
  ];

  private formalSynonyms: Record<string, string[]> = {
    "show": ["demonstrate", "illustrate", "reveal", "indicate", "exhibit"],
    "use": ["utilize", "employ", "implement", "apply", "adopt"],
    "help": ["assist", "facilitate", "support", "aid", "contribute to"],
    "make": ["create", "establish", "generate", "produce", "construct"],
    "get": ["obtain", "acquire", "secure", "attain", "procure"],
    "big": ["substantial", "significant", "considerable", "extensive", "major"],
    "small": ["minimal", "limited", "modest", "minor", "negligible"],
    "good": ["effective", "beneficial", "advantageous", "favorable", "positive"],
    "bad": ["detrimental", "adverse", "negative", "unfavorable", "problematic"],
    "important": ["crucial", "essential", "vital", "significant", "critical"],
    "different": ["distinct", "diverse", "varied", "alternative", "contrasting"],
    "same": ["identical", "equivalent", "comparable", "similar", "analogous"]
  };

  /**
   * Main humanization method that applies various transformations
   */
  public async humanizeText(
    text: string, 
    options: HumanizationOptions = {}
  ): Promise<string> {
    const {
      usePassiveVoice = false,
      useSynonymReplacement = true,
      addTransitions = true,
      maintainCitations = true,
      academicLevel = 'graduate'
    } = options;

    // Split text into sentences
    const sentences = this.splitIntoSentences(text);
    const transformedSentences: string[] = [];

    for (let i = 0; i < sentences.length; i++) {
      let sentence = sentences[i].trim();
      
      if (!sentence) continue;

      // 1. Expand contractions for formal academic writing
      sentence = this.expandContractions(sentence);

      // 2. Add academic transitions (but not to every sentence)
      if (addTransitions && i > 0 && Math.random() < 0.3) {
        sentence = this.addAcademicTransition(sentence);
      }

      // 3. Vary sentence structure
      sentence = this.varySentenceStructure(sentence);

      // 4. Replace with more formal synonyms
      if (useSynonymReplacement && Math.random() < 0.4) {
        sentence = this.replaceFormalSynonyms(sentence);
      }

      // 5. Improve academic flow
      sentence = this.improveAcademicFlow(sentence, academicLevel);

      // 6. Maintain citations if present
      if (maintainCitations) {
        sentence = this.preserveCitations(sentence);
      }

      transformedSentences.push(sentence);
    }

    return transformedSentences.join(' ');
  }

  /**
   * Split text into sentences while preserving citations
   */
  private splitIntoSentences(text: string): string[] {
    // Handle citations like (Author, 2023) and avoid splitting on them
    const sentences = text
      .replace(/([.!?])\s+(?=[A-Z])/g, '$1|SPLIT|')
      .split('|SPLIT|')
      .filter(s => s.trim().length > 0);
    
    return sentences;
  }

  /**
   * Expand contractions for formal academic writing
   */
  private expandContractions(sentence: string): string {
    let expanded = sentence;
    
    for (const [contraction, expansion] of Object.entries(this.contractionMap)) {
      const regex = new RegExp(`\\b\\w+${contraction.replace("'", "\\'")}\\b`, 'gi');
      expanded = expanded.replace(regex, (match) => {
        const base = match.slice(0, -contraction.length);
        return base + expansion;
      });
    }
    
    return expanded;
  }

  /**
   * Add academic transitions to improve flow
   */
  private addAcademicTransition(sentence: string): string {
    const transition = this.academicTransitions[
      Math.floor(Math.random() * this.academicTransitions.length)
    ];
    
    // Don't add transition if sentence already starts with one
    const startsWithTransition = this.academicTransitions.some(t => 
      sentence.toLowerCase().startsWith(t.toLowerCase())
    );
    
    if (startsWithTransition) {
      return sentence;
    }
    
    return `${transition} ${sentence.charAt(0).toLowerCase() + sentence.slice(1)}`;
  }

  /**
   * Vary sentence structure to sound more natural
   */
  private varySentenceStructure(sentence: string): string {
    // Add variety by occasionally restructuring sentences
    if (Math.random() < 0.2) {
      // Convert some "This shows that..." to "The evidence suggests that..."
      sentence = sentence.replace(
        /^This (shows|demonstrates|indicates|reveals)/i,
        'The evidence $1'
      );
      
      // Convert some "We can see that..." to "It is evident that..."
      sentence = sentence.replace(
        /^We can (see|observe|note) that/i,
        'It is evident that'
      );
      
      // Convert some "It is important to note that..." variations
      sentence = sentence.replace(
        /^It is (important|crucial|essential) to (note|mention|observe) that/i,
        'Notably,'
      );
    }
    
    return sentence;
  }

  /**
   * Replace common words with more formal academic synonyms
   */
  private replaceFormalSynonyms(sentence: string): string {
    let transformed = sentence;
    
    for (const [informal, formals] of Object.entries(this.formalSynonyms)) {
      const regex = new RegExp(`\\b${informal}\\b`, 'gi');
      if (regex.test(transformed) && Math.random() < 0.5) {
        const replacement = formals[Math.floor(Math.random() * formals.length)];
        transformed = transformed.replace(regex, replacement);
        break; // Only replace one word per sentence to avoid over-formalization
      }
    }
    
    return transformed;
  }

  /**
   * Improve academic flow and readability
   */
  private improveAcademicFlow(sentence: string, level: string): string {
    let improved = sentence;
    
    // Add hedging language for academic caution
    if (Math.random() < 0.15) {
      improved = improved.replace(
        /^(This|The results|The findings|The data)/i,
        'The present findings'
      );
      
      improved = improved.replace(
        /\b(proves|shows definitively)\b/gi,
        'suggests'
      );
    }
    
    // Improve passive voice usage for academic writing
    if (level === 'professional' && Math.random() < 0.2) {
      improved = improved.replace(
        /^We (conducted|performed|carried out)/i,
        'The study was conducted'
      );
    }
    
    return improved;
  }

  /**
   * Preserve citations and references
   */
  private preserveCitations(sentence: string): string {
    // This method ensures citations like (Author, 2023) are preserved
    // and properly formatted according to academic standards
    
    // Ensure proper spacing around citations
    let preserved = sentence.replace(
      /\s*\(\s*([^)]+)\s*\)\s*/g,
      ' ($1) '
    );
    
    // Clean up extra spaces
    preserved = preserved.replace(/\s+/g, ' ').trim();
    
    return preserved;
  }

  /**
   * Generate humanization prompt for AI services
   */
  public generateHumanizationPrompt(
    text: string, 
    options: HumanizationOptions = {}
  ): string {
    const {
      academicLevel = 'graduate',
      maintainCitations = true
    } = options;

    return `
Please humanize the following academic text to make it sound more natural and human-written while maintaining academic quality and scholarly tone:

ORIGINAL TEXT:
"${text}"

HUMANIZATION REQUIREMENTS:
1. **Academic Rigor**: Maintain scholarly tone appropriate for ${academicLevel}-level academic writing
2. **Natural Flow**: Improve readability and natural progression of ideas
3. **Sentence Variety**: Use varied sentence structures and lengths to avoid monotony
4. **Smooth Transitions**: Add natural transitions between concepts where appropriate
5. **Formal Language**: Use appropriate academic vocabulary without being overly complex
6. **Human Touch**: Make the text sound like it was written by a knowledgeable human scholar
7. **Clarity**: Ensure ideas are expressed clearly and concisely
${maintainCitations ? '8. **Citations**: Preserve all citations and references exactly as they appear' : ''}

AVOID:
- Overly robotic or formulaic phrasing
- Repetitive sentence structures
- Unnecessarily complex vocabulary
- Loss of academic credibility
- Informal or casual language

Please provide the humanized version that maintains academic excellence while sounding naturally written by a human expert in the field.
`;
  }

  /**
   * Quick humanization for selected text
   */
  public async quickHumanize(text: string): Promise<string> {
    return this.humanizeText(text, {
      useSynonymReplacement: true,
      addTransitions: true,
      maintainCitations: true,
      academicLevel: 'graduate'
    });
  }
}
