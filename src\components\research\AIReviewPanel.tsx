import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  FileText, 
  CheckCircle, 
  AlertTriangle, 
  Lightbulb, 
  Search,
  BookOpen,
  Target,
  Zap,
  RefreshCw,
  Download,
  Share2
} from 'lucide-react';
import { toast } from 'sonner';
import { enhancedAIService } from './paper-generator/enhanced-ai.service';
import { tavilySearchService } from './research-search/services/tavily-search.service';

interface AIReviewPanelProps {
  documentContent: string;
  selectedText?: string;
  onContentUpdate?: (content: string) => void;
  className?: string;
}

interface ReviewResult {
  id: string;
  type: 'strength' | 'weakness' | 'suggestion' | 'citation';
  title: string;
  description: string;
  severity?: 'low' | 'medium' | 'high';
  section?: string;
}

interface SearchResult {
  title: string;
  content: string;
  url: string;
  relevance: number;
}

export function AIReviewPanel({
  documentContent,
  selectedText,
  onContentUpdate,
  className = ''
}: AIReviewPanelProps) {
  const [isReviewing, setIsReviewing] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [reviewResults, setReviewResults] = useState<ReviewResult[]>([]);
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [activeTab, setActiveTab] = useState('review');
  const [searchQuery, setSearchQuery] = useState('');

  const reviewTools = [
    {
      id: 'comprehensive',
      name: 'Comprehensive Review',
      description: 'Complete analysis of structure, content, and style',
      icon: <FileText className="h-4 w-4" />,
      color: 'blue'
    },
    {
      id: 'academic-style',
      name: 'Academic Style',
      description: 'Check academic writing conventions and tone',
      icon: <BookOpen className="h-4 w-4" />,
      color: 'green'
    },
    {
      id: 'citations',
      name: 'Citations & References',
      description: 'Verify citations and suggest improvements',
      icon: <Target className="h-4 w-4" />,
      color: 'purple'
    },
    {
      id: 'clarity',
      name: 'Clarity & Flow',
      description: 'Analyze readability and logical flow',
      icon: <Zap className="h-4 w-4" />,
      color: 'orange'
    }
  ];

  const handleReview = async (reviewType: string) => {
    if (!documentContent.trim()) {
      toast.error('No content to review');
      return;
    }

    setIsReviewing(true);
    setActiveTab('review');

    try {
      const content = selectedText || documentContent;
      let prompt = '';

      switch (reviewType) {
        case 'comprehensive':
          prompt = `Please provide a comprehensive review of this academic document. Analyze:
1. Structure and organization
2. Argument strength and clarity
3. Writing style and tone
4. Areas for improvement
5. Strengths to maintain

Content: ${content.slice(0, 3000)}...`;
          break;
        case 'academic-style':
          prompt = `Review this text for academic writing style. Check:
1. Formal tone and language
2. Academic conventions
3. Sentence structure
4. Vocabulary appropriateness
5. Professional presentation

Content: ${content.slice(0, 3000)}...`;
          break;
        case 'citations':
          prompt = `Analyze the citations and references in this text. Provide:
1. Citation format assessment
2. Missing citation opportunities
3. Reference quality evaluation
4. Suggestions for additional sources
5. Citation style consistency

Content: ${content.slice(0, 3000)}...`;
          break;
        case 'clarity':
          prompt = `Review this text for clarity and flow. Analyze:
1. Logical progression of ideas
2. Transition quality
3. Sentence clarity
4. Paragraph structure
5. Overall readability

Content: ${content.slice(0, 3000)}...`;
          break;
      }

      const response = await enhancedAIService.generateText(prompt, 'google/gemini-2.5-flash');
      
      // Parse the response into structured review results
      const results = parseReviewResponse(response, reviewType);
      setReviewResults(results);
      
      toast.success('Review completed successfully');
    } catch (error: any) {
      console.error('Review failed:', error);
      toast.error(`Review failed: ${error.message}`);
    } finally {
      setIsReviewing(false);
    }
  };

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      toast.error('Please enter a search query');
      return;
    }

    setIsSearching(true);
    setActiveTab('search');

    try {
      const results = await tavilySearchService.searchAcademic(searchQuery, {
        maxResults: 8,
        searchDepth: 'advanced',
        includeAnswer: true
      });

      const formattedResults: SearchResult[] = results.results.map(result => ({
        title: result.title,
        content: result.content,
        url: result.url,
        relevance: result.score || 0.8
      }));

      setSearchResults(formattedResults);
      toast.success(`Found ${formattedResults.length} relevant sources`);
    } catch (error: any) {
      console.error('Search failed:', error);
      toast.error(`Search failed: ${error.message}`);
    } finally {
      setIsSearching(false);
    }
  };

  const parseReviewResponse = (response: string, type: string): ReviewResult[] => {
    // Simple parsing logic - in a real implementation, this would be more sophisticated
    const lines = response.split('\n').filter(line => line.trim());
    const results: ReviewResult[] = [];
    
    lines.forEach((line, index) => {
      if (line.includes('✓') || line.includes('strength') || line.includes('good')) {
        results.push({
          id: `strength_${index}`,
          type: 'strength',
          title: 'Strength Identified',
          description: line.replace(/[✓•-]/g, '').trim(),
          section: type
        });
      } else if (line.includes('⚠') || line.includes('improve') || line.includes('consider')) {
        results.push({
          id: `suggestion_${index}`,
          type: 'suggestion',
          title: 'Improvement Suggestion',
          description: line.replace(/[⚠•-]/g, '').trim(),
          severity: 'medium',
          section: type
        });
      } else if (line.includes('❌') || line.includes('issue') || line.includes('problem')) {
        results.push({
          id: `weakness_${index}`,
          type: 'weakness',
          title: 'Issue Found',
          description: line.replace(/[❌•-]/g, '').trim(),
          severity: 'high',
          section: type
        });
      }
    });

    return results;
  };

  const getResultIcon = (type: string) => {
    switch (type) {
      case 'strength':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'weakness':
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case 'suggestion':
        return <Lightbulb className="h-4 w-4 text-yellow-600" />;
      case 'citation':
        return <BookOpen className="h-4 w-4 text-blue-600" />;
      default:
        return <FileText className="h-4 w-4 text-gray-600" />;
    }
  };

  const getResultColor = (type: string) => {
    switch (type) {
      case 'strength':
        return 'bg-green-50 border-green-200';
      case 'weakness':
        return 'bg-red-50 border-red-200';
      case 'suggestion':
        return 'bg-yellow-50 border-yellow-200';
      case 'citation':
        return 'bg-blue-50 border-blue-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  return (
    <Card className={`h-full flex flex-col ${className}`}>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-semibold flex items-center gap-2">
          <FileText className="h-5 w-5 text-blue-600" />
          AI Review & Research
        </CardTitle>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col space-y-4">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="review">AI Review</TabsTrigger>
            <TabsTrigger value="search">Research</TabsTrigger>
          </TabsList>

          <TabsContent value="review" className="flex-1 flex flex-col space-y-4">
            {/* Review Tools */}
            <div className="grid grid-cols-2 gap-2">
              {reviewTools.map((tool) => (
                <Button
                  key={tool.id}
                  variant="outline"
                  size="sm"
                  className="h-auto p-3 flex flex-col items-center gap-2 text-xs"
                  onClick={() => handleReview(tool.id)}
                  disabled={isReviewing}
                >
                  {tool.icon}
                  <span className="text-center">{tool.name}</span>
                </Button>
              ))}
            </div>

            {/* Review Results */}
            {reviewResults.length > 0 && (
              <ScrollArea className="flex-1">
                <div className="space-y-3">
                  {reviewResults.map((result) => (
                    <Card key={result.id} className={`p-3 ${getResultColor(result.type)}`}>
                      <div className="flex items-start gap-3">
                        {getResultIcon(result.type)}
                        <div className="flex-1">
                          <div className="font-medium text-sm">{result.title}</div>
                          <p className="text-xs text-gray-700 mt-1">{result.description}</p>
                          {result.section && (
                            <Badge variant="outline" className="mt-2 text-xs">
                              {result.section}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            )}

            {isReviewing && (
              <div className="flex items-center justify-center py-8">
                <RefreshCw className="h-6 w-6 animate-spin text-blue-600 mr-2" />
                <span className="text-sm text-gray-600">Analyzing document...</span>
              </div>
            )}
          </TabsContent>

          <TabsContent value="search" className="flex-1 flex flex-col space-y-4">
            {/* Search Input */}
            <div className="flex gap-2">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search academic sources..."
                className="flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
              <Button
                size="sm"
                onClick={handleSearch}
                disabled={isSearching || !searchQuery.trim()}
              >
                {isSearching ? <RefreshCw className="h-4 w-4 animate-spin" /> : <Search className="h-4 w-4" />}
              </Button>
            </div>

            {/* Search Results */}
            {searchResults.length > 0 && (
              <ScrollArea className="flex-1">
                <div className="space-y-3">
                  {searchResults.map((result, index) => (
                    <Card key={index} className="p-3 hover:bg-gray-50 transition-colors">
                      <div className="space-y-2">
                        <div className="flex items-start justify-between">
                          <h4 className="font-medium text-sm line-clamp-2">{result.title}</h4>
                          <Badge variant="outline" className="text-xs ml-2">
                            {Math.round(result.relevance * 100)}%
                          </Badge>
                        </div>
                        <p className="text-xs text-gray-600 line-clamp-3">{result.content}</p>
                        <div className="flex items-center justify-between">
                          <Button
                            variant="link"
                            size="sm"
                            className="h-auto p-0 text-xs"
                            onClick={() => window.open(result.url, '_blank')}
                          >
                            View Source
                          </Button>
                          <div className="flex gap-1">
                            <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                              <Download className="h-3 w-3" />
                            </Button>
                            <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                              <Share2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            )}

            {isSearching && (
              <div className="flex items-center justify-center py-8">
                <RefreshCw className="h-6 w-6 animate-spin text-blue-600 mr-2" />
                <span className="text-sm text-gray-600">Searching academic sources...</span>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
