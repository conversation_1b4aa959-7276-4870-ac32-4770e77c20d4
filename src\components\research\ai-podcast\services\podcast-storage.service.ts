import { supabase } from '@/lib/supabase';
import { 
  PodcastGeneration, 
  PodcastHistory, 
  PodcastHistoryResponse,
  PodcastGenerationResponse 
} from '../types';

/**
 * Service for managing podcast data in Supabase
 */
export class PodcastStorageService {
  private readonly TABLE_PODCASTS = 'ai_podcasts';
  private readonly TABLE_PODCAST_FILES = 'ai_podcast_files';
  private readonly BUCKET_NAME = 'podcast-files';

  /**
   * Save a podcast generation to the database
   */
  async savePodcast(podcast: PodcastGeneration): Promise<PodcastGenerationResponse> {
    try {
      const { data, error } = await supabase
        .from(this.TABLE_PODCASTS)
        .insert([
          {
            id: podcast.id,
            user_id: podcast.userId,
            title: podcast.title,
            input_source: podcast.inputSource,
            outline: podcast.outline,
            script: podcast.script,
            audio_url: podcast.audioUrl,
            status: podcast.status,
            progress: podcast.progress,
            error_message: podcast.error,
            metadata: podcast.metadata,
            voices: podcast.voices,
            provider: podcast.provider,
            created_at: podcast.createdAt.toISOString(),
            updated_at: podcast.updatedAt.toISOString()
          }
        ])
        .select()
        .single();

      if (error) {
        console.error('Error saving podcast:', error);
        return { success: false, error: error.message };
      }

      return { success: true, data: this.mapDatabaseToPodcast(data) };
    } catch (error) {
      console.error('Error saving podcast:', error);
      return { success: false, error: 'Failed to save podcast' };
    }
  }

  /**
   * Update an existing podcast
   */
  async updatePodcast(podcastId: string, updates: Partial<PodcastGeneration>): Promise<PodcastGenerationResponse> {
    try {
      const updateData: any = {
        updated_at: new Date().toISOString()
      };

      if (updates.title) updateData.title = updates.title;
      if (updates.outline) updateData.outline = updates.outline;
      if (updates.script) updateData.script = updates.script;
      if (updates.audioUrl) updateData.audio_url = updates.audioUrl;
      if (updates.status) updateData.status = updates.status;
      if (updates.progress !== undefined) updateData.progress = updates.progress;
      if (updates.error) updateData.error_message = updates.error;
      if (updates.metadata) updateData.metadata = updates.metadata;

      const { data, error } = await supabase
        .from(this.TABLE_PODCASTS)
        .update(updateData)
        .eq('id', podcastId)
        .select()
        .single();

      if (error) {
        console.error('Error updating podcast:', error);
        return { success: false, error: error.message };
      }

      return { success: true, data: this.mapDatabaseToPodcast(data) };
    } catch (error) {
      console.error('Error updating podcast:', error);
      return { success: false, error: 'Failed to update podcast' };
    }
  }

  /**
   * Get podcast by ID
   */
  async getPodcast(podcastId: string): Promise<PodcastGenerationResponse> {
    try {
      const { data, error } = await supabase
        .from(this.TABLE_PODCASTS)
        .select('*')
        .eq('id', podcastId)
        .single();

      if (error) {
        console.error('Error fetching podcast:', error);
        return { success: false, error: error.message };
      }

      return { success: true, data: this.mapDatabaseToPodcast(data) };
    } catch (error) {
      console.error('Error fetching podcast:', error);
      return { success: false, error: 'Failed to fetch podcast' };
    }
  }

  /**
   * Get user's podcast history
   */
  async getUserPodcasts(
    userId: string, 
    page: number = 1, 
    pageSize: number = 10,
    searchQuery?: string
  ): Promise<PodcastHistoryResponse> {
    try {
      let query = supabase
        .from(this.TABLE_PODCASTS)
        .select('*', { count: 'exact' })
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      // Add search filter if provided
      if (searchQuery) {
        query = query.or(`title.ilike.%${searchQuery}%,metadata->>tags.cs.{${searchQuery}}`);
      }

      // Add pagination
      const from = (page - 1) * pageSize;
      const to = from + pageSize - 1;
      query = query.range(from, to);

      const { data, error, count } = await query;

      if (error) {
        console.error('Error fetching user podcasts:', error);
        return { success: false, error: error.message };
      }

      const podcasts = data?.map(item => this.mapDatabaseToPodcast(item)) || [];

      const history: PodcastHistory = {
        podcasts,
        totalCount: count || 0,
        page,
        pageSize
      };

      return { success: true, data: history };
    } catch (error) {
      console.error('Error fetching user podcasts:', error);
      return { success: false, error: 'Failed to fetch podcast history' };
    }
  }

  /**
   * Delete a podcast
   */
  async deletePodcast(podcastId: string, userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      // First, delete associated files from storage
      await this.deleteAssociatedFiles(podcastId);

      // Then delete the podcast record
      const { error } = await supabase
        .from(this.TABLE_PODCASTS)
        .delete()
        .eq('id', podcastId)
        .eq('user_id', userId); // Ensure user can only delete their own podcasts

      if (error) {
        console.error('Error deleting podcast:', error);
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error) {
      console.error('Error deleting podcast:', error);
      return { success: false, error: 'Failed to delete podcast' };
    }
  }

  /**
   * Upload file to Supabase Storage
   */
  async uploadFile(file: File, userId: string, podcastId?: string): Promise<{ filePath: string | null; error: any }> {
    try {
      const fileExtension = file.name.split('.').pop();
      const fileName = `${userId}/${podcastId || 'temp'}/${Date.now()}.${fileExtension}`;

      const { data, error } = await supabase.storage
        .from(this.BUCKET_NAME)
        .upload(fileName, file);

      if (error) {
        console.error('Error uploading file:', error);
        return { filePath: null, error: error.message };
      }

      // Save file record to database
      await supabase
        .from(this.TABLE_PODCAST_FILES)
        .insert([
          {
            file_path: data.path,
            original_name: file.name,
            file_size: file.size,
            mime_type: file.type,
            user_id: userId,
            podcast_id: podcastId
          }
        ]);

      return { filePath: data.path, error: null };
    } catch (error) {
      console.error('Error uploading file:', error);
      return { filePath: null, error: 'Failed to upload file' };
    }
  }

  /**
   * Get public URL for a file
   */
  async getFileUrl(filePath: string): Promise<string | null> {
    try {
      const { data } = supabase.storage
        .from(this.BUCKET_NAME)
        .getPublicUrl(filePath);

      return data.publicUrl;
    } catch (error) {
      console.error('Error getting file URL:', error);
      return null;
    }
  }

  /**
   * Delete associated files from storage
   */
  private async deleteAssociatedFiles(podcastId: string): Promise<void> {
    try {
      // Get file records
      const { data: files } = await supabase
        .from(this.TABLE_PODCAST_FILES)
        .select('file_path')
        .eq('podcast_id', podcastId);

      if (files && files.length > 0) {
        // Delete files from storage
        const filePaths = files.map(f => f.file_path);
        await supabase.storage
          .from(this.BUCKET_NAME)
          .remove(filePaths);

        // Delete file records
        await supabase
          .from(this.TABLE_PODCAST_FILES)
          .delete()
          .eq('podcast_id', podcastId);
      }
    } catch (error) {
      console.error('Error deleting associated files:', error);
    }
  }

  /**
   * Map database record to PodcastGeneration object
   */
  private mapDatabaseToPodcast(data: any): PodcastGeneration {
    return {
      id: data.id,
      userId: data.user_id,
      title: data.title,
      inputSource: data.input_source,
      outline: data.outline,
      script: data.script,
      audioUrl: data.audio_url,
      status: data.status,
      progress: data.progress,
      error: data.error_message,
      metadata: data.metadata,
      voices: data.voices,
      provider: data.provider,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    };
  }

  /**
   * Get podcast statistics for a user
   */
  async getUserStats(userId: string): Promise<{
    totalPodcasts: number;
    totalDuration: number;
    completedPodcasts: number;
    recentActivity: Date | null;
  }> {
    try {
      const { data, error } = await supabase
        .from(this.TABLE_PODCASTS)
        .select('status, metadata, created_at')
        .eq('user_id', userId);

      if (error) {
        console.error('Error fetching user stats:', error);
        return { totalPodcasts: 0, totalDuration: 0, completedPodcasts: 0, recentActivity: null };
      }

      const totalPodcasts = data.length;
      const completedPodcasts = data.filter(p => p.status === 'completed').length;
      const totalDuration = data.reduce((sum, p) => sum + (p.metadata?.duration || 0), 0);
      const recentActivity = data.length > 0 ? new Date(Math.max(...data.map(p => new Date(p.created_at).getTime()))) : null;

      return {
        totalPodcasts,
        totalDuration,
        completedPodcasts,
        recentActivity
      };
    } catch (error) {
      console.error('Error fetching user stats:', error);
      return { totalPodcasts: 0, totalDuration: 0, completedPodcasts: 0, recentActivity: null };
    }
  }
}
