/**
 * Sample Text Manager Component
 * Handles adding, editing, and managing sample texts for style analysis
 */

import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Plus,
  Trash2,
  Edit3,
  FileText,
  CheckCircle,
  AlertCircle,
  BookOpen,
  Mail,
  FileBarChart,
  MessageSquare,
  GraduationCap
} from "lucide-react";
import { toast } from 'sonner';

import { useHumanizerStore } from '../stores/humanizer.store';
import { SAMPLE_TEXT_TEMPLATES } from '../constants';
import { SampleText, VALIDATION_LIMITS } from '../types';

export function SampleTextManager() {
  const {
    sampleTexts,
    lastError,
    addSampleText,
    removeSampleText,
    updateSampleText,
    clearSampleTexts,
    canAddSampleText,
    validateSampleText,
    clearError
  } = useHumanizerStore();

  const [isAdding, setIsAdding] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [newSampleContent, setNewSampleContent] = useState('');
  const [newSampleLabel, setNewSampleLabel] = useState('');

  /**
   * Handle adding a new sample text
   */
  const handleAddSample = () => {
    const validation = validateSampleText(newSampleContent, newSampleLabel);
    if (validation) {
      toast.error(validation);
      return;
    }

    addSampleText(newSampleContent, newSampleLabel);
    setNewSampleContent('');
    setNewSampleLabel('');
    setIsAdding(false);
    clearError();
    toast.success('Sample text added successfully!');
  };

  /**
   * Handle editing an existing sample text
   */
  const handleEditSample = (sample: SampleText) => {
    setEditingId(sample.id);
    setNewSampleContent(sample.content);
    setNewSampleLabel(sample.label);
  };

  /**
   * Handle saving edited sample text
   */
  const handleSaveEdit = () => {
    if (!editingId) return;

    const validation = validateSampleText(newSampleContent, newSampleLabel);
    if (validation) {
      toast.error(validation);
      return;
    }

    updateSampleText(editingId, newSampleContent, newSampleLabel);
    setEditingId(null);
    setNewSampleContent('');
    setNewSampleLabel('');
    clearError();
    toast.success('Sample text updated successfully!');
  };

  /**
   * Handle canceling add/edit operations
   */
  const handleCancel = () => {
    setIsAdding(false);
    setEditingId(null);
    setNewSampleContent('');
    setNewSampleLabel('');
    clearError();
  };

  /**
   * Handle removing a sample text
   */
  const handleRemoveSample = (id: string) => {
    removeSampleText(id);
    toast.success('Sample text removed');
  };

  /**
   * Handle using a template
   */
  const handleUseTemplate = (template: typeof SAMPLE_TEXT_TEMPLATES[0]) => {
    setNewSampleLabel(template.name);
    setNewSampleContent('');
    setIsAdding(true);
  };

  /**
   * Get icon for academic sample type
   */
  const getSampleIcon = (label: string) => {
    const lowerLabel = label.toLowerCase();
    if (lowerLabel.includes('research') || lowerLabel.includes('paper')) return FileText;
    if (lowerLabel.includes('dissertation') || lowerLabel.includes('thesis')) return GraduationCap;
    if (lowerLabel.includes('report') || lowerLabel.includes('document')) return FileBarChart;
    if (lowerLabel.includes('journal') || lowerLabel.includes('article')) return BookOpen;
    if (lowerLabel.includes('conference')) return MessageSquare;
    return GraduationCap; // Default to academic icon
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold text-gray-800 mb-2">Academic Writing Samples</h2>
        <p className="text-gray-600">
          Add examples of your academic writing to train the AI on your scholarly style.
          Include samples from research papers, dissertations, reports, or journal articles to help the AI understand your academic voice.
        </p>
      </div>

      {/* Error Alert */}
      {lastError && lastError.code.includes('VALIDATION') && (
        <Alert className="border-red-200 bg-red-50">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            {lastError.message}
          </AlertDescription>
        </Alert>
      )}

      {/* Templates Section */}
      {sampleTexts.length === 0 && !isAdding && (
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <FileText className="h-5 w-5 text-blue-600" />
              Quick Start Templates
            </CardTitle>
            <CardDescription>
              Choose a template to get started quickly
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {SAMPLE_TEXT_TEMPLATES.map((template) => (
                <Button
                  key={template.id}
                  variant="outline"
                  className="h-auto p-4 justify-start text-left"
                  onClick={() => handleUseTemplate(template)}
                >
                  <div>
                    <div className="font-medium">{template.name}</div>
                    <div className="text-sm text-gray-500 mt-1">
                      {template.description}
                    </div>
                  </div>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Add New Sample Form */}
      {(isAdding || editingId) && (
        <Card className="border-green-200 bg-green-50">
          <CardHeader>
            <CardTitle className="text-lg">
              {editingId ? 'Edit Sample Text' : 'Add New Sample Text'}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="sample-label">Label</Label>
              <Input
                id="sample-label"
                placeholder="e.g., Professional Email, Blog Post, Academic Paper"
                value={newSampleLabel}
                onChange={(e) => setNewSampleLabel(e.target.value)}
                maxLength={VALIDATION_LIMITS.MAX_SAMPLE_LABEL_LENGTH}
              />
              <p className="text-sm text-gray-500 mt-1">
                Give your sample a descriptive name
              </p>
            </div>

            <div>
              <Label htmlFor="sample-content">Content</Label>
              <Textarea
                id="sample-content"
                placeholder="Paste your writing sample here..."
                value={newSampleContent}
                onChange={(e) => setNewSampleContent(e.target.value)}
                rows={8}
                maxLength={VALIDATION_LIMITS.MAX_SAMPLE_TEXT_LENGTH}
              />
              <div className="flex justify-between text-sm text-gray-500 mt-1">
                <span>
                  Minimum {VALIDATION_LIMITS.MIN_SAMPLE_TEXT_LENGTH} characters required
                </span>
                <span>
                  {newSampleContent.length} / {VALIDATION_LIMITS.MAX_SAMPLE_TEXT_LENGTH}
                </span>
              </div>
            </div>

            <div className="flex gap-2">
              <Button
                onClick={editingId ? handleSaveEdit : handleAddSample}
                disabled={!newSampleContent.trim() || !newSampleLabel.trim()}
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                {editingId ? 'Save Changes' : 'Add Sample'}
              </Button>
              <Button variant="outline" onClick={handleCancel}>
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Existing Samples */}
      {sampleTexts.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">
              Your Writing Samples ({sampleTexts.length}/{VALIDATION_LIMITS.MAX_SAMPLE_TEXTS})
            </h3>
            <div className="flex gap-2">
              {canAddSampleText() && !isAdding && !editingId && (
                <Button
                  onClick={() => setIsAdding(true)}
                  size="sm"
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Sample
                </Button>
              )}
              {sampleTexts.length > 1 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    clearSampleTexts();
                    toast.success('All samples cleared');
                  }}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Clear All
                </Button>
              )}
            </div>
          </div>

          <div className="grid gap-4">
            {sampleTexts.map((sample) => {
              const Icon = getSampleIcon(sample.label);
              return (
                <Card key={sample.id} className="hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Icon className="h-5 w-5 text-blue-600" />
                        <CardTitle className="text-base">{sample.label}</CardTitle>
                        {sample.isAnalyzed && (
                          <Badge variant="secondary" className="bg-green-100 text-green-800">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Analyzed
                          </Badge>
                        )}
                      </div>
                      <div className="flex gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditSample(sample)}
                          disabled={editingId !== null || isAdding}
                        >
                          <Edit3 className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveSample(sample.id)}
                          disabled={editingId !== null || isAdding}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-sm text-gray-600 mb-2">
                      {sample.wordCount} words • Added {sample.addedAt.toLocaleDateString()}
                    </div>
                    <div className="text-sm bg-gray-50 p-3 rounded-md max-h-32 overflow-y-auto">
                      {sample.content.length > 200
                        ? `${sample.content.substring(0, 200)}...`
                        : sample.content
                      }
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      )}

      {/* Add First Sample CTA */}
      {sampleTexts.length === 0 && !isAdding && (
        <Card className="border-dashed border-2 border-gray-300">
          <CardContent className="flex flex-col items-center justify-center py-12 text-center">
            <FileText className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-semibold text-gray-600 mb-2">
              No sample texts yet
            </h3>
            <p className="text-gray-500 mb-4 max-w-md">
              Add examples of your writing to train the AI on your unique style and voice.
            </p>
            <Button onClick={() => setIsAdding(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Your First Sample
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Progress Indicator */}
      {sampleTexts.length > 0 && (
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-blue-800">
                Sample Collection Progress
              </span>
              <span className="text-sm text-blue-600">
                {sampleTexts.length} / {VALIDATION_LIMITS.MAX_SAMPLE_TEXTS}
              </span>
            </div>
            <div className="w-full bg-blue-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{
                  width: `${(sampleTexts.length / VALIDATION_LIMITS.MAX_SAMPLE_TEXTS) * 100}%`
                }}
              />
            </div>
            <p className="text-xs text-blue-700 mt-2">
              {sampleTexts.length >= 3
                ? 'Great! You have enough samples for good style analysis.'
                : 'Add more samples for better style analysis (recommended: 3+ samples).'}
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
