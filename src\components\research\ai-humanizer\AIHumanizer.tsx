/**
 * AI Humanizer - Main Component
 * Allows users to train an AI writing style model and apply it to rewrite content
 */

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import {
  Wand2,
  Upload,
  Edit3,
  Sparkles,
  Settings,
  Brain,
  FileText,
  AlertCircle,
  CheckCircle,
  Loader2,
  Eye,
  Download
} from "lucide-react";
import { toast } from 'sonner';

// Components
import { SampleTextManager } from './components/SampleTextManager';
import { ContentInput } from './components/ContentInput';
import { ResultsViewer } from './components/ResultsViewer';
import { HumanizerSettings } from './components/HumanizerSettings';
import { ProcessingStatus } from './components/ProcessingStatus';

// Store and services
import { useHumanizerStore } from './stores/humanizer.store';
import { GeminiHumanizerService } from './services/gemini-humanizer.service';

// Types and constants
import { UI_CONFIG } from './constants';

export function AIHumanizer() {
  const {
    // State
    sampleTexts,
    originalContent,
    humanizedContent,
    styleAnalysis,
    processingStatus,
    isProcessing,
    uiState,
    settings,
    lastError,
    
    // Actions
    setActiveTab,
    startProcessing,
    stopProcessing,
    setProcessingStatus,
    setStyleAnalysis,
    setHumanizedContent,
    setError,
    clearError,
    canProcessContent,
    saveCurrentSession
  } = useHumanizerStore();

  const [humanizerService] = useState(() => new GeminiHumanizerService());
  const [isServiceReady, setIsServiceReady] = useState(false);

  // Check service readiness on mount
  useEffect(() => {
    setIsServiceReady(humanizerService.isReady());
    if (!humanizerService.isReady()) {
      setError({
        code: 'SERVICE_NOT_READY',
        message: 'Gemini API is not properly configured. Please check your API key.',
        timestamp: new Date()
      });
    }
  }, [humanizerService, setError]);

  // Clear error when switching tabs
  useEffect(() => {
    clearError();
  }, [uiState.activeTab, clearError]);

  /**
   * Handle the complete humanization process
   */
  const handleHumanizeContent = async () => {
    if (!canProcessContent()) {
      toast.error('Please add sample texts and content before processing');
      return;
    }

    if (!isServiceReady) {
      toast.error('AI service is not ready. Please check your configuration.');
      return;
    }

    try {
      startProcessing();

      // Step 1: Analyze writing style
      setProcessingStatus({
        state: 'analyzing-samples',
        progress: 20,
        currentStep: 'Analyzing your writing style...',
        estimatedTimeRemaining: 30
      });

      const analysisResult = await humanizerService.analyzeWritingStyle(sampleTexts, {
        model: settings.preferredModel,
        creativityLevel: settings.creativityLevel,
        preserveFormatting: settings.preserveFormatting,
        maintainTechnicalTerms: settings.maintainTechnicalTerms
      });

      if (!analysisResult.success) {
        throw new Error(analysisResult.error?.message || 'Failed to analyze writing style');
      }

      setStyleAnalysis(analysisResult.data);

      // Step 2: Generate style profile
      setProcessingStatus({
        state: 'generating-profile',
        progress: 50,
        currentStep: 'Creating your personalized style profile...',
        estimatedTimeRemaining: 20
      });

      // Simulate profile generation delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Step 3: Humanize content
      setProcessingStatus({
        state: 'humanizing-content',
        progress: 75,
        currentStep: 'Rewriting content in your style...',
        estimatedTimeRemaining: 15
      });

      const humanizationResult = await humanizerService.humanizeContent(
        originalContent,
        sampleTexts,
        {
          model: settings.preferredModel,
          creativityLevel: settings.creativityLevel,
          preserveFormatting: settings.preserveFormatting,
          maintainTechnicalTerms: settings.maintainTechnicalTerms
        }
      );

      if (!humanizationResult.success) {
        throw new Error(humanizationResult.error?.message || 'Failed to humanize content');
      }

      setHumanizedContent(humanizationResult.data.humanizedContent);

      // Step 4: Finalize
      setProcessingStatus({
        state: 'complete',
        progress: 100,
        currentStep: 'Processing complete!',
        estimatedTimeRemaining: 0
      });

      // Auto-save session
      if (settings.autoSave) {
        saveCurrentSession();
      }

      // Switch to results tab
      setActiveTab('results');

      toast.success('Content successfully humanized!');

    } catch (error) {
      console.error('Humanization error:', error);
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      
      setError({
        code: 'HUMANIZATION_FAILED',
        message: errorMessage,
        timestamp: new Date()
      });

      toast.error(`Humanization failed: ${errorMessage}`);
    } finally {
      stopProcessing();
    }
  };

  /**
   * Get the appropriate tab content based on current state
   */
  const getTabContent = (tabId: string) => {
    switch (tabId) {
      case 'samples':
        return <SampleTextManager />;
      case 'content':
        return <ContentInput />;
      case 'results':
        return <ResultsViewer />;
      case 'settings':
        return <HumanizerSettings />;
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl text-white">
              <Wand2 className="h-8 w-8" />
            </div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Academic Writing Humanizer
            </h1>
          </div>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Train an AI to learn your unique academic writing style and apply it to research papers, dissertations, and scholarly content
          </p>
        </div>

        {/* Error Alert */}
        {lastError && (
          <Alert className="mb-6 border-red-200 bg-red-50">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              {lastError.message}
            </AlertDescription>
          </Alert>
        )}

        {/* Processing Status */}
        {isProcessing && (
          <Card className="mb-6 border-blue-200 bg-blue-50">
            <CardContent className="pt-6">
              <ProcessingStatus />
            </CardContent>
          </Card>
        )}

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Main Panel */}
          <div className="lg:col-span-3">
            <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="border-b bg-gradient-to-r from-blue-50 to-purple-50">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-xl text-gray-800">Academic Writing Style Humanizer</CardTitle>
                    <CardDescription>
                      Train AI on your academic writing style for research papers, dissertations, and scholarly content
                    </CardDescription>
                  </div>
                  {canProcessContent() && !isProcessing && (
                    <Button
                      onClick={handleHumanizeContent}
                      className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
                      size="lg"
                    >
                      <Sparkles className="h-4 w-4 mr-2" />
                      Humanize Content
                    </Button>
                  )}
                </div>
              </CardHeader>

              <CardContent className="p-0">
                <Tabs value={uiState.activeTab} onValueChange={setActiveTab}>
                  <TabsList className="grid w-full grid-cols-4 bg-gray-50 p-1 m-4 rounded-lg">
                    {UI_CONFIG.TABS.map((tab) => {
                      const Icon = tab.icon;
                      return (
                        <TabsTrigger
                          key={tab.id}
                          value={tab.id}
                          className="flex items-center gap-2 data-[state=active]:bg-white data-[state=active]:shadow-sm"
                        >
                          <Icon className="h-4 w-4" />
                          <span className="hidden sm:inline">{tab.name}</span>
                        </TabsTrigger>
                      );
                    })}
                  </TabsList>

                  {UI_CONFIG.TABS.map((tab) => (
                    <TabsContent key={tab.id} value={tab.id} className="p-6">
                      {getTabContent(tab.id)}
                    </TabsContent>
                  ))}
                </Tabs>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="space-y-6">
              {/* Quick Stats */}
              <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Brain className="h-5 w-5 text-blue-600" />
                    Quick Stats
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Sample Texts</span>
                    <span className="font-semibold text-blue-600">{sampleTexts.length}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Content Words</span>
                    <span className="font-semibold text-purple-600">
                      {originalContent ? originalContent.split(/\s+/).length : 0}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Style Analyzed</span>
                    <span className="font-semibold">
                      {styleAnalysis ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : (
                        <span className="text-gray-400">No</span>
                      )}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Ready to Process</span>
                    <span className="font-semibold">
                      {canProcessContent() ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : (
                        <span className="text-gray-400">No</span>
                      )}
                    </span>
                  </div>
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <Card className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Sparkles className="h-5 w-5 text-purple-600" />
                    Quick Actions
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full justify-start"
                    onClick={() => setActiveTab('samples')}
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    Add Samples
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full justify-start"
                    onClick={() => setActiveTab('content')}
                  >
                    <Edit3 className="h-4 w-4 mr-2" />
                    Add Content
                  </Button>
                  {humanizedContent && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full justify-start"
                      onClick={() => setActiveTab('results')}
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      View Results
                    </Button>
                  )}
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full justify-start"
                    onClick={() => setActiveTab('settings')}
                  >
                    <Settings className="h-4 w-4 mr-2" />
                    Settings
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
