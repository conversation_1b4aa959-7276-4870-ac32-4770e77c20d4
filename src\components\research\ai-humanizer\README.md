# AI Humanizer Module

A comprehensive AI-powered writing style humanizer that allows users to train an AI model on their personal writing samples and apply that learned style to rewrite new content.

## Features

### Core Functionality
- **Sample Text Collection**: Multiple input containers for user writing samples
- **Style Analysis**: AI-powered analysis of writing patterns, tone, and voice
- **Content Humanization**: Rewrite content to match learned writing style
- **Real-time Processing**: Live progress tracking with detailed status updates
- **Results Comparison**: Side-by-side view of original vs. humanized content

### Technical Features
- **Google Gemini 2.5 Pro Integration**: Advanced AI for style analysis and content rewriting
- **Intelligent Validation**: Word count limits and content validation
- **Persistent Settings**: User preferences saved across sessions
- **Error Handling**: Comprehensive error management and user feedback
- **Responsive Design**: Modern UI that works on all devices

## Architecture

### Components Structure
```
ai-humanizer/
├── AIHumanizer.tsx              # Main component
├── components/
│   ├── SampleTextManager.tsx    # Manage writing samples
│   ├── ContentInput.tsx         # Input content to humanize
│   ├── ResultsViewer.tsx        # Display results and comparison
│   ├── StyleAnalysisViewer.tsx  # Show style analysis details
│   ├── ProcessingStatus.tsx     # Real-time processing status
│   ├── HumanizerSettings.tsx    # Configuration settings
│   └── index.ts                 # Component exports
├── services/
│   └── gemini-humanizer.service.ts  # Gemini API integration
├── stores/
│   └── humanizer.store.ts       # Zustand state management
├── types.ts                     # TypeScript definitions
├── constants.ts                 # Configuration constants
└── index.ts                     # Module exports
```

### State Management
Uses Zustand with persistence for:
- Sample text management
- Content storage
- Processing status
- UI state
- User settings
- Session management

### API Integration
- **Google Gemini 2.5 Pro**: Primary model for style analysis and humanization
- **Google Gemini 2.5 Flash**: Alternative faster model option
- **Environment Variables**: Uses `GEMINI_API_KEY` from .env file

## Usage

### Basic Usage
```tsx
import { AIHumanizer } from '@/components/research/ai-humanizer';

function App() {
  return <AIHumanizer />;
}
```

### Store Usage
```tsx
import { useHumanizerStore } from '@/components/research/ai-humanizer';

function CustomComponent() {
  const {
    sampleTexts,
    addSampleText,
    humanizedContent,
    canProcessContent
  } = useHumanizerStore();

  // Use store methods and state
}
```

## Configuration

### Environment Variables
```env
# OpenRouter API Key (Primary - supports all models)
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Gemini API Key (Optional - for direct Google access)
GEMINI_API_KEY=your_gemini_api_key_here
```

### Settings Options
- **AI Model**: Choose between Gemini 2.5 Pro and Flash
- **Creativity Level**: Conservative, Moderate, or Creative
- **Output Style**: Natural, Enhanced, or Creative
- **Content Preservation**: Formatting and technical terms
- **User Experience**: Auto-save and analysis details

## Validation Limits

- **Sample Texts**: Maximum 10 samples
- **Sample Length**: 50-5,000 characters each
- **Content Length**: 10 characters to 4,000 words
- **Label Length**: Maximum 100 characters

## Processing Flow

1. **Sample Collection**: User adds 1-10 writing samples
2. **Style Analysis**: AI analyzes writing patterns and characteristics
3. **Profile Generation**: Creates personalized style profile
4. **Content Humanization**: Rewrites input content using learned style
5. **Results Display**: Shows comparison and analysis

## API Prompts

### Style Analysis Prompt
Analyzes sample texts to extract:
- Tone and voice characteristics
- Vocabulary complexity and patterns
- Sentence structure preferences
- Writing patterns and style elements

### Humanization Prompt
Applies learned style to new content while:
- Maintaining original meaning
- Preserving technical terms
- Applying consistent style
- Ensuring natural flow

## Error Handling

- **Service Configuration**: Validates Gemini API setup
- **Input Validation**: Checks content length and format
- **API Errors**: Graceful handling of API failures
- **User Feedback**: Clear error messages and guidance

## Performance

- **Streaming Support**: Real-time processing updates
- **Efficient Storage**: Optimized state management
- **Lazy Loading**: Components loaded as needed
- **Memory Management**: Proper cleanup and disposal

## Accessibility

- **Keyboard Navigation**: Full keyboard support
- **Screen Readers**: Proper ARIA labels and descriptions
- **Color Contrast**: Meets WCAG guidelines
- **Focus Management**: Clear focus indicators

## Browser Support

- **Modern Browsers**: Chrome, Firefox, Safari, Edge
- **Mobile Support**: Responsive design for all devices
- **Progressive Enhancement**: Graceful degradation

## Future Enhancements

- **Multiple AI Models**: Support for additional AI providers
- **Export Formats**: PDF, DOCX, HTML export options
- **Batch Processing**: Process multiple documents
- **Style Templates**: Pre-built writing style templates
- **Collaboration**: Share styles between users
- **Analytics**: Detailed usage and improvement metrics

## Dependencies

- **React**: UI framework
- **Zustand**: State management
- **Google Generative AI**: Gemini API client
- **Lucide React**: Icons
- **Tailwind CSS**: Styling
- **Sonner**: Toast notifications

## Contributing

1. Follow existing code patterns
2. Add proper TypeScript types
3. Include error handling
4. Write comprehensive tests
5. Update documentation

## License

Part of the Paper Genius Platform - All rights reserved.
