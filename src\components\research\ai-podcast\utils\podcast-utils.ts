import { PodcastGeneration, PodcastInputSource, VoiceOption } from '../types';

/**
 * Utility functions for podcast operations
 */

export class PodcastUtils {
  /**
   * Generate a unique podcast ID
   */
  static generateId(): string {
    return `podcast_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate a podcast title from input source
   */
  static generateTitle(inputSource: PodcastInputSource): string {
    if (inputSource.metadata?.title) {
      return inputSource.metadata.title;
    }

    switch (inputSource.type) {
      case 'topic':
        return this.titleFromTopic(inputSource.content);
      case 'link':
        return this.titleFromLink(inputSource.content);
      case 'file':
        return this.titleFromFile(inputSource);
      case 'text':
        return this.titleFromText(inputSource.content);
      default:
        return 'AI Generated Podcast';
    }
  }

  /**
   * Generate title from topic
   */
  private static titleFromTopic(topic: string): string {
    const cleanTopic = topic.trim();
    if (cleanTopic.length <= 60) {
      return cleanTopic;
    }
    return cleanTopic.substring(0, 57) + '...';
  }

  /**
   * Generate title from link
   */
  private static titleFromLink(url: string): string {
    try {
      const urlObj = new URL(url);
      const domain = urlObj.hostname.replace('www.', '');
      return `Discussion: Content from ${domain}`;
    } catch {
      return 'Discussion: Web Content';
    }
  }

  /**
   * Generate title from file
   */
  private static titleFromFile(inputSource: PodcastInputSource): string {
    const fileName = inputSource.metadata?.fileName;
    if (fileName) {
      const nameWithoutExt = fileName.replace(/\.[^/.]+$/, '');
      return `Discussion: ${nameWithoutExt}`;
    }
    return 'Discussion: Uploaded Document';
  }

  /**
   * Generate title from text
   */
  private static titleFromText(text: string): string {
    const firstLine = text.split('\n')[0]?.trim();
    if (firstLine && firstLine.length > 10 && firstLine.length <= 60) {
      return firstLine;
    }
    
    const words = text.trim().split(/\s+/).slice(0, 8);
    return words.join(' ') + (text.split(/\s+/).length > 8 ? '...' : '');
  }

  /**
   * Extract tags from content
   */
  static extractTags(content: string): string[] {
    const commonKeywords = [
      'AI', 'technology', 'science', 'business', 'education', 'health',
      'environment', 'politics', 'economics', 'culture', 'innovation',
      'research', 'development', 'analysis', 'strategy', 'future',
      'digital', 'data', 'machine learning', 'automation', 'sustainability'
    ];

    const contentLower = content.toLowerCase();
    const foundTags = commonKeywords.filter(keyword => 
      contentLower.includes(keyword.toLowerCase())
    );

    // Add some generic tags based on content characteristics
    const wordCount = content.split(/\s+/).length;
    if (wordCount > 1000) foundTags.push('in-depth');
    if (content.includes('research') || content.includes('study')) foundTags.push('research');
    if (content.includes('future') || content.includes('trend')) foundTags.push('trends');

    return [...new Set(foundTags)].slice(0, 5);
  }

  /**
   * Estimate podcast duration from content
   */
  static estimateDuration(content: string): number {
    const wordCount = content.split(/\s+/).length;
    // Base calculation: ~150 words per minute of speech
    const baseDuration = (wordCount / 150) * 60;
    
    // Add overhead for discussion, questions, transitions
    const discussionMultiplier = 1.8;
    
    // Minimum 2 minutes, maximum 20 minutes
    const estimatedDuration = Math.max(120, Math.min(1200, baseDuration * discussionMultiplier));
    
    return Math.round(estimatedDuration);
  }

  /**
   * Validate voice combination
   */
  static validateVoiceCombination(host1: VoiceOption, host2: VoiceOption): {
    valid: boolean;
    warnings?: string[];
    error?: string;
  } {
    const warnings: string[] = [];

    // Check if voices are from the same provider
    if (host1.provider.id !== host2.provider.id) {
      return {
        valid: false,
        error: 'Both voices must be from the same audio provider'
      };
    }

    // Check if voices are the same
    if (host1.id === host2.id) {
      return {
        valid: false,
        error: 'Please select different voices for each host'
      };
    }

    // Check language compatibility
    if (host1.language !== host2.language) {
      warnings.push('Voices are in different languages - this may affect podcast quality');
    }

    // Recommend gender diversity
    if (host1.gender === host2.gender) {
      warnings.push('Consider using voices of different genders for better conversation dynamics');
    }

    return { valid: true, warnings };
  }

  /**
   * Format podcast for sharing
   */
  static formatForSharing(podcast: PodcastGeneration): {
    title: string;
    description: string;
    url: string;
  } {
    const description = `Listen to this AI-generated podcast: "${podcast.title}". ` +
      `Duration: ${this.formatDuration(podcast.metadata.duration || 0)}. ` +
      `Generated using ${podcast.provider.name} with ${podcast.voices.host1.name} and ${podcast.voices.host2.name}.`;

    return {
      title: podcast.title,
      description,
      url: window.location.href
    };
  }

  /**
   * Format duration to readable string
   */
  static formatDuration(seconds: number): string {
    if (isNaN(seconds) || seconds < 0) return '0:00';
    
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }

  /**
   * Get content preview
   */
  static getContentPreview(content: string, maxLength: number = 200): string {
    if (content.length <= maxLength) return content;
    
    const truncated = content.substring(0, maxLength);
    const lastSpace = truncated.lastIndexOf(' ');
    
    if (lastSpace > maxLength * 0.8) {
      return truncated.substring(0, lastSpace) + '...';
    }
    
    return truncated + '...';
  }

  /**
   * Validate input source
   */
  static validateInputSource(inputSource: PodcastInputSource): {
    valid: boolean;
    error?: string;
    warnings?: string[];
  } {
    const warnings: string[] = [];

    if (!inputSource.content || inputSource.content.trim().length === 0) {
      return { valid: false, error: 'Content cannot be empty' };
    }

    const contentLength = inputSource.content.trim().length;
    
    if (contentLength < 50) {
      return { valid: false, error: 'Content is too short for meaningful podcast generation' };
    }

    if (contentLength > 50000) {
      warnings.push('Content is very long and may result in a lengthy podcast');
    }

    const wordCount = inputSource.content.split(/\s+/).length;
    if (wordCount < 25) {
      return { valid: false, error: 'Content has too few words for podcast generation' };
    }

    if (wordCount > 10000) {
      warnings.push('High word count may result in a very long podcast');
    }

    // Type-specific validations
    switch (inputSource.type) {
      case 'link':
        try {
          new URL(inputSource.content);
        } catch {
          return { valid: false, error: 'Invalid URL format' };
        }
        break;
      
      case 'file':
        if (!inputSource.metadata?.fileName) {
          return { valid: false, error: 'File metadata is missing' };
        }
        break;
    }

    return { valid: true, warnings };
  }

  /**
   * Get recommended settings based on content
   */
  static getRecommendedSettings(inputSource: PodcastInputSource): {
    estimatedDuration: number;
    recommendedVoices: string[];
    suggestedTags: string[];
  } {
    const content = inputSource.content;
    const estimatedDuration = this.estimateDuration(content);
    const suggestedTags = this.extractTags(content);

    // Recommend voice types based on content
    let recommendedVoices: string[] = [];
    
    if (content.toLowerCase().includes('research') || content.toLowerCase().includes('academic')) {
      recommendedVoices = ['Professional', 'Narrator', 'Academic'];
    } else if (content.toLowerCase().includes('business') || content.toLowerCase().includes('corporate')) {
      recommendedVoices = ['Professional', 'Executive', 'Business'];
    } else if (content.toLowerCase().includes('technology') || content.toLowerCase().includes('tech')) {
      recommendedVoices = ['Tech Expert', 'Enthusiastic', 'Modern'];
    } else {
      recommendedVoices = ['Conversational', 'Friendly', 'Engaging'];
    }

    return {
      estimatedDuration,
      recommendedVoices,
      suggestedTags
    };
  }
}
