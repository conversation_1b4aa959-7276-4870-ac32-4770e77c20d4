import { saveAs } from 'file-saver';
import { Document, Packer, Paragraph, TextRun, HeadingLevel, AlignmentType } from 'docx';
import { Quiz, QuizQuestion } from '../types';

interface QuizResults {
  score: number;
  totalQuestions: number;
  correctAnswers: number;
  timeSpent: number;
  answers: Array<{
    questionId: string;
    answer: string;
    isCorrect: boolean;
    timeSpent: number;
  }>;
}

class QuizExportService {
  /**
   * Export quiz results to Word document
   */
  async exportToWord(quiz: Quiz, results: QuizResults): Promise<void> {
    try {
      const doc = new Document({
        sections: [{
          properties: {},
          children: [
            // Title
            new Paragraph({
              children: [
                new TextRun({
                  text: quiz.title,
                  bold: true,
                  size: 32,
                })
              ],
              heading: HeadingLevel.TITLE,
              alignment: AlignmentType.CENTER,
              spacing: { after: 400 }
            }),

            // Quiz Summary
            new Paragraph({
              children: [
                new TextRun({
                  text: "Quiz Summary",
                  bold: true,
                  size: 24,
                })
              ],
              heading: HeadingLevel.HEADING_1,
              spacing: { before: 400, after: 200 }
            }),

            new Paragraph({
              children: [
                new TextRun({ text: `Difficulty: `, bold: true }),
                new TextRun({ text: quiz.difficulty })
              ],
              spacing: { after: 100 }
            }),

            new Paragraph({
              children: [
                new TextRun({ text: `Total Questions: `, bold: true }),
                new TextRun({ text: quiz.questions.length.toString() })
              ],
              spacing: { after: 100 }
            }),

            new Paragraph({
              children: [
                new TextRun({ text: `Score: `, bold: true }),
                new TextRun({ text: `${results.score}% (${results.correctAnswers}/${results.totalQuestions})` })
              ],
              spacing: { after: 100 }
            }),

            new Paragraph({
              children: [
                new TextRun({ text: `Time Spent: `, bold: true }),
                new TextRun({ text: this.formatTime(results.timeSpent) })
              ],
              spacing: { after: 100 }
            }),

            new Paragraph({
              children: [
                new TextRun({ text: `Completed: `, bold: true }),
                new TextRun({ text: new Date().toLocaleString() })
              ],
              spacing: { after: 400 }
            }),

            // Questions and Answers
            new Paragraph({
              children: [
                new TextRun({
                  text: "Questions and Answers",
                  bold: true,
                  size: 24,
                })
              ],
              heading: HeadingLevel.HEADING_1,
              spacing: { before: 400, after: 200 }
            }),

            ...this.generateQuestionParagraphs(quiz.questions, results.answers)
          ]
        }]
      });

      const buffer = await Packer.toBuffer(doc);
      const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' });
      const fileName = `${quiz.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_results.docx`;
      saveAs(blob, fileName);

    } catch (error) {
      console.error('Error exporting to Word:', error);
      throw new Error('Failed to export quiz results to Word document');
    }
  }

  /**
   * Export quiz results to PDF (via HTML)
   */
  async exportToPDF(quiz: Quiz, results: QuizResults): Promise<void> {
    try {
      const htmlContent = this.generateHTMLContent(quiz, results);
      
      // Create a new window for printing
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        throw new Error('Unable to open print window. Please allow popups.');
      }

      printWindow.document.write(htmlContent);
      printWindow.document.close();
      
      // Wait for content to load then print
      printWindow.onload = () => {
        printWindow.print();
        printWindow.close();
      };

    } catch (error) {
      console.error('Error exporting to PDF:', error);
      throw new Error('Failed to export quiz results to PDF');
    }
  }

  /**
   * Generate HTML content for PDF export
   */
  private generateHTMLContent(quiz: Quiz, results: QuizResults): string {
    const questionsHTML = quiz.questions.map((question, index) => {
      const userAnswer = results.answers.find(a => a.questionId === question.id);
      const isCorrect = userAnswer?.isCorrect || false;
      
      let optionsHTML = '';
      if (question.options && question.options.length > 0) {
        optionsHTML = question.options.map(option => {
          const isSelected = userAnswer?.answer === option;
          const isCorrectOption = option === question.correctAnswer;
          let className = '';
          
          if (isSelected && isCorrect) className = 'correct-answer';
          else if (isSelected && !isCorrect) className = 'incorrect-answer';
          else if (isCorrectOption) className = 'correct-option';
          
          return `<div class="option ${className}">
            ${isSelected ? '→ ' : ''}${option}
            ${isCorrectOption ? ' ✓' : ''}
          </div>`;
        }).join('');
      }

      return `
        <div class="question-block">
          <h3>Question ${index + 1}</h3>
          <p class="question-text">${question.question}</p>
          
          ${optionsHTML ? `<div class="options">${optionsHTML}</div>` : ''}
          
          ${question.type === 'short-answer' || question.type === 'essay' ? 
            `<div class="user-answer">
              <strong>Your Answer:</strong> ${userAnswer?.answer || 'No answer provided'}
            </div>` : ''
          }
          
          <div class="result ${isCorrect ? 'correct' : 'incorrect'}">
            <strong>Result:</strong> ${isCorrect ? '✅ Correct' : '❌ Incorrect'}
          </div>
          
          <div class="explanation">
            <strong>Explanation:</strong> ${question.explanation}
          </div>
        </div>
      `;
    }).join('');

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>${quiz.title} - Results</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
          .header { text-align: center; margin-bottom: 30px; }
          .title { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
          .summary { background: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 30px; }
          .summary-item { margin-bottom: 5px; }
          .question-block { margin-bottom: 30px; border-bottom: 1px solid #eee; padding-bottom: 20px; }
          .question-text { font-weight: bold; margin-bottom: 10px; }
          .options { margin: 10px 0; }
          .option { padding: 5px; margin: 2px 0; }
          .correct-answer { background: #d4edda; color: #155724; }
          .incorrect-answer { background: #f8d7da; color: #721c24; }
          .correct-option { background: #d1ecf1; color: #0c5460; }
          .result.correct { color: #28a745; }
          .result.incorrect { color: #dc3545; }
          .explanation { background: #e9ecef; padding: 10px; border-radius: 3px; margin-top: 10px; }
          .user-answer { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 3px; }
          @media print { body { margin: 0; } }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="title">${quiz.title}</div>
          <div>Quiz Results</div>
        </div>
        
        <div class="summary">
          <div class="summary-item"><strong>Difficulty:</strong> ${quiz.difficulty}</div>
          <div class="summary-item"><strong>Total Questions:</strong> ${quiz.questions.length}</div>
          <div class="summary-item"><strong>Score:</strong> ${results.score}% (${results.correctAnswers}/${results.totalQuestions})</div>
          <div class="summary-item"><strong>Time Spent:</strong> ${this.formatTime(results.timeSpent)}</div>
          <div class="summary-item"><strong>Completed:</strong> ${new Date().toLocaleString()}</div>
        </div>
        
        <h2>Questions and Answers</h2>
        ${questionsHTML}
      </body>
      </html>
    `;
  }

  /**
   * Generate question paragraphs for Word document
   */
  private generateQuestionParagraphs(questions: QuizQuestion[], answers: QuizResults['answers']): Paragraph[] {
    const paragraphs: Paragraph[] = [];

    questions.forEach((question, index) => {
      const userAnswer = answers.find(a => a.questionId === question.id);
      const isCorrect = userAnswer?.isCorrect || false;

      // Question number and text
      paragraphs.push(
        new Paragraph({
          children: [
            new TextRun({
              text: `Question ${index + 1}`,
              bold: true,
              size: 20,
            })
          ],
          spacing: { before: 300, after: 100 }
        }),
        new Paragraph({
          children: [
            new TextRun({ text: question.question })
          ],
          spacing: { after: 100 }
        })
      );

      // Options (for multiple choice)
      if (question.options && question.options.length > 0) {
        question.options.forEach(option => {
          const isSelected = userAnswer?.answer === option;
          const isCorrectOption = option === question.correctAnswer;
          
          paragraphs.push(
            new Paragraph({
              children: [
                new TextRun({ 
                  text: `${isSelected ? '→ ' : '  '}${option}${isCorrectOption ? ' ✓' : ''}`,
                  color: isSelected && isCorrect ? '28a745' : 
                         isSelected && !isCorrect ? 'dc3545' : 
                         isCorrectOption ? '0c5460' : '000000'
                })
              ],
              spacing: { after: 50 }
            })
          );
        });
      }

      // User answer for text questions
      if (question.type === 'short-answer' || question.type === 'essay') {
        paragraphs.push(
          new Paragraph({
            children: [
              new TextRun({ text: 'Your Answer: ', bold: true }),
              new TextRun({ text: userAnswer?.answer || 'No answer provided' })
            ],
            spacing: { after: 100 }
          })
        );
      }

      // Result
      paragraphs.push(
        new Paragraph({
          children: [
            new TextRun({ text: 'Result: ', bold: true }),
            new TextRun({ 
              text: isCorrect ? '✅ Correct' : '❌ Incorrect',
              color: isCorrect ? '28a745' : 'dc3545'
            })
          ],
          spacing: { after: 100 }
        })
      );

      // Explanation
      paragraphs.push(
        new Paragraph({
          children: [
            new TextRun({ text: 'Explanation: ', bold: true }),
            new TextRun({ text: question.explanation })
          ],
          spacing: { after: 200 }
        })
      );
    });

    return paragraphs;
  }

  /**
   * Format time in minutes and seconds
   */
  private formatTime(seconds: number): string {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  }
}

export const quizExportService = new QuizExportService();
