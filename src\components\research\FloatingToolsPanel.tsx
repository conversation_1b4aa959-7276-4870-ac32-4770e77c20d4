import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  X, 
  Replace, 
  Plus, 
  Eye, 
  Loader2,
  Type,
  Target,
  BookOpen,
  Sparkles,
  FlaskConical,
  Lightbulb,
  Search,
  TrendingUp,
  CheckCircle,
  FileText,
  BarChart3,
  FileCheck,
  Edit,
  PenTool,
  AlignLeft,
  List,
  Repeat,
  Wand2
} from 'lucide-react';
import { toast } from 'sonner';
import { enhancedAIService, RESEARCH_TOOLS, ResearchTool } from './paper-generator/enhanced-ai.service';
import { TextHumanizerService } from './services/text-humanizer.service';
import './academic-interface.css';

interface FloatingToolsPanelProps {
  isVisible: boolean;
  onClose: () => void;
  onToolExecute: (toolId: string, mode: 'replace' | 'cursor' | 'popup') => void;
  selectedText: string;
  documentContent: string;
  aiLoading: boolean;
  position?: { x: number; y: number };
}

export function FloatingToolsPanel({
  isVisible,
  onClose,
  onToolExecute,
  selectedText,
  documentContent,
  aiLoading,
  position = { x: 100, y: 100 }
}: FloatingToolsPanelProps) {
  const [processingTool, setProcessingTool] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('quick');
  
  const humanizerService = new TextHumanizerService();

  if (!isVisible) return null;

  // Enhanced tool categories with better organization
  const toolCategories = [
    {
      id: 'quick',
      name: 'Quick Actions',
      tools: [
        {
          id: 'humanize',
          name: 'Humanize Text',
          description: 'Make AI text sound more natural and human-written',
          icon: 'Wand2',
          category: 'enhancement',
          requiresSelection: true,
          mode: 'replace' as const
        },
        {
          id: 'improve-clarity',
          name: 'Improve Clarity',
          description: 'Enhance readability and flow',
          icon: 'Sparkles',
          category: 'enhancement',
          requiresSelection: true,
          mode: 'replace' as const
        },
        {
          id: 'academic-tone',
          name: 'Academic Tone',
          description: 'Convert to formal academic style',
          icon: 'BookOpen',
          category: 'enhancement',
          requiresSelection: true,
          mode: 'replace' as const
        },
        {
          id: 'expand-point',
          name: 'Expand Ideas',
          description: 'Develop and elaborate on concepts',
          icon: 'TrendingUp',
          category: 'generation',
          requiresSelection: true,
          mode: 'insert' as const
        }
      ]
    },
    {
      id: 'generation',
      name: 'Content Generation',
      tools: RESEARCH_TOOLS.filter(tool => tool.category === 'generation').slice(0, 6)
    },
    {
      id: 'enhancement',
      name: 'Text Enhancement', 
      tools: RESEARCH_TOOLS.filter(tool => tool.category === 'enhancement').slice(0, 6)
    },
    {
      id: 'analysis',
      name: 'Analysis & Review',
      tools: RESEARCH_TOOLS.filter(tool => tool.category === 'analysis' || tool.category === 'review').slice(0, 6)
    }
  ];

  // Icon mapping for tools
  const iconMap: Record<string, React.ReactNode> = {
    Type: <Type className="h-4 w-4" />,
    Target: <Target className="h-4 w-4" />,
    BookOpen: <BookOpen className="h-4 w-4" />,
    Sparkles: <Sparkles className="h-4 w-4" />,
    FlaskConical: <FlaskConical className="h-4 w-4" />,
    Lightbulb: <Lightbulb className="h-4 w-4" />,
    Search: <Search className="h-4 w-4" />,
    TrendingUp: <TrendingUp className="h-4 w-4" />,
    CheckCircle: <CheckCircle className="h-4 w-4" />,
    FileText: <FileText className="h-4 w-4" />,
    BarChart3: <BarChart3 className="h-4 w-4" />,
    FileCheck: <FileCheck className="h-4 w-4" />,
    Edit: <Edit className="h-4 w-4" />,
    PenTool: <PenTool className="h-4 w-4" />,
    AlignLeft: <AlignLeft className="h-4 w-4" />,
    List: <List className="h-4 w-4" />,
    Repeat: <Repeat className="h-4 w-4" />,
    Wand2: <Wand2 className="h-4 w-4" />
  };

  // Handle tool execution with enhanced prompts
  const handleToolExecution = async (tool: any) => {
    if (tool.requiresSelection && !selectedText.trim()) {
      toast.error(`Please select text to use "${tool.name}"`);
      return;
    }

    if (aiLoading || processingTool) return;

    setProcessingTool(tool.id);

    try {
      let result: string;

      // Handle special tools
      if (tool.id === 'humanize') {
        const prompt = humanizerService.generateHumanizationPrompt(selectedText, {
          academicLevel: 'graduate',
          maintainCitations: true
        });
        result = await enhancedAIService.generateText(prompt);
      } else if (tool.id === 'improve-clarity') {
        const prompt = `
Please improve the clarity, flow, and readability of the following academic text while maintaining its scholarly tone and content:

ORIGINAL TEXT:
"${selectedText}"

IMPROVEMENT REQUIREMENTS:
1. Enhance sentence flow and transitions
2. Improve word choice for clarity
3. Eliminate redundancy and wordiness
4. Maintain academic rigor and tone
5. Ensure logical progression of ideas
6. Preserve all citations and technical terms
7. Make the text more engaging while remaining scholarly

Please provide the improved version that is clearer and more readable while maintaining academic excellence.
`;
        result = await enhancedAIService.generateText(prompt);
      } else if (tool.id === 'academic-tone') {
        const prompt = `
Please convert the following text to a formal academic tone suitable for scholarly publication:

ORIGINAL TEXT:
"${selectedText}"

ACADEMIC TONE REQUIREMENTS:
1. Use formal, objective language
2. Employ appropriate academic vocabulary
3. Structure sentences for scholarly clarity
4. Remove casual or informal expressions
5. Add appropriate hedging language where needed
6. Ensure proper academic style and conventions
7. Maintain factual accuracy and meaning
8. Use third-person perspective where appropriate

Please provide the academically-toned version suitable for peer-reviewed publication.
`;
        result = await enhancedAIService.generateText(prompt);
      } else if (tool.id === 'expand-point') {
        const prompt = `
Please expand and elaborate on the following text with additional academic depth and supporting details:

ORIGINAL TEXT:
"${selectedText}"

EXPANSION REQUIREMENTS:
1. Add relevant supporting details and examples
2. Provide deeper analysis and explanation
3. Include potential implications or applications
4. Maintain academic rigor and scholarly tone
5. Ensure logical flow and coherence
6. Add appropriate academic transitions
7. Consider multiple perspectives where relevant
8. Preserve the original meaning while adding depth

Please provide an expanded version that adds substantial academic value and depth.
`;
        result = await enhancedAIService.generateText(prompt);
      } else {
        // Handle regular research tools
        const context = tool.requiresSelection ? selectedText : documentContent;
        result = await enhancedAIService.executeResearchTool(tool.id, context);
      }

      // Execute with the tool's preferred mode
      onToolExecute(tool.id, tool.mode || 'popup');
      
      toast.success(`${tool.name} completed successfully`);
      onClose(); // Close the panel after successful execution
    } catch (error: any) {
      console.error('Tool execution error:', error);
      toast.error(error.message || `Failed to execute ${tool.name}`);
    } finally {
      setProcessingTool(null);
    }
  };

  // Get color classes for tool categories
  const getCategoryColor = (category: string) => {
    const colors = {
      generation: 'bg-blue-50 hover:bg-blue-100 text-blue-700 border-blue-200',
      enhancement: 'bg-green-50 hover:bg-green-100 text-green-700 border-green-200',
      analysis: 'bg-purple-50 hover:bg-purple-100 text-purple-700 border-purple-200',
      review: 'bg-orange-50 hover:bg-orange-100 text-orange-700 border-orange-200',
      quick: 'bg-indigo-50 hover:bg-indigo-100 text-indigo-700 border-indigo-200'
    };
    return colors[category as keyof typeof colors] || 'bg-gray-50 hover:bg-gray-100 text-gray-700 border-gray-200';
  };

  const currentCategory = toolCategories.find(cat => cat.id === selectedCategory);
  const tools = currentCategory?.tools || [];

  return (
    <div 
      className="fixed z-50"
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
        transform: 'translate(-50%, -50%)'
      }}
    >
      <Card className="floating-tools-panel w-80 max-h-96 bg-white shadow-2xl border-gray-200">
        {/* Header */}
        <div className="popup-panel-header flex items-center justify-between p-4 border-b">
          <div className="flex items-center gap-2">
            <Edit className="h-5 w-5 text-blue-600" />
            <div>
              <h3 className="font-semibold text-gray-900 text-sm">Writing Tools</h3>
              <p className="text-xs text-gray-600">AI-powered writing assistance</p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Category Tabs */}
        <div className="flex border-b bg-gray-50">
          {toolCategories.map((category) => (
            <Button
              key={category.id}
              variant={selectedCategory === category.id ? "default" : "ghost"}
              size="sm"
              onClick={() => setSelectedCategory(category.id)}
              className="flex-1 rounded-none h-8 text-xs"
            >
              {category.name}
            </Button>
          ))}
        </div>

        {/* Tools Grid */}
        <ScrollArea className="max-h-64">
          <div className="p-3">
            {selectedText && (
              <div className="mb-3">
                <Badge variant="secondary" className="text-xs">
                  {selectedText.length} characters selected
                </Badge>
              </div>
            )}
            
            <div className="grid grid-cols-1 gap-2">
              {tools.map((tool) => (
                <Button
                  key={tool.id}
                  variant="outline"
                  className={`h-auto p-3 flex items-start gap-3 text-left transition-all duration-200 tool-button ${getCategoryColor(tool.category)}`}
                  onClick={() => handleToolExecution(tool)}
                  disabled={aiLoading || processingTool !== null || (tool.requiresSelection && !selectedText.trim())}
                  title={tool.description}
                >
                  <div className="flex-shrink-0 mt-0.5">
                    {processingTool === tool.id ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      iconMap[tool.icon] || <FileText className="h-4 w-4" />
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium text-sm">{tool.name}</span>
                      {tool.requiresSelection && (
                        <Badge variant="outline" className="text-xs">
                          Selection
                        </Badge>
                      )}
                    </div>
                    <p className="text-xs text-gray-600 leading-relaxed">
                      {tool.description}
                    </p>
                  </div>
                </Button>
              ))}
            </div>

            {tools.length === 0 && (
              <div className="text-center py-8">
                <FileText className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-600">No tools available in this category</p>
              </div>
            )}
          </div>
        </ScrollArea>
      </Card>
    </div>
  );
}
