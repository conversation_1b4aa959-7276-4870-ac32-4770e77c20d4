/**
 * Integrated Tutor Service
 * Combines Gemini AI, Tavily search, and RAG capabilities for comprehensive tutoring
 */

import { GeminiTutorService } from './gemini-tutor.service';
import { tavilySearchService } from './tavily-search.service';

interface TutorOptions {
  educationLevel: string;
  useCodeExecution?: boolean;
  useWebSearch?: boolean;
  useRAG?: boolean;
  subject?: string;
  context?: string;
}

interface TutorResponse {
  content: string;
  sources?: Array<{
    title: string;
    url: string;
    snippet: string;
  }>;
  codeOutput?: string;
  visualizations?: any[];
  searchResults?: any;
  confidence: number;
  model: string;
}

class IntegratedTutorService {
  private geminiService: GeminiTutorService;

  constructor() {
    this.geminiService = new GeminiTutorService();
  }

  /**
   * Generate a comprehensive tutoring response
   */
  async generateResponse(
    query: string,
    options: TutorOptions,
    conversationHistory: Array<{ role: string; content: string }> = [],
    onChunk?: (chunk: string) => void
  ): Promise<TutorResponse> {
    const {
      educationLevel,
      useCodeExecution = false,
      useWebSearch = true,
      useRAG = false,
      subject,
      context
    } = options;

    try {
      let searchResults = null;
      let enhancedContext = context || '';

      // Step 1: Perform web search if enabled
      if (useWebSearch && tavilySearchService.isAvailable()) {
        try {
          onChunk?.('🔍 Searching for relevant information...\n\n');
          
          searchResults = await tavilySearchService.searchEducational(
            query,
            educationLevel,
            { maxResults: 5 }
          );

          if (searchResults.results.length > 0) {
            enhancedContext += '\n\nRelevant information from web search:\n';
            searchResults.results.slice(0, 3).forEach((result, index) => {
              enhancedContext += `${index + 1}. ${result.title}: ${result.content.substring(0, 300)}...\n`;
            });
          }

          onChunk?.('✅ Search completed. Generating response...\n\n');
        } catch (searchError) {
          console.warn('Web search failed:', searchError);
          onChunk?.('⚠️ Web search unavailable, proceeding with AI knowledge...\n\n');
        }
      }

      // Step 2: Generate AI response
      const tutorOptions = {
        educationLevel: educationLevel as any,
        subject,
        context: enhancedContext,
        conversationHistory
      };

      let response;

      if (useCodeExecution && this.geminiService.isServiceConfigured()) {
        // Use code execution for STEM subjects or when explicitly requested
        response = await this.geminiService.generateWithCodeExecution(
          subject || 'general',
          query,
          tutorOptions,
          onChunk
        );
      } else {
        // Use regular streaming response
        response = await this.geminiService.generateTutoringResponseStream(
          subject || 'general',
          query,
          tutorOptions,
          onChunk
        );
      }

      // Step 3: Format and return comprehensive response
      return {
        content: response.content,
        sources: searchResults ? this.formatSearchSources(searchResults) : [],
        codeOutput: response.codeOutput,
        visualizations: response.visualizations || [],
        searchResults: searchResults,
        confidence: response.confidence,
        model: response.model
      };

    } catch (error) {
      console.error('Integrated tutor service error:', error);
      throw new Error(`Failed to generate tutoring response: ${error.message}`);
    }
  }

  /**
   * Generate a quick answer for simple questions
   */
  async getQuickAnswer(
    question: string,
    educationLevel: string = 'high-school'
  ): Promise<string> {
    try {
      // Try Tavily quick answer first
      if (tavilySearchService.isAvailable()) {
        const quickAnswer = await tavilySearchService.getQuickAnswer(question);
        if (quickAnswer) {
          return quickAnswer;
        }
      }

      // Fallback to Gemini
      if (this.geminiService.isServiceConfigured()) {
        const response = await this.geminiService.generateTutoringResponseStream(
          'general',
          question,
          {
            educationLevel: educationLevel as any,
            maxTokens: 500 // Keep it short for quick answers
          }
        );
        return response.content;
      }

      throw new Error('No AI services available');
    } catch (error) {
      console.error('Quick answer failed:', error);
      return 'I apologize, but I cannot provide an answer at the moment. Please try again.';
    }
  }

  /**
   * Explain a concept with visualizations
   */
  async explainWithVisualization(
    concept: string,
    educationLevel: string,
    subject?: string,
    onChunk?: (chunk: string) => void
  ): Promise<TutorResponse> {
    if (!this.geminiService.isServiceConfigured()) {
      throw new Error('Gemini service not available for visualizations');
    }

    try {
      onChunk?.('🎨 Creating visual explanation...\n\n');

      const response = await this.geminiService.generateWithCodeExecution(
        subject || 'general',
        `Explain the concept of "${concept}" with visual aids and code examples`,
        {
          educationLevel: educationLevel as any,
          subject,
          context: `Focus on creating clear visualizations and interactive examples to help understand ${concept}.`
        },
        onChunk
      );

      return {
        content: response.content,
        sources: [],
        codeOutput: response.codeOutput,
        visualizations: response.visualizations || [],
        confidence: response.confidence,
        model: response.model
      };
    } catch (error) {
      console.error('Visualization explanation failed:', error);
      throw error;
    }
  }

  /**
   * Generate practice questions for a topic
   */
  async generatePracticeQuestions(
    topic: string,
    educationLevel: string,
    questionCount: number = 5,
    difficulty: 'easy' | 'medium' | 'hard' = 'medium',
    source: 'ai' | 'search' | 'documents' = 'ai',
    uploadedDocuments: any[] = []
  ): Promise<Array<{
    id: string;
    question: string;
    type: 'multiple-choice' | 'true-false' | 'short-answer';
    options?: string[];
    correctAnswer: string;
    explanation: string;
    hint?: string;
  }>> {
    if (!this.geminiService.isServiceConfigured()) {
      const errorMessage = 'Gemini API key not configured. Please add VITE_GEMINI_API_KEY to your environment variables.';
      console.error(errorMessage);
      throw new Error(errorMessage);
    }

    try {
      const prompt = this.buildQuizPrompt(topic, educationLevel, questionCount, difficulty, source);

      // Add timeout and retry logic
      let response;
      let attempts = 0;
      const maxAttempts = 2;

      while (attempts < maxAttempts) {
        try {
          response = await Promise.race([
            this.geminiService.generateTutoringResponseStream(
              'assessment',
              prompt,
              {
                educationLevel: educationLevel as any,
                maxTokens: Math.min(4000, questionCount * 400) // Increase tokens based on question count
              }
            ),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Request timeout')), 30000) // 30 second timeout
            )
          ]);
          break; // Success, exit retry loop
        } catch (attemptError) {
          attempts++;
          console.warn(`Attempt ${attempts} failed:`, attemptError.message);

          if (attempts >= maxAttempts) {
            throw new Error(`Failed after ${maxAttempts} attempts: ${attemptError.message}`);
          }

          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      if (!response.content) {
        throw new Error('No content received from AI service');
      }

      console.log('Raw AI response:', response.content.substring(0, 500) + '...');

      // Try to parse JSON response
      try {
        const cleanedContent = this.cleanJsonResponse(response.content);
        console.log('Cleaned content:', cleanedContent.substring(0, 300) + '...');

        if (!cleanedContent || cleanedContent.trim().length === 0) {
          throw new Error('Empty response after cleaning');
        }

        const questions = JSON.parse(cleanedContent);

        if (!Array.isArray(questions)) {
          throw new Error('AI response is not a valid question array');
        }

        if (questions.length === 0) {
          throw new Error('No questions were generated by the AI');
        }

        const formattedQuestions = questions.map((q, index) => {
          if (!q.question) {
            throw new Error(`Question ${index + 1} is missing question text`);
          }

          return {
            id: `q_${Date.now()}_${index}`,
            question: q.question,
            type: q.type || 'multiple-choice',
            options: q.options || [],
            correctAnswer: q.correctAnswer || q.correct || '',
            explanation: q.explanation || 'No explanation provided',
            hint: q.hint
          };
        });

        console.log(`Successfully generated ${formattedQuestions.length} questions for topic "${topic}"`);
        return formattedQuestions;

      } catch (parseError) {
        console.error('Failed to parse questions JSON:', parseError);
        console.error('Raw response content:', response.content);

        // Try to extract partial JSON if possible
        const partialQuestions = this.extractPartialQuestions(response.content);
        if (partialQuestions.length > 0) {
          console.log(`Recovered ${partialQuestions.length} questions from partial response`);
          return partialQuestions;
        }

        // Last resort: try a simpler prompt
        console.log('Attempting simplified question generation...');
        try {
          const simpleQuestions = await this.generateSimpleQuestions(topic, questionCount);
          if (simpleQuestions.length > 0) {
            console.log(`Generated ${simpleQuestions.length} simple questions as fallback`);
            return simpleQuestions;
          }
        } catch (simpleError) {
          console.error('Simple question generation also failed:', simpleError);
        }

        throw new Error(`Failed to parse AI response: ${parseError.message}. Please try again with a simpler topic or fewer questions.`);
      }
    } catch (error) {
      console.error('Practice questions generation failed:', error);
      throw new Error(`Failed to generate practice questions for "${topic}": ${error.message}`);
    }
  }

  private buildQuizPrompt(topic: string, educationLevel: string, questionCount: number, difficulty: string, source: string): string {
    let contextInfo = '';

    if (source === 'search') {
      contextInfo = 'Use current knowledge and web-searchable information about ';
    } else if (source === 'documents') {
      contextInfo = 'Based on uploaded documents about ';
    } else {
      contextInfo = 'Using your knowledge about ';
    }

    return `${contextInfo}"${topic}" - Generate exactly ${questionCount} practice questions for ${educationLevel} level students with ${difficulty} difficulty.

CRITICAL: Return ONLY a valid JSON array. Do not include any text before or after the JSON. Do not use markdown code blocks.

Required JSON structure:
[
  {
    "question": "Clear question text",
    "type": "multiple-choice",
    "options": ["Option A", "Option B", "Option C", "Option D"],
    "correctAnswer": "Option A",
    "explanation": "Brief explanation why this is correct",
    "hint": "Optional hint for students"
  }
]

Requirements:
- Generate exactly ${questionCount} questions
- Each question must have all required fields
- Use proper JSON syntax with double quotes
- No trailing commas
- Make questions educational and appropriate for ${difficulty} difficulty level
- Start response with [ and end with ]`;
  }

  private cleanJsonResponse(content: string): string {
    // Remove markdown code blocks and extra text
    let cleaned = content.replace(/```json\s*/g, '').replace(/```\s*/g, '');
    cleaned = cleaned.replace(/```/g, '');

    // Remove any text before the JSON array
    const arrayStartIndex = cleaned.indexOf('[');
    if (arrayStartIndex !== -1) {
      cleaned = cleaned.substring(arrayStartIndex);
    }

    // Find the last complete closing bracket
    const lastBracketIndex = cleaned.lastIndexOf(']');
    if (lastBracketIndex !== -1) {
      cleaned = cleaned.substring(0, lastBracketIndex + 1);
    }

    // Try to fix common JSON issues
    cleaned = cleaned.replace(/,\s*]/g, ']'); // Remove trailing commas
    cleaned = cleaned.replace(/,\s*}/g, '}'); // Remove trailing commas in objects

    return cleaned.trim();
  }

  private extractPartialQuestions(content: string): any[] {
    try {
      // Try to find individual question objects even if the array is incomplete
      const questionMatches = content.match(/\{[^{}]*"question"[^{}]*\}/g);
      if (questionMatches && questionMatches.length > 0) {
        const questions = [];
        for (const match of questionMatches) {
          try {
            const question = JSON.parse(match);
            if (question.question) {
              questions.push({
                id: `q_${Date.now()}_${questions.length}`,
                question: question.question,
                type: question.type || 'multiple-choice',
                options: question.options || [],
                correctAnswer: question.correctAnswer || question.correct || '',
                explanation: question.explanation || 'No explanation provided',
                hint: question.hint
              });
            }
          } catch (e) {
            // Skip invalid question objects
            continue;
          }
        }
        return questions;
      }
    } catch (error) {
      console.error('Failed to extract partial questions:', error);
    }
    return [];
  }

  private async generateSimpleQuestions(topic: string, questionCount: number): Promise<any[]> {
    try {
      const simplePrompt = `Generate ${questionCount} simple questions about ${topic}. Return only JSON array:
[{"question": "What is ${topic}?", "type": "short-answer", "correctAnswer": "Brief answer", "explanation": "Simple explanation"}]`;

      const response = await this.geminiService.generateTutoringResponseStream(
        'assessment',
        simplePrompt,
        { maxTokens: 1000 }
      );

      if (response.content) {
        const cleaned = this.cleanJsonResponse(response.content);
        const questions = JSON.parse(cleaned);

        return questions.map((q: any, index: number) => ({
          id: `simple_q_${Date.now()}_${index}`,
          question: q.question || `Question about ${topic}`,
          type: q.type || 'short-answer',
          options: q.options || [],
          correctAnswer: q.correctAnswer || 'Answer not provided',
          explanation: q.explanation || 'Explanation not provided',
          hint: q.hint
        }));
      }
    } catch (error) {
      console.error('Simple question generation failed:', error);
    }
    return [];
  }



  /**
   * Check service availability
   */
  getServiceStatus(): {
    gemini: boolean;
    tavily: boolean;
    rag: boolean;
  } {
    return {
      gemini: this.geminiService.isServiceConfigured(),
      tavily: tavilySearchService.isAvailable(),
      rag: false // RAG service status would go here
    };
  }

  /**
   * Format search sources for display
   */
  private formatSearchSources(searchResults: any): Array<{
    title: string;
    url: string;
    snippet: string;
  }> {
    if (!searchResults || !searchResults.results) {
      return [];
    }

    return searchResults.results.slice(0, 5).map((result: any) => ({
      title: result.title || 'Untitled',
      url: result.url || '#',
      snippet: result.content ? result.content.substring(0, 200) + '...' : 'No description available'
    }));
  }

  /**
   * Suggest follow-up questions
   */
  async suggestFollowUpQuestions(
    topic: string,
    conversationHistory: Array<{ role: string; content: string }>,
    educationLevel: string
  ): Promise<string[]> {
    if (!this.geminiService.isServiceConfigured()) {
      return [];
    }

    try {
      const prompt = `Based on the conversation about "${topic}", suggest 3-5 follow-up questions that would help the student learn more. Make them appropriate for ${educationLevel} level.

Conversation context:
${conversationHistory.slice(-4).map(msg => `${msg.role}: ${msg.content}`).join('\n')}

Return only the questions, one per line.`;

      const response = await this.geminiService.generateTutoringResponseStream(
        'general',
        prompt,
        {
          educationLevel: educationLevel as any,
          maxTokens: 300
        }
      );

      return response.content
        .split('\n')
        .filter(line => line.trim().length > 0 && line.includes('?'))
        .slice(0, 5);
    } catch (error) {
      console.error('Follow-up questions generation failed:', error);
      return [];
    }
  }
}

export const integratedTutorService = new IntegratedTutorService();
