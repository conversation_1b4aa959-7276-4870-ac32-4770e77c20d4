import React, { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { 
  BookOpen, 
  ExternalLink, 
  Copy, 
  Check, 
  Plus, 
  Search, 
  Trash2, 
  Edit3,
  Download,
  Upload,
  Settings,
  X,
  Quote,
  Link,
  FileText,
  Globe,
  Calendar,
  User,
  Building
} from 'lucide-react';
import { toast } from 'sonner';
import { tavilySearchService } from './research-search/services/tavily-search.service';

interface Citation {
  id: string;
  type: 'journal' | 'book' | 'website' | 'conference' | 'thesis' | 'report';
  title: string;
  authors: string[];
  year: number;
  journal?: string;
  volume?: string;
  issue?: string;
  pages?: string;
  doi?: string;
  url?: string;
  publisher?: string;
  location?: string;
  accessDate?: string;
  abstract?: string;
  keywords?: string[];
  notes?: string;
  addedDate: Date;
  lastModified: Date;
}

interface AcademicCitationSidebarProps {
  isVisible: boolean;
  onToggleVisibility: () => void;
  onCitationInsert: (citation: string, format: string) => void;
  documentContent: string;
  className?: string;
}

type CitationFormat = 'apa' | 'mla' | 'chicago' | 'harvard' | 'ieee';

export function AcademicCitationSidebar({
  isVisible,
  onToggleVisibility,
  onCitationInsert,
  documentContent,
  className = ''
}: AcademicCitationSidebarProps) {
  const [citations, setCitations] = useState<Citation[]>([]);
  const [selectedFormat, setSelectedFormat] = useState<CitationFormat>('apa');
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredCitations, setFilteredCitations] = useState<Citation[]>([]);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingCitation, setEditingCitation] = useState<Citation | null>(null);
  const [copiedId, setCopiedId] = useState<string | null>(null);
  const [isSearching, setIsSearching] = useState(false);
  const [showSettings, setShowSettings] = useState(false);

  // New citation form state
  const [newCitation, setNewCitation] = useState<Partial<Citation>>({
    type: 'journal',
    title: '',
    authors: [''],
    year: new Date().getFullYear(),
    journal: '',
    volume: '',
    issue: '',
    pages: '',
    doi: '',
    url: '',
    publisher: '',
    location: '',
    abstract: '',
    keywords: [],
    notes: ''
  });

  // Filter citations based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredCitations(citations);
    } else {
      const filtered = citations.filter(citation =>
        citation.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        citation.authors.some(author => author.toLowerCase().includes(searchQuery.toLowerCase())) ||
        citation.journal?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        citation.keywords?.some(keyword => keyword.toLowerCase().includes(searchQuery.toLowerCase()))
      );
      setFilteredCitations(filtered);
    }
  }, [citations, searchQuery]);

  // Load citations from localStorage on mount
  useEffect(() => {
    const savedCitations = localStorage.getItem('academic-citations');
    if (savedCitations) {
      try {
        const parsed = JSON.parse(savedCitations);
        setCitations(parsed.map((c: any) => ({
          ...c,
          addedDate: new Date(c.addedDate),
          lastModified: new Date(c.lastModified)
        })));
      } catch (error) {
        console.error('Failed to load citations:', error);
      }
    }
  }, []);

  // Save citations to localStorage whenever citations change
  useEffect(() => {
    localStorage.setItem('academic-citations', JSON.stringify(citations));
  }, [citations]);

  // Format citation according to selected style
  const formatCitation = useCallback((citation: Citation, format: CitationFormat): string => {
    const authors = citation.authors.join(', ');
    const year = citation.year;
    const title = citation.title;

    switch (format) {
      case 'apa':
        return formatAPACitation(citation);
      case 'mla':
        return formatMLACitation(citation);
      case 'chicago':
        return formatChicagoCitation(citation);
      case 'harvard':
        return formatHarvardCitation(citation);
      case 'ieee':
        return formatIEEECitation(citation);
      default:
        return `${authors} (${year}). ${title}.`;
    }
  }, []);

  // APA formatting
  const formatAPACitation = (citation: Citation): string => {
    const authors = citation.authors.join(', ');
    let formatted = `${authors} (${citation.year}). ${citation.title}`;

    if (citation.type === 'journal' && citation.journal) {
      formatted += `. *${citation.journal}*`;
      if (citation.volume) formatted += `, ${citation.volume}`;
      if (citation.issue) formatted += `(${citation.issue})`;
      if (citation.pages) formatted += `, ${citation.pages}`;
    } else if (citation.type === 'book' && citation.publisher) {
      formatted += `. ${citation.publisher}`;
      if (citation.location) formatted += `: ${citation.location}`;
    }

    if (citation.doi) {
      formatted += `. https://doi.org/${citation.doi}`;
    } else if (citation.url) {
      formatted += `. ${citation.url}`;
    }

    return formatted + '.';
  };

  // MLA formatting
  const formatMLACitation = (citation: Citation): string => {
    const authors = citation.authors.join(', ');
    let formatted = `${authors}. "${citation.title}."`;

    if (citation.type === 'journal' && citation.journal) {
      formatted += ` *${citation.journal}*`;
      if (citation.volume) formatted += `, vol. ${citation.volume}`;
      if (citation.issue) formatted += `, no. ${citation.issue}`;
      formatted += `, ${citation.year}`;
      if (citation.pages) formatted += `, pp. ${citation.pages}`;
    }

    if (citation.doi) {
      formatted += `, doi:${citation.doi}`;
    } else if (citation.url) {
      formatted += `, ${citation.url}`;
    }

    return formatted + '.';
  };

  // Chicago formatting
  const formatChicagoCitation = (citation: Citation): string => {
    const authors = citation.authors.join(', ');
    let formatted = `${authors}. "${citation.title}."`;

    if (citation.type === 'journal' && citation.journal) {
      formatted += ` *${citation.journal}*`;
      if (citation.volume) formatted += ` ${citation.volume}`;
      if (citation.issue) formatted += `, no. ${citation.issue}`;
      formatted += ` (${citation.year})`;
      if (citation.pages) formatted += `: ${citation.pages}`;
    }

    if (citation.doi) {
      formatted += `. https://doi.org/${citation.doi}`;
    }

    return formatted + '.';
  };

  // Harvard formatting
  const formatHarvardCitation = (citation: Citation): string => {
    const authors = citation.authors.join(', ');
    let formatted = `${authors} ${citation.year}, '${citation.title}'`;

    if (citation.type === 'journal' && citation.journal) {
      formatted += `, *${citation.journal}*`;
      if (citation.volume) formatted += `, vol. ${citation.volume}`;
      if (citation.issue) formatted += `, no. ${citation.issue}`;
      if (citation.pages) formatted += `, pp. ${citation.pages}`;
    }

    if (citation.doi) {
      formatted += `, doi: ${citation.doi}`;
    }

    return formatted + '.';
  };

  // IEEE formatting
  const formatIEEECitation = (citation: Citation): string => {
    const authors = citation.authors.join(', ');
    let formatted = `${authors}, "${citation.title},"`;

    if (citation.type === 'journal' && citation.journal) {
      formatted += ` *${citation.journal}*`;
      if (citation.volume) formatted += `, vol. ${citation.volume}`;
      if (citation.issue) formatted += `, no. ${citation.issue}`;
      if (citation.pages) formatted += `, pp. ${citation.pages}`;
      formatted += `, ${citation.year}`;
    }

    if (citation.doi) {
      formatted += `, doi: ${citation.doi}`;
    }

    return formatted + '.';
  };

  // Add new citation
  const handleAddCitation = () => {
    if (!newCitation.title || !newCitation.authors?.[0]) {
      toast.error('Title and at least one author are required');
      return;
    }

    const citation: Citation = {
      id: Date.now().toString(),
      type: newCitation.type as Citation['type'],
      title: newCitation.title,
      authors: newCitation.authors?.filter(a => a.trim()) || [],
      year: newCitation.year || new Date().getFullYear(),
      journal: newCitation.journal,
      volume: newCitation.volume,
      issue: newCitation.issue,
      pages: newCitation.pages,
      doi: newCitation.doi,
      url: newCitation.url,
      publisher: newCitation.publisher,
      location: newCitation.location,
      abstract: newCitation.abstract,
      keywords: newCitation.keywords || [],
      notes: newCitation.notes,
      addedDate: new Date(),
      lastModified: new Date()
    };

    setCitations(prev => [citation, ...prev]);
    setNewCitation({
      type: 'journal',
      title: '',
      authors: [''],
      year: new Date().getFullYear(),
      journal: '',
      volume: '',
      issue: '',
      pages: '',
      doi: '',
      url: '',
      publisher: '',
      location: '',
      abstract: '',
      keywords: [],
      notes: ''
    });
    setShowAddForm(false);
    toast.success('Citation added successfully');
  };

  // Copy citation to clipboard
  const handleCopyCitation = async (citation: Citation) => {
    const formatted = formatCitation(citation, selectedFormat);
    try {
      await navigator.clipboard.writeText(formatted);
      setCopiedId(citation.id);
      setTimeout(() => setCopiedId(null), 2000);
      toast.success('Citation copied to clipboard');
    } catch (error) {
      toast.error('Failed to copy citation');
    }
  };

  // Insert citation into document
  const handleInsertCitation = (citation: Citation) => {
    const formatted = formatCitation(citation, selectedFormat);
    onCitationInsert(formatted, selectedFormat);
    toast.success('Citation inserted into document');
  };

  // Search for citations online
  const handleOnlineSearch = async () => {
    if (!searchQuery.trim()) {
      toast.error('Please enter a search query');
      return;
    }

    if (!tavilySearchService.isAvailable()) {
      toast.error('Online search not available - Tavily API key not configured');
      return;
    }

    setIsSearching(true);
    try {
      const results = await tavilySearchService.searchAcademic(searchQuery, {
        maxResults: 5,
        searchDepth: 'advanced',
        includeAnswer: false
      });

      // Convert search results to citations
      const newCitations: Citation[] = results.results.map((result, index) => ({
        id: `search-${Date.now()}-${index}`,
        type: 'website' as const,
        title: result.title,
        authors: ['Unknown Author'],
        year: new Date().getFullYear(),
        url: result.url,
        abstract: result.content.slice(0, 300),
        addedDate: new Date(),
        lastModified: new Date()
      }));

      setCitations(prev => [...newCitations, ...prev]);
      toast.success(`Found ${newCitations.length} citations`);
    } catch (error) {
      console.error('Search error:', error);
      toast.error('Failed to search for citations');
    } finally {
      setIsSearching(false);
    }
  };

  // Delete citation
  const handleDeleteCitation = (id: string) => {
    setCitations(prev => prev.filter(c => c.id !== id));
    toast.success('Citation deleted');
  };

  // Export citations
  const handleExportCitations = () => {
    const formatted = filteredCitations.map(c => formatCitation(c, selectedFormat)).join('\n\n');
    const blob = new Blob([formatted], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `citations-${selectedFormat}.txt`;
    a.click();
    URL.revokeObjectURL(url);
    toast.success('Citations exported');
  };

  if (!isVisible) return null;

  return (
    <div className={`fixed left-0 top-0 h-full w-80 bg-white shadow-xl border-r z-40 ${className}`}>
      <Card className="h-full rounded-none border-0">
        <CardHeader className="pb-3 border-b">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-semibold flex items-center gap-2">
              <Quote className="h-5 w-5 text-blue-600" />
              Citations
            </CardTitle>
            <div className="flex gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowSettings(!showSettings)}
                className="h-8 w-8 p-0"
              >
                <Settings className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={onToggleVisibility}
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
          
          {/* Citation Format Selector */}
          <div className="space-y-2">
            <label className="text-xs font-medium text-gray-700">Citation Format</label>
            <Select value={selectedFormat} onValueChange={(value: CitationFormat) => setSelectedFormat(value)}>
              <SelectTrigger className="h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="apa">APA Style</SelectItem>
                <SelectItem value="mla">MLA Style</SelectItem>
                <SelectItem value="chicago">Chicago Style</SelectItem>
                <SelectItem value="harvard">Harvard Style</SelectItem>
                <SelectItem value="ieee">IEEE Style</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>

        <CardContent className="p-0 flex flex-col h-full">
          {/* Search and Actions */}
          <div className="p-3 border-b space-y-2">
            <div className="flex gap-2">
              <Input
                placeholder="Search citations..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="flex-1 h-8"
              />
              <Button
                variant="outline"
                size="sm"
                onClick={handleOnlineSearch}
                disabled={isSearching}
                className="h-8 px-2"
                title="Search online for citations"
              >
                <Search className="h-3 w-3" />
              </Button>
            </div>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowAddForm(true)}
                className="flex-1 h-8"
              >
                <Plus className="h-3 w-3 mr-1" />
                Add
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleExportCitations}
                disabled={filteredCitations.length === 0}
                className="h-8 px-2"
                title="Export citations"
              >
                <Download className="h-3 w-3" />
              </Button>
            </div>
          </div>

          {/* Citations List */}
          <ScrollArea className="flex-1">
            <div className="p-3 space-y-3">
              {filteredCitations.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <BookOpen className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                  <div className="text-sm">No citations found</div>
                  <div className="text-xs text-gray-400 mt-1">
                    {searchQuery ? 'Try a different search term' : 'Add your first citation'}
                  </div>
                </div>
              ) : (
                filteredCitations.map((citation) => (
                  <Card key={citation.id} className="p-3 hover:bg-gray-50 transition-colors">
                    <div className="space-y-2">
                      {/* Citation Header */}
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h4 className="font-medium text-sm line-clamp-2 mb-1">
                            {citation.title}
                          </h4>
                          <div className="text-xs text-gray-600">
                            {citation.authors.join(', ')} ({citation.year})
                          </div>
                          {citation.journal && (
                            <div className="text-xs text-gray-500 italic">
                              {citation.journal}
                            </div>
                          )}
                        </div>
                        <Badge variant="outline" className="text-xs ml-2">
                          {citation.type}
                        </Badge>
                      </div>

                      {/* Citation Preview */}
                      <div className="text-xs text-gray-700 bg-gray-50 p-2 rounded border">
                        {formatCitation(citation, selectedFormat)}
                      </div>

                      {/* Citation Actions */}
                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleCopyCitation(citation)}
                          className="h-6 px-2 text-xs"
                        >
                          {copiedId === citation.id ? (
                            <Check className="h-3 w-3 text-green-600" />
                          ) : (
                            <Copy className="h-3 w-3" />
                          )}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleInsertCitation(citation)}
                          className="h-6 px-2 text-xs"
                        >
                          <Plus className="h-3 w-3" />
                        </Button>
                        {citation.url && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => window.open(citation.url, '_blank')}
                            className="h-6 px-2 text-xs"
                          >
                            <ExternalLink className="h-3 w-3" />
                          </Button>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteCitation(citation.id)}
                          className="h-6 px-2 text-xs text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>

                      {/* DOI and URL */}
                      {(citation.doi || citation.url) && (
                        <div className="flex items-center gap-2 text-xs">
                          {citation.doi && (
                            <div className="flex items-center gap-1 text-blue-600">
                              <Link className="h-3 w-3" />
                              <span>DOI: {citation.doi}</span>
                            </div>
                          )}
                          {citation.url && !citation.doi && (
                            <div className="flex items-center gap-1 text-blue-600">
                              <Globe className="h-3 w-3" />
                              <span className="truncate">URL</span>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </Card>
                ))
              )}
            </div>
          </ScrollArea>

          {/* Citation Count */}
          <div className="p-3 border-t bg-gray-50">
            <div className="text-xs text-gray-600 text-center">
              {filteredCitations.length} citation{filteredCitations.length !== 1 ? 's' : ''}
              {searchQuery && ` matching "${searchQuery}"`}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Add Citation Form Modal */}
      {showAddForm && (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
          <Card className="w-full max-w-md max-h-[90vh] overflow-hidden">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center justify-between">
                Add New Citation
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowAddForm(false)}
                  className="h-8 w-8 p-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 max-h-[70vh] overflow-y-auto">
              {/* Citation Type */}
              <div>
                <label className="text-sm font-medium">Type</label>
                <Select
                  value={newCitation.type}
                  onValueChange={(value) => setNewCitation(prev => ({ ...prev, type: value as Citation['type'] }))}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="journal">Journal Article</SelectItem>
                    <SelectItem value="book">Book</SelectItem>
                    <SelectItem value="website">Website</SelectItem>
                    <SelectItem value="conference">Conference Paper</SelectItem>
                    <SelectItem value="thesis">Thesis/Dissertation</SelectItem>
                    <SelectItem value="report">Report</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Title */}
              <div>
                <label className="text-sm font-medium">Title *</label>
                <Input
                  value={newCitation.title}
                  onChange={(e) => setNewCitation(prev => ({ ...prev, title: e.target.value }))}
                  placeholder="Enter title"
                  className="mt-1"
                />
              </div>

              {/* Authors */}
              <div>
                <label className="text-sm font-medium">Authors *</label>
                {newCitation.authors?.map((author, index) => (
                  <div key={index} className="flex gap-2 mt-1">
                    <Input
                      value={author}
                      onChange={(e) => {
                        const newAuthors = [...(newCitation.authors || [])];
                        newAuthors[index] = e.target.value;
                        setNewCitation(prev => ({ ...prev, authors: newAuthors }));
                      }}
                      placeholder="Author name"
                      className="flex-1"
                    />
                    {index > 0 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          const newAuthors = newCitation.authors?.filter((_, i) => i !== index);
                          setNewCitation(prev => ({ ...prev, authors: newAuthors }));
                        }}
                        className="h-10 w-10 p-0"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                ))}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setNewCitation(prev => ({
                    ...prev,
                    authors: [...(prev.authors || []), '']
                  }))}
                  className="mt-2 h-8"
                >
                  <Plus className="h-3 w-3 mr-1" />
                  Add Author
                </Button>
              </div>

              {/* Year */}
              <div>
                <label className="text-sm font-medium">Year</label>
                <Input
                  type="number"
                  value={newCitation.year}
                  onChange={(e) => setNewCitation(prev => ({ ...prev, year: parseInt(e.target.value) || new Date().getFullYear() }))}
                  placeholder="Publication year"
                  className="mt-1"
                />
              </div>

              {/* Journal/Publisher specific fields */}
              {(newCitation.type === 'journal' || newCitation.type === 'conference') && (
                <>
                  <div>
                    <label className="text-sm font-medium">
                      {newCitation.type === 'journal' ? 'Journal' : 'Conference'}
                    </label>
                    <Input
                      value={newCitation.journal}
                      onChange={(e) => setNewCitation(prev => ({ ...prev, journal: e.target.value }))}
                      placeholder={`Enter ${newCitation.type === 'journal' ? 'journal' : 'conference'} name`}
                      className="mt-1"
                    />
                  </div>
                  <div className="grid grid-cols-3 gap-2">
                    <div>
                      <label className="text-sm font-medium">Volume</label>
                      <Input
                        value={newCitation.volume}
                        onChange={(e) => setNewCitation(prev => ({ ...prev, volume: e.target.value }))}
                        placeholder="Vol."
                        className="mt-1"
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium">Issue</label>
                      <Input
                        value={newCitation.issue}
                        onChange={(e) => setNewCitation(prev => ({ ...prev, issue: e.target.value }))}
                        placeholder="No."
                        className="mt-1"
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium">Pages</label>
                      <Input
                        value={newCitation.pages}
                        onChange={(e) => setNewCitation(prev => ({ ...prev, pages: e.target.value }))}
                        placeholder="pp."
                        className="mt-1"
                      />
                    </div>
                  </div>
                </>
              )}

              {/* Book specific fields */}
              {newCitation.type === 'book' && (
                <div className="grid grid-cols-2 gap-2">
                  <div>
                    <label className="text-sm font-medium">Publisher</label>
                    <Input
                      value={newCitation.publisher}
                      onChange={(e) => setNewCitation(prev => ({ ...prev, publisher: e.target.value }))}
                      placeholder="Publisher name"
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Location</label>
                    <Input
                      value={newCitation.location}
                      onChange={(e) => setNewCitation(prev => ({ ...prev, location: e.target.value }))}
                      placeholder="City, Country"
                      className="mt-1"
                    />
                  </div>
                </div>
              )}

              {/* DOI and URL */}
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="text-sm font-medium">DOI</label>
                  <Input
                    value={newCitation.doi}
                    onChange={(e) => setNewCitation(prev => ({ ...prev, doi: e.target.value }))}
                    placeholder="10.1000/xyz123"
                    className="mt-1"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">URL</label>
                  <Input
                    value={newCitation.url}
                    onChange={(e) => setNewCitation(prev => ({ ...prev, url: e.target.value }))}
                    placeholder="https://..."
                    className="mt-1"
                  />
                </div>
              </div>

              {/* Abstract */}
              <div>
                <label className="text-sm font-medium">Abstract</label>
                <Textarea
                  value={newCitation.abstract}
                  onChange={(e) => setNewCitation(prev => ({ ...prev, abstract: e.target.value }))}
                  placeholder="Brief abstract or summary"
                  className="mt-1 min-h-[60px]"
                />
              </div>

              {/* Notes */}
              <div>
                <label className="text-sm font-medium">Notes</label>
                <Textarea
                  value={newCitation.notes}
                  onChange={(e) => setNewCitation(prev => ({ ...prev, notes: e.target.value }))}
                  placeholder="Personal notes about this citation"
                  className="mt-1 min-h-[60px]"
                />
              </div>

              {/* Form Actions */}
              <div className="flex gap-2 pt-4">
                <Button
                  variant="outline"
                  onClick={() => setShowAddForm(false)}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleAddCitation}
                  className="flex-1"
                >
                  Add Citation
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
