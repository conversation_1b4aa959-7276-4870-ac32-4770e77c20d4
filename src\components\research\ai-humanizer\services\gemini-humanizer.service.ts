/**
 * Academic Writing Humanizer Service
 * Handles AI humanization using OpenRouter and Google APIs for academic writing
 */

import {
  HumanizerResponse,
  StreamingHumanizerResponse,
  StyleAnalysis,
  StyleCharacteristics,
  SampleText,
  ProcessingStatus
} from '../types';
import { STYLE_ANALYSIS_PROMPT, HUMANIZATION_PROMPT } from '../constants';

interface HumanizerOptions {
  model?: string;
  temperature?: number;
  maxTokens?: number;
  preserveFormatting?: boolean;
  maintainTechnicalTerms?: boolean;
  creativityLevel?: 'conservative' | 'moderate' | 'creative';
}

export class GeminiHumanizerService {
  private openRouterApiKey: string;
  private geminiApiKey: string;
  private isConfigured: boolean = false;

  constructor() {
    // Get API keys from environment
    this.openRouterApiKey = import.meta.env.VITE_OPENROUTER_API_KEY ||
                           process.env.OPENROUTER_API_KEY || '';
    this.geminiApiKey = import.meta.env.VITE_GEMINI_API_KEY ||
                       process.env.NEXT_PUBLIC_GEMINI_API_KEY ||
                       process.env.GEMINI_API_KEY || '';

    // Check if at least one API key is available
    const hasOpenRouter = this.openRouterApiKey && this.openRouterApiKey.length > 20;
    const hasGemini = this.geminiApiKey && this.geminiApiKey.length > 20;

    this.isConfigured = hasOpenRouter || hasGemini;

    if (this.isConfigured) {
      console.log('Academic Writing Humanizer Service initialized successfully', {
        hasOpenRouter,
        hasGemini
      });
    } else {
      console.warn('No valid API keys found. Please configure OpenRouter or Gemini API key.');
    }
  }

  /**
   * Check if the service is properly configured
   */
  public isReady(): boolean {
    return this.isConfigured;
  }

  /**
   * Determine which API to use based on model
   */
  private shouldUseOpenRouter(model: string): boolean {
    return model.includes('/') && !model.startsWith('google/');
  }

  /**
   * Get the appropriate API key for the model
   */
  private getApiKey(model: string): string {
    if (this.shouldUseOpenRouter(model)) {
      return this.openRouterApiKey;
    }
    return this.geminiApiKey;
  }

  /**
   * Call OpenRouter API for AI generation
   */
  private async callOpenRouterAPI(
    prompt: string,
    model: string,
    options: { temperature: number; maxTokens: number }
  ): Promise<string> {
    if (!this.openRouterApiKey) {
      throw new Error('OpenRouter API key not configured');
    }

    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.openRouterApiKey}`,
        'HTTP-Referer': window.location.origin,
        'X-Title': 'Academic Writing Humanizer'
      },
      body: JSON.stringify({
        model,
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: options.maxTokens,
        temperature: options.temperature,
        stream: false
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`OpenRouter API error: ${response.status} - ${errorText}`);
    }

    const data = await response.json();

    if (!data.choices || !data.choices[0] || !data.choices[0].message) {
      throw new Error('Invalid response format from OpenRouter API');
    }

    return data.choices[0].message.content;
  }

  /**
   * Analyze sample texts to extract writing style characteristics
   */
  public async analyzeWritingStyle(
    sampleTexts: SampleText[],
    options: HumanizerOptions = {}
  ): Promise<HumanizerResponse> {
    if (!this.isReady()) {
      return {
        success: false,
        error: {
          code: 'SERVICE_NOT_CONFIGURED',
          message: 'Gemini service is not properly configured',
          timestamp: new Date()
        }
      };
    }

    if (!sampleTexts || sampleTexts.length === 0) {
      return {
        success: false,
        error: {
          code: 'NO_SAMPLES',
          message: 'No sample texts provided for analysis',
          timestamp: new Date()
        }
      };
    }

    try {
      const startTime = Date.now();
      const model = options.model || 'google/gemini-2.5-flash';

      // Prepare sample texts for analysis
      const sampleTextsFormatted = sampleTexts
        .map((sample, index) => `Sample ${index + 1} (${sample.label}):\n${sample.content}`)
        .join('\n\n---\n\n');

      const prompt = STYLE_ANALYSIS_PROMPT.replace('{SAMPLE_TEXTS}', sampleTextsFormatted);

      let text = '';

      if (this.shouldUseOpenRouter(model)) {
        text = await this.callOpenRouterAPI(prompt, model, {
          temperature: options.temperature || 0.3,
          maxTokens: options.maxTokens || 4096
        });
      } else {
        // For Google models, use OpenRouter as well to avoid CORS issues
        text = await this.callOpenRouterAPI(prompt, model, {
          temperature: options.temperature || 0.3,
          maxTokens: options.maxTokens || 4096
        });
      }

      // Parse the JSON response
      let styleCharacteristics: StyleCharacteristics;
      let confidence = 0.8;
      let insights: string[] = [];

      try {
        const parsed = JSON.parse(text);
        styleCharacteristics = {
          tone: parsed.tone || [],
          vocabulary: parsed.vocabulary || {
            complexity: 'moderate',
            commonWords: [],
            preferredPhrases: []
          },
          sentenceStructure: parsed.sentenceStructure || {
            averageLength: 15,
            complexity: 'varied',
            patterns: []
          },
          voice: parsed.voice || {
            perspective: 'mixed',
            formality: 'semi-formal',
            personality: []
          },
          writingPatterns: parsed.writingPatterns || {
            paragraphLength: 'medium',
            transitionStyle: [],
            emphasisMethods: []
          }
        };
        confidence = parsed.confidence || 0.8;
        insights = parsed.insights || [];
      } catch (parseError) {
        console.warn('Failed to parse style analysis JSON, using fallback');
        styleCharacteristics = this.createFallbackStyleCharacteristics();
      }

      const analysis: StyleAnalysis = {
        id: `analysis_${Date.now()}`,
        sampleTextIds: sampleTexts.map(s => s.id),
        characteristics: styleCharacteristics,
        confidence,
        analysisDate: new Date(),
        modelUsed: options.model || 'google/gemini-2.5-flash',
        insights,
        recommendations: this.generateRecommendations(styleCharacteristics)
      };

      const processingTime = Date.now() - startTime;

      return {
        success: true,
        data: analysis,
        metadata: {
          processingTime,
          tokensUsed: this.estimateTokens(text),
          modelUsed: options.model || 'google/gemini-2.5-flash'
        }
      };

    } catch (error) {
      console.error('Error analyzing writing style:', error);
      return {
        success: false,
        error: {
          code: 'ANALYSIS_FAILED',
          message: error instanceof Error ? error.message : 'Failed to analyze writing style',
          details: error,
          timestamp: new Date()
        }
      };
    }
  }

  /**
   * Humanize content using the analyzed writing style
   */
  public async humanizeContent(
    content: string,
    sampleTexts: SampleText[],
    options: HumanizerOptions = {}
  ): Promise<HumanizerResponse> {
    if (!this.isReady()) {
      return {
        success: false,
        error: {
          code: 'SERVICE_NOT_CONFIGURED',
          message: 'Gemini service is not properly configured',
          timestamp: new Date()
        }
      };
    }

    if (!content || content.trim().length === 0) {
      return {
        success: false,
        error: {
          code: 'NO_CONTENT',
          message: 'No content provided for humanization',
          timestamp: new Date()
        }
      };
    }

    if (!sampleTexts || sampleTexts.length === 0) {
      return {
        success: false,
        error: {
          code: 'NO_SAMPLES',
          message: 'No sample texts provided for style reference',
          timestamp: new Date()
        }
      };
    }

    try {
      const startTime = Date.now();
      const model = options.model || 'google/gemini-2.5-flash';

      // Prepare sample texts for reference
      const sampleTextsFormatted = sampleTexts
        .map((sample, index) => `Sample ${index + 1} (${sample.label}):\n${sample.content}`)
        .join('\n\n---\n\n');

      const prompt = HUMANIZATION_PROMPT
        .replace('{SAMPLE_TEXTS}', sampleTextsFormatted)
        .replace('{USER_TEXT_TO_REWRITE}', content);

      let humanizedContent = '';

      if (this.shouldUseOpenRouter(model)) {
        humanizedContent = await this.callOpenRouterAPI(prompt, model, {
          temperature: this.getTemperatureForCreativity(options.creativityLevel),
          maxTokens: options.maxTokens || 4096
        });
      } else {
        // For Google models, use OpenRouter as well to avoid CORS issues
        humanizedContent = await this.callOpenRouterAPI(prompt, model, {
          temperature: this.getTemperatureForCreativity(options.creativityLevel),
          maxTokens: options.maxTokens || 4096
        });
      }

      const processingTime = Date.now() - startTime;

      return {
        success: true,
        data: {
          originalContent: content,
          humanizedContent: humanizedContent.trim(),
          wordCount: this.countWords(humanizedContent),
          processingTime
        },
        metadata: {
          processingTime,
          tokensUsed: this.estimateTokens(humanizedContent),
          modelUsed: options.model || 'google/gemini-2.5-flash'
        }
      };

    } catch (error) {
      console.error('Error humanizing content:', error);
      return {
        success: false,
        error: {
          code: 'HUMANIZATION_FAILED',
          message: error instanceof Error ? error.message : 'Failed to humanize content',
          details: error,
          timestamp: new Date()
        }
      };
    }
  }

  /**
   * Create fallback style characteristics when parsing fails
   */
  private createFallbackStyleCharacteristics(): StyleCharacteristics {
    return {
      tone: ['professional', 'clear'],
      vocabulary: {
        complexity: 'moderate',
        commonWords: [],
        preferredPhrases: []
      },
      sentenceStructure: {
        averageLength: 15,
        complexity: 'varied',
        patterns: []
      },
      voice: {
        perspective: 'mixed',
        formality: 'semi-formal',
        personality: ['clear', 'direct']
      },
      writingPatterns: {
        paragraphLength: 'medium',
        transitionStyle: [],
        emphasisMethods: []
      }
    };
  }

  /**
   * Generate recommendations based on style characteristics
   */
  private generateRecommendations(characteristics: StyleCharacteristics): string[] {
    const recommendations: string[] = [];
    
    if (characteristics.tone.includes('professional')) {
      recommendations.push('Maintain professional tone throughout the content');
    }
    
    if (characteristics.vocabulary.complexity === 'academic') {
      recommendations.push('Use sophisticated vocabulary and technical terms appropriately');
    }
    
    if (characteristics.voice.formality === 'casual') {
      recommendations.push('Keep the writing conversational and approachable');
    }

    return recommendations;
  }

  /**
   * Get temperature setting based on creativity level
   */
  private getTemperatureForCreativity(level?: string): number {
    switch (level) {
      case 'conservative': return 0.2;
      case 'creative': return 0.8;
      case 'moderate':
      default: return 0.5;
    }
  }

  /**
   * Estimate token count (rough approximation)
   */
  private estimateTokens(text: string): number {
    return Math.ceil(text.length / 4);
  }

  /**
   * Count words in text
   */
  private countWords(text: string): number {
    return text.trim().split(/\s+/).length;
  }
}
