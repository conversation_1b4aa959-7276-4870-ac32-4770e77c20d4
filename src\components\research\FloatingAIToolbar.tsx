import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import {
  <PERSON>rkles,
  CheckCircle,
  AlignLeft,
  Edit,
  X,
  Loader2,
  Send,
  ChevronDown,
  ChevronUp,
  Wand2
} from 'lucide-react';
import { enhancedAIService } from './paper-generator/enhanced-ai.service';
import { tavilySearchService } from './research-search/services/tavily-search.service';
import './academic-interface.css';
import { toast } from 'sonner';

interface FloatingAIToolbarProps {
  selectedText: string;
  position: { x: number; y: number };
  onApplyResult: (result: string, mode: 'replace' | 'insert') => void;
  onClose: () => void;
  isLoading?: boolean;
  documentContext?: string; // Additional context from the document
  onInteractionStart?: () => void;
  onInteractionEnd?: () => void;
  onSendToAssistant?: (result: string, toolName: string) => void; // New prop for routing to main assistant
}

export function FloatingAIToolbar({
  selectedText,
  position,
  onApplyResult,
  onClose,
  isLoading = false,
  documentContext = '',
  onInteractionStart,
  onInteractionEnd,
  onSendToAssistant
}: FloatingAIToolbarProps) {
  const [processingTool, setProcessingTool] = useState<string | null>(null);
  const [showCustomPrompt, setShowCustomPrompt] = useState(false);
  const [customPrompt, setCustomPrompt] = useState('');
  const [isInteracting, setIsInteracting] = useState(false);
  const [aiOutput, setAiOutput] = useState<string>('');
  const [showOutput, setShowOutput] = useState(false);
  const [outputMode, setOutputMode] = useState<'replace' | 'insert' | 'keep'>('replace');

  // Essential quick actions only
  const essentialTools = [
    {
      id: 'humanize',
      name: 'Humanize',
      icon: <Wand2 className="h-3 w-3" />,
      color: 'blue',
      description: 'Make text sound more natural'
    },
    {
      id: 'concise',
      name: 'Concise',
      icon: <Sparkles className="h-3 w-3" />,
      color: 'green',
      description: 'Make text more concise'
    },
    {
      id: 'expand',
      name: 'Expand',
      icon: <AlignLeft className="h-3 w-3" />,
      color: 'purple',
      description: 'Add more detail'
    },
    {
      id: 'grammar',
      name: 'Fix Grammar',
      icon: <CheckCircle className="h-3 w-3" />,
      color: 'orange',
      description: 'Fix grammar and style'
    }
  ];

  const handleQuickAction = async (toolId: string) => {
    if (!selectedText.trim() || isLoading) return;

    console.log(`FloatingAIToolbar: Starting ${toolId} action`);
    setProcessingTool(toolId);

    try {
      let result: string;

      // Handle humanize action
      if (toolId === 'humanize') {
        const humanizePrompt = `
Please humanize the following text while maintaining its academic quality and accuracy. Make it sound more natural and engaging while preserving all key information:

"${selectedText}"

Guidelines:
- Keep all factual information intact
- Maintain appropriate tone but make it more conversational
- Improve flow and readability
- Remove overly robotic phrasing
- Ensure the text sounds naturally written

Please provide the humanized version:
`;
        result = await enhancedAIService.generateText(humanizePrompt);
      } else if (toolId === 'concise') {
        const concisePrompt = `Make this text more concise while preserving all key information and meaning:

"${selectedText}"

Please provide a shorter, more concise version:`;
        result = await enhancedAIService.generateText(concisePrompt);
      } else if (['clarity', 'grammar', 'expand'].includes(toolId)) {
        // Handle other quick enhancement tools
        result = await enhancedAIService.quickEnhance(
          selectedText,
          toolId as 'clarity' | 'grammar' | 'expand'
        );
      } else {
        // Fallback for other tools
        result = await enhancedAIService.generateText(`Please ${toolId} the following text: "${selectedText}"`);
      }

      // Show result in output box instead of immediately applying
      console.log('Setting AI output:', result.slice(0, 100) + '...');
      setAiOutput(result);
      setShowOutput(true);
      toast.success(`${toolId} completed - check output below`);

    } catch (error: any) {
      console.error('Quick action failed:', error);
      toast.error(`Failed to ${toolId} text: ${error.message}`);
      // Show error in output box
      setAiOutput(`Error: ${error.message}`);
      setShowOutput(true);
    } finally {
      setProcessingTool(null);
    }
  };

  const handleAdvancedAction = async (toolId: string): Promise<string> => {
    const context = documentContext ? `Document context: ${documentContext.slice(0, 500)}...\n\n` : '';

    switch (toolId) {
      case 'cite':
        return await enhancedAIService.generateText(
          `${context}Add relevant academic citations to this text. Provide proper in-text citations and suggest credible sources:\n\n"${selectedText}"`
        );
      case 'research':
        return await enhancedAIService.generateText(
          `${context}Find and suggest relevant research points to support this text. Include key findings and studies:\n\n"${selectedText}"`
        );
      case 'search-enhance':
        return await handleSearchEnhance();
      case 'summarize':
        return await enhancedAIService.generateText(
          `${context}Create a concise, academic summary of this text while preserving key points:\n\n"${selectedText}"`
        );
      case 'suggest':
        return await enhancedAIService.generateText(
          `${context}Provide specific suggestions to improve this text for academic writing:\n\n"${selectedText}"`
        );
      default:
        throw new Error(`Unknown tool: ${toolId}`);
    }
  };

  const handleSearchEnhance = async (): Promise<string> => {
    try {
      // Perform web search using Tavily
      const searchQuery = `${selectedText} academic research enhancement`;
      const searchResults = await tavilySearchService.searchAcademic(searchQuery, {
        maxResults: 5,
        searchDepth: 'advanced',
        includeAnswer: true
      });

      // Format search results
      const formattedResults = searchResults.results
        .map(result => `Title: ${result.title}\nContent: ${result.content}\nURL: ${result.url}`)
        .join('\n\n');

      // Generate enhanced content using AI with search results
      const context = documentContext ? `Document context: ${documentContext.slice(0, 500)}...\n\n` : '';

      return await enhancedAIService.generateText(
        `${context}Enhance the following text using current research findings:

Original text: "${selectedText}"

Recent research findings:
${formattedResults}

Please:
1. Enhance the text with current research insights
2. Add relevant supporting information
3. Suggest appropriate citations
4. Improve academic tone and clarity
5. Maintain the original meaning while strengthening with evidence

Provide an enhanced version that incorporates the research findings.`
      );
    } catch (error: any) {
      console.error('Search enhance failed:', error);
      // Fallback to regular enhancement if search fails
      return await enhancedAIService.generateText(
        `Enhance this text for academic writing:\n\n"${selectedText}"`
      );
    }
  };

  const handleCustomPrompt = async () => {
    if (!customPrompt.trim() || !selectedText.trim() || isLoading) return;

    setProcessingTool('custom');

    try {
      const context = documentContext ? `Document context: ${documentContext.slice(0, 500)}...\n\n` : '';
      const result = await enhancedAIService.generateText(
        `${context}Selected text: "${selectedText}"\n\nUser request: ${customPrompt}`
      );

      // Show result in output box
      setAiOutput(result);
      setShowOutput(true);
      setCustomPrompt('');
      setShowCustomPrompt(false);

    } catch (error: any) {
      console.error('Custom prompt failed:', error);
      toast.error(`Failed to process custom prompt: ${error.message}`);
    } finally {
      setProcessingTool(null);
    }
  };

  const getColorClasses = (color: string) => {
    const classes = {
      blue: 'bg-blue-100 hover:bg-blue-200 text-blue-700 border-blue-200',
      green: 'bg-green-100 hover:bg-green-200 text-green-700 border-green-200',
      purple: 'bg-purple-100 hover:bg-purple-200 text-purple-700 border-purple-200',
      indigo: 'bg-indigo-100 hover:bg-indigo-200 text-indigo-700 border-indigo-200',
      emerald: 'bg-emerald-100 hover:bg-emerald-200 text-emerald-700 border-emerald-200',
      orange: 'bg-orange-100 hover:bg-orange-200 text-orange-700 border-orange-200',
      cyan: 'bg-cyan-100 hover:bg-cyan-200 text-cyan-700 border-cyan-200',
      yellow: 'bg-yellow-100 hover:bg-yellow-200 text-yellow-700 border-yellow-200'
    };
    return classes[color as keyof typeof classes] || classes.blue;
  };

  return (
    <Card
      className="floating-ai-toolbar fixed z-[60] p-3 shadow-xl border bg-white backdrop-blur-sm"
      style={{
        left: `${Math.max(10, position.x)}px`,
        top: `${Math.max(10, position.y)}px`,
        transform: 'translateY(-100%)',
        maxWidth: showOutput ? '400px' : '320px',
        minWidth: '300px'
      }}
      onMouseEnter={() => {
        setIsInteracting(true);
        onInteractionStart?.();
      }}
      onMouseLeave={(e) => {
        // Don't close if we have output showing or processing
        if (showOutput || processingTool) return;

        // Only close if mouse leaves AND no active interaction
        const relatedTarget = e.relatedTarget as Element;
        if (!relatedTarget || !e.currentTarget.contains(relatedTarget)) {
          setIsInteracting(false);
          onInteractionEnd?.();
        }
      }}
      onClick={(e) => {
        e.stopPropagation();
        setIsInteracting(true);
        // Prevent auto-closing when clicked directly
      }}
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <Edit className="h-4 w-4 text-gray-500" />
          <span className="text-sm font-medium text-gray-700">AI Assistant</span>
          <Badge variant="secondary" className="text-xs">
            {selectedText.length} chars
          </Badge>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="h-6 w-6 p-0 hover:bg-gray-100"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Essential Quick Actions */}
      <div className="mb-3">
        <div className="text-xs font-medium text-gray-600 mb-2">Quick Actions</div>
        <div className="grid grid-cols-2 gap-1.5">
          {essentialTools.map((tool) => (
            <Button
              key={tool.id}
              variant="outline"
              size="sm"
              className={`h-auto p-2 flex flex-col items-center gap-1 text-xs transition-all duration-200 ${getColorClasses(tool.color)}`}
              onClick={() => handleQuickAction(tool.id)}
              disabled={isLoading || processingTool !== null}
              title={tool.description}
            >
              {processingTool === tool.id ? (
                <Loader2 className="h-3 w-3 animate-spin" />
              ) : (
                tool.icon
              )}
              <span className="font-medium">{tool.name}</span>
            </Button>
          ))}
        </div>
      </div>

      <Separator className="my-2" />

      {/* Custom Prompt */}
      <div>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setShowCustomPrompt(!showCustomPrompt)}
          className="w-full justify-between text-xs font-medium text-gray-600 h-6 p-1"
        >
          Custom AI Prompt
          {showCustomPrompt ? <ChevronUp className="h-3 w-3" /> : <ChevronDown className="h-3 w-3" />}
        </Button>

        {showCustomPrompt && (
          <div className="mt-2 space-y-2">
            <Textarea
              value={customPrompt}
              onChange={(e) => setCustomPrompt(e.target.value)}
              placeholder="Ask AI to do something specific with this text..."
              className="w-full text-xs min-h-[60px] resize-none"
              disabled={isLoading || processingTool !== null}
            />
            <Button
              size="sm"
              onClick={handleCustomPrompt}
              disabled={!customPrompt.trim() || isLoading || processingTool !== null}
              className="w-full h-7 text-xs"
            >
              {processingTool === 'custom' ? (
                <Loader2 className="h-3 w-3 animate-spin mr-1" />
              ) : (
                <Send className="h-3 w-3 mr-1" />
              )}
              Apply Custom Prompt
            </Button>
          </div>
        )}
      </div>

      {/* AI Output Box */}
      {showOutput && aiOutput && (
        <>
          <Separator className="my-3" />
          <div className="space-y-3 ai-output-result p-3 rounded-lg border border-blue-200">
            <div className="flex items-center justify-between">
              <div className="text-sm font-medium text-blue-800">✨ AI Result</div>
              <Badge variant="secondary" className="text-xs">
                {aiOutput.length} chars
              </Badge>
            </div>
            <div className="p-3 bg-white rounded-lg text-sm text-gray-800 max-h-40 overflow-y-auto border border-gray-200 shadow-sm">
              {aiOutput}
            </div>

            {/* Action Buttons */}
            <div className="space-y-2">
              <div className="flex gap-1">
                <Button
                  size="sm"
                  variant="default"
                  onClick={() => {
                    onApplyResult(aiOutput, 'replace');
                    toast.success('Text replaced successfully');
                    setTimeout(() => {
                      setShowOutput(false);
                      setAiOutput('');
                      onClose();
                    }, 500);
                  }}
                  className="flex-1 h-7 text-xs bg-blue-600 hover:bg-blue-700 modern-button"
                >
                  Replace
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => {
                    onApplyResult(aiOutput, 'insert');
                    toast.success('Text inserted successfully');
                    setTimeout(() => {
                      setShowOutput(false);
                      setAiOutput('');
                      onClose();
                    }, 500);
                  }}
                  className="flex-1 h-7 text-xs modern-button"
                >
                  Insert
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => {
                    setShowOutput(false);
                    setAiOutput('');
                  }}
                  className="h-7 text-xs px-2"
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>

              {/* Send to Main Assistant Button */}
              {onSendToAssistant && (
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={() => {
                    onSendToAssistant(aiOutput, processingTool || 'Quick Fix');
                    toast.success('Sent to AI Assistant');
                    setTimeout(() => {
                      setShowOutput(false);
                      setAiOutput('');
                      onClose();
                    }, 500);
                  }}
                  className="w-full h-7 text-xs bg-green-100 hover:bg-green-200 text-green-800 border-green-300"
                >
                  📤 Send to AI Assistant
                </Button>
              )}
            </div>
          </div>
        </>
      )}

      {!showOutput && (
        <div className="mt-3 text-xs text-gray-500 text-center">
          Select text and choose an AI action
        </div>
      )}
    </Card>
  );
}
