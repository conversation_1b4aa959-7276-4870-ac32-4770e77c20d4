import React, { useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import {
  Send,
  MessageSquare,
  Bot,
  User,
  Code,
  BarChart3,
  Loader2,
  Copy,
  CheckCircle,
  AlertTriangle,
  Clock,
  Sparkles,
  Brain,
  Lightbulb,
  TrendingUp,
  Eye,
  Settings,
  Zap,
  Activity,
  PieChart,
  LineChart,
  Download
} from "lucide-react";
import { GeminiAnalysisService } from '../services/gemini-analysis.service';
import { useDataVisualizationStore } from '../stores/data-visualization.store';
import { QueryRequest, QueryResponse, UploadedFile, VisualizationConfig } from '../types';
import { DATA_VIZ_CONFIG, ERROR_MESSAGES } from '../constants';
import { PlotlyChart } from './PlotlyChart';
import { nanoid } from 'nanoid';

interface EnhancedQueryInterfaceProps {
  file: UploadedFile;
  dataDescription?: string;
  className?: string;
}

interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  code?: string;
  visualizations?: VisualizationConfig[];
  images?: string[]; // Base64 encoded images
  timestamp: Date;
  status?: 'sending' | 'completed' | 'error';
  error?: string;
  executionResult?: string;
}

interface QuerySettings {
  analysisType: 'quick' | 'detailed' | 'research' | 'business';
  includeCode: boolean;
  includeVisualizations: boolean;
  responseStyle: 'concise' | 'detailed' | 'technical';
}

export const EnhancedQueryInterface: React.FC<EnhancedQueryInterfaceProps> = ({
  file,
  dataDescription = '',
  className = ""
}) => {
  const {
    addQuery,
    updateQuery,
    addQueryResponse,
    setQuerying,
    addError,
    isQuerying
  } = useDataVisualizationStore();

  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: 'welcome',
      type: 'assistant',
      content: `Hello! I'm your enhanced AI data analyst powered by Gemini 2.5 Pro with code execution capabilities. I can:

🔍 **Analyze your data** with advanced statistical methods
📊 **Create visualizations** using Python (matplotlib, pandas, numpy)
🧮 **Execute code** in real-time to answer your questions
💡 **Provide insights** with evidence-based analysis

**Your dataset:** ${file.name} (${file.data.length} rows × ${file.headers.length} columns)
${dataDescription ? `**Context:** ${dataDescription}` : ''}

**Available Libraries:** pandas, matplotlib, numpy, scipy (standard Python libraries)
**Note:** Some external libraries (plotly, seaborn) may not be available in the execution environment.

**Try asking:**
• "Show me the distribution of [column] with a histogram"
• "Create a scatter plot of [x] vs [y] and show the correlation"
• "What's the correlation between all numeric columns?"
• "Find outliers in [column] and visualize them"
• "Generate a bar chart showing [category] by [value]"
• "Create a comprehensive statistical summary with plots"

What would you like to explore?`,
      timestamp: new Date()
    }
  ]);

  const [currentQuery, setCurrentQuery] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [settings, setSettings] = useState<QuerySettings>({
    analysisType: 'detailed',
    includeCode: true,
    includeVisualizations: true,
    responseStyle: 'detailed'
  });
  const [showSettings, setShowSettings] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const parseVisualizationsFromResponse = (text: string, code?: string): VisualizationConfig[] => {
    const visualizations: VisualizationConfig[] = [];

    // The GeminiAnalysisService.processQuery already returns properly formatted visualizations
    // This method is mainly for fallback parsing if needed

    if (code) {
      // Look for matplotlib or plotly chart configurations in the code
      const hasVisualization = code.includes('plt.') || code.includes('plotly') || code.includes('fig') || code.includes('chart');

      if (hasVisualization) {
        // Create a basic visualization placeholder that indicates code was generated
        visualizations.push({
          id: `code-viz-${Date.now()}`,
          type: 'scatter',
          title: 'Code-Generated Visualization',
          description: 'Visualization code was generated. Execute the code to see the chart.',
          data: [], // Empty data since this is just a placeholder
          config: {},
          codeGenerated: true,
          generatedCode: code
        });
      }
    }

    // If no visualizations found but there's meaningful analysis text,
    // suggest that the user might want to ask for a specific chart
    if (visualizations.length === 0 && text.length > 100) {
      const hasDataMentions = text.toLowerCase().includes('data') ||
                             text.toLowerCase().includes('values') ||
                             text.toLowerCase().includes('trend') ||
                             text.toLowerCase().includes('pattern');

      if (hasDataMentions) {
        visualizations.push({
          id: `suggestion-${Date.now()}`,
          type: 'column',
          title: 'Visualization Suggestion',
          description: 'Based on the analysis, you might want to ask for a specific chart type (e.g., "show me a bar chart of..." or "create a line graph of...")',
          data: [],
          config: {},
          isSuggestion: true
        });
      }
    }

    return visualizations;
  };

  const handleSubmitQuery = async () => {
    if (!currentQuery.trim() || isProcessing) return;

    if (currentQuery.length > DATA_VIZ_CONFIG.MAX_QUERY_LENGTH) {
      addError(ERROR_MESSAGES.QUERY_TOO_LONG);
      return;
    }

    const queryId = nanoid();
    const userMessage: ChatMessage = {
      id: `user-${queryId}`,
      type: 'user',
      content: currentQuery.trim(),
      timestamp: new Date()
    };

    const assistantMessage: ChatMessage = {
      id: `assistant-${queryId}`,
      type: 'assistant',
      content: '',
      timestamp: new Date(),
      status: 'sending'
    };

    // Add messages to chat
    setMessages(prev => [...prev, userMessage, assistantMessage]);
    
    // Create query request
    const queryRequest: QueryRequest = {
      id: queryId,
      fileId: file.id,
      question: currentQuery.trim(),
      timestamp: new Date(),
      status: 'processing'
    };

    addQuery(queryRequest);
    setCurrentQuery('');
    setIsProcessing(true);
    setQuerying(true);

    try {
      // Process query with enhanced Gemini
      const response = await GeminiAnalysisService.processEnhancedQuery(
        file,
        queryRequest.question,
        settings,
        dataDescription
      );

      // Use visualizations from Gemini response if available, otherwise parse from code
      let visualizations = response.visualizations || [];

      // If no visualizations from Gemini, try to parse from code/text
      if (visualizations.length === 0) {
        visualizations = parseVisualizationsFromResponse(response.text || '', response.code);
      }

      // Create query response
      const queryResponse: QueryResponse = {
        id: nanoid(),
        requestId: queryId,
        answer: response.text || 'Analysis completed',
        code: response.code,
        visualizations,
        executionTime: 0
      };

      addQueryResponse(queryResponse);
      updateQuery(queryId, { status: 'completed' });

      // Check for library import errors
      const hasLibraryError = response.result &&
        (response.result.includes('No module named') || response.result.includes('ModuleNotFoundError'));

      // Update assistant message
      setMessages(prev => prev.map(msg =>
        msg.id === assistantMessage.id
          ? {
              ...msg,
              content: queryResponse.answer,
              code: queryResponse.code,
              visualizations: queryResponse.visualizations,
              images: response.images,
              executionResult: response.result,
              status: 'completed'
            }
          : msg
      ));

      if (hasLibraryError) {
        toast.success('Query processed with standard libraries (some visualization libraries unavailable)');
      } else {
        toast.success('Query processed successfully with code execution!');
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Query processing failed';
      
      updateQuery(queryId, { status: 'error' });
      addError(errorMessage);

      // Update assistant message with error
      setMessages(prev => prev.map(msg => 
        msg.id === assistantMessage.id 
          ? {
              ...msg,
              content: 'I apologize, but I encountered an error processing your query. Please try rephrasing your question or check if your data is properly formatted.',
              status: 'error',
              error: errorMessage
            }
          : msg
      ));

      toast.error(errorMessage);
    } finally {
      setIsProcessing(false);
      setQuerying(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmitQuery();
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('Copied to clipboard');
    } catch (error) {
      toast.error('Failed to copy to clipboard');
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const suggestedQuestions = [
    "What are the main patterns in this data?",
    "Show me summary statistics for all numeric columns",
    "Create a bar chart of the top 10 values",
    "What's the correlation between variables?",
    "Are there any outliers in the data?",
    "Show me the distribution of [column name]"
  ];

  return (
    <div className={`space-y-8 ${className}`}>
      {/* Header */}
      <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-blue-50/50">
        <CardHeader>
          <CardTitle className="flex items-center gap-3 text-2xl">
            <div className="p-3 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl">
              <Brain className="h-8 w-8 text-white" />
            </div>
            <div>
              <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                Ask AI About Your Data
              </span>
              <p className="text-sm font-normal text-gray-600 mt-1">
                Get instant insights and beautiful visualizations through natural language
              </p>
            </div>
          </CardTitle>
        </CardHeader>
      </Card>

      {/* Chat Interface */}
      <Card className="border-0 shadow-xl h-[600px] flex flex-col">
        <CardHeader className="pb-3 border-b">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5 text-blue-600" />
              Enhanced AI Data Assistant
              <Badge variant="outline" className="ml-2 bg-blue-50 text-blue-700">
                Gemini 2.5 Pro + Code Execution
              </Badge>
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowSettings(!showSettings)}
            >
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
          </div>
          <p className="text-sm text-gray-500 mt-2">
            Ask questions and get answers with real-time code execution and visualizations
          </p>
        </CardHeader>

        {/* Settings Panel */}
        {showSettings && (
          <div className="px-6 py-4 border-b bg-gray-50">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium">Analysis Type</Label>
                <Select
                  value={settings.analysisType}
                  onValueChange={(value: any) => setSettings(prev => ({ ...prev, analysisType: value }))}
                >
                  <SelectTrigger className="h-8">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="quick">Quick Analysis</SelectItem>
                    <SelectItem value="detailed">Detailed Analysis</SelectItem>
                    <SelectItem value="research">Research Grade</SelectItem>
                    <SelectItem value="business">Business Focused</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">Response Style</Label>
                <Select
                  value={settings.responseStyle}
                  onValueChange={(value: any) => setSettings(prev => ({ ...prev, responseStyle: value }))}
                >
                  <SelectTrigger className="h-8">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="concise">Concise</SelectItem>
                    <SelectItem value="detailed">Detailed</SelectItem>
                    <SelectItem value="technical">Technical</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">Options</Label>
                <div className="flex gap-2">
                  <Button
                    variant={settings.includeCode ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSettings(prev => ({ ...prev, includeCode: !prev.includeCode }))}
                  >
                    <Code className="h-3 w-3 mr-1" />
                    Code
                  </Button>
                  <Button
                    variant={settings.includeVisualizations ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSettings(prev => ({ ...prev, includeVisualizations: !prev.includeVisualizations }))}
                  >
                    <BarChart3 className="h-3 w-3 mr-1" />
                    Viz
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        <CardContent className="flex-1 flex flex-col p-0">
          {/* Messages Area */}
          <ScrollArea className="flex-1 p-6">
            <div className="space-y-6">
              {messages.map((message) => (
                <div key={message.id} className="space-y-4">
                  <div className={`flex gap-4 ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
                    <div className={`flex gap-4 max-w-[85%] ${message.type === 'user' ? 'flex-row-reverse' : 'flex-row'}`}>
                      {/* Avatar */}
                      <div className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center ${
                        message.type === 'user' 
                          ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white' 
                          : 'bg-gradient-to-r from-green-500 to-teal-500 text-white'
                      }`}>
                        {message.type === 'user' ? <User className="h-5 w-5" /> : <Bot className="h-5 w-5" />}
                      </div>

                      {/* Message Content */}
                      <div className={`space-y-3 ${message.type === 'user' ? 'text-right' : 'text-left'}`}>
                        <div className={`inline-block p-4 rounded-2xl ${
                          message.type === 'user'
                            ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white'
                            : 'bg-gray-100 text-gray-900'
                        }`}>
                          {message.status === 'sending' ? (
                            <div className="flex items-center gap-3">
                              <Loader2 className="h-5 w-5 animate-spin" />
                              <span>Analyzing your question...</span>
                            </div>
                          ) : (
                            <div className="whitespace-pre-wrap">{message.content}</div>
                          )}
                        </div>

                        {/* Code Block */}
                        {message.code && (
                          <div className="bg-gray-900 text-gray-100 p-4 rounded-xl text-sm font-mono">
                            <div className="flex items-center justify-between mb-3">
                              <div className="flex items-center gap-2">
                                <Code className="h-4 w-4" />
                                <span className="text-xs font-semibold">Executed Python Code</span>
                                <Badge variant="outline" className="text-xs bg-green-900 text-green-300 border-green-700">
                                  ✓ Executed
                                </Badge>
                              </div>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => copyToClipboard(message.code!)}
                                className="h-8 w-8 p-0 text-gray-400 hover:text-white"
                              >
                                <Copy className="h-4 w-4" />
                              </Button>
                            </div>
                            <pre className="whitespace-pre-wrap text-xs overflow-x-auto">{message.code}</pre>
                          </div>
                        )}

                        {/* Execution Result */}
                        {message.executionResult && (
                          <div className="bg-green-50 border border-green-200 p-3 rounded-lg">
                            <div className="flex items-center gap-2 mb-2">
                              <CheckCircle className="h-4 w-4 text-green-600" />
                              <span className="text-sm font-medium text-green-800">Execution Output</span>
                            </div>
                            <pre className="text-xs text-green-700 whitespace-pre-wrap">{message.executionResult}</pre>
                          </div>
                        )}

                        {/* Visualizations */}
                        {message.visualizations && message.visualizations.length > 0 && (
                          <div className="space-y-4">
                            <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
                              <BarChart3 className="h-4 w-4" />
                              <span>Generated Visualizations</span>
                            </div>
                            {message.visualizations.map((viz, index) => (
                              <PlotlyChart
                                key={index}
                                visualization={viz}
                                className="max-w-full"
                                onError={(error) => console.warn('Visualization error:', error)}
                              />
                            ))}
                          </div>
                        )}

                        {/* Generated Images */}
                        {message.images && message.images.length > 0 && (
                          <div className="space-y-4">
                            <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
                              <Eye className="h-4 w-4" />
                              <span>Generated Plots ({message.images.length})</span>
                            </div>
                            <div className="grid grid-cols-1 gap-4">
                              {message.images.map((imageBase64, index) => (
                                <div key={index} className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                                  <img
                                    src={`data:image/png;base64,${imageBase64}`}
                                    alt={`Generated plot ${index + 1}`}
                                    className="w-full h-auto rounded-lg"
                                    style={{ maxWidth: '100%', height: 'auto' }}
                                  />
                                  <div className="mt-2 flex items-center justify-between">
                                    <span className="text-xs text-gray-500">Plot {index + 1}</span>
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => {
                                        const link = document.createElement('a');
                                        link.href = `data:image/png;base64,${imageBase64}`;
                                        link.download = `plot_${index + 1}.png`;
                                        link.click();
                                      }}
                                    >
                                      <Download className="h-3 w-3 mr-1" />
                                      Download
                                    </Button>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Error */}
                        {message.status === 'error' && (
                          <Alert variant="destructive">
                            <AlertTriangle className="h-4 w-4" />
                            <AlertDescription className="text-sm">
                              {message.error || 'An error occurred processing this query'}
                            </AlertDescription>
                          </Alert>
                        )}

                        {/* Timestamp */}
                        <div className="text-xs text-gray-500">
                          {formatTimestamp(message.timestamp)}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
              <div ref={messagesEndRef} />
            </div>
          </ScrollArea>

          <Separator />

          {/* Suggested Questions */}
          {messages.length === 1 && (
            <div className="p-4 border-b bg-gray-50">
              <p className="text-sm font-medium mb-3 text-gray-700">Try asking:</p>
              <div className="flex flex-wrap gap-2">
                {suggestedQuestions.map((question, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentQuery(question)}
                    className="text-xs bg-white hover:bg-blue-50 border-blue-200"
                  >
                    <Sparkles className="h-3 w-3 mr-1" />
                    {question}
                  </Button>
                ))}
              </div>
            </div>
          )}

          {/* Input Area */}
          <div className="p-6">
            <div className="flex gap-3">
              <Textarea
                ref={textareaRef}
                value={currentQuery}
                onChange={(e) => setCurrentQuery(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Ask me anything about your data... For example: 'Show me a bar chart of sales by region' or 'What are the trends in this data?'"
                className="flex-1 min-h-[80px] max-h-[120px] resize-none border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                disabled={isProcessing}
              />
              <Button
                onClick={handleSubmitQuery}
                disabled={!currentQuery.trim() || isProcessing}
                className="self-end bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
                size="lg"
              >
                {isProcessing ? (
                  <Loader2 className="h-5 w-5 animate-spin" />
                ) : (
                  <Send className="h-5 w-5" />
                )}
              </Button>
            </div>
            
            {currentQuery.length > DATA_VIZ_CONFIG.MAX_QUERY_LENGTH * 0.8 && (
              <p className="text-xs text-gray-500 mt-2">
                {DATA_VIZ_CONFIG.MAX_QUERY_LENGTH - currentQuery.length} characters remaining
              </p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
