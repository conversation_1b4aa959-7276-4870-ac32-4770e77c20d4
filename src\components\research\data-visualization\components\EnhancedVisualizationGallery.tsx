import React, { useState, useMemo } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import {
  BarChart3,
  LineChart,
  PieChart,
  Circle,
  TrendingUp,
  Map,
  Network,
  Grid3x3,
  Search,
  Sparkles,
  Eye,
  Play,
  Star,
  Filter,
  Layers,
  Globe,
  Workflow,
  TreePine,
  Target,
  Activity
} from 'lucide-react';
import { toast } from 'sonner';
import { GPTVisChart } from './GPTVisChart';
import { ChartType } from '@antv/gpt-vis';
import { ChartRecommendationService, type ChartRecommendation } from '../services/chart-recommendation.service';

interface ChartTypeInfo {
  type: ChartType;
  title: string;
  description: string;
  category: 'statistical' | 'geographic' | 'relationship' | 'text' | 'advanced';
  icon: React.ComponentType<any>;
  useCase: string;
  dataRequirements: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  popularity: number; // 1-5 stars
}

const CHART_TYPES: ChartTypeInfo[] = [
  // Statistical Charts
  {
    type: 'line',
    title: 'Line Chart',
    description: 'Perfect for showing trends and changes over time',
    category: 'statistical',
    icon: LineChart,
    useCase: 'Time series data, trend analysis, continuous data',
    dataRequirements: ['Numeric values', 'Time/sequence column'],
    difficulty: 'beginner',
    popularity: 5
  },
  {
    type: 'column',
    title: 'Column Chart',
    description: 'Compare values across different categories',
    category: 'statistical',
    icon: BarChart3,
    useCase: 'Categorical comparisons, rankings, discrete data',
    dataRequirements: ['Categorical column', 'Numeric values'],
    difficulty: 'beginner',
    popularity: 5
  },
  {
    type: 'bar',
    title: 'Bar Chart',
    description: 'Horizontal comparison of categories',
    category: 'statistical',
    icon: BarChart3,
    useCase: 'Long category names, horizontal comparisons',
    dataRequirements: ['Categorical column', 'Numeric values'],
    difficulty: 'beginner',
    popularity: 4
  },
  {
    type: 'pie',
    title: 'Pie Chart',
    description: 'Show parts of a whole relationship',
    category: 'statistical',
    icon: PieChart,
    useCase: 'Part-to-whole relationships, proportions',
    dataRequirements: ['Categorical column', 'Numeric values'],
    difficulty: 'beginner',
    popularity: 4
  },
  {
    type: 'scatter',
    title: 'Scatter Plot',
    description: 'Explore relationships between two variables',
    category: 'statistical',
    icon: Circle,
    useCase: 'Correlation analysis, relationship exploration',
    dataRequirements: ['Two numeric columns'],
    difficulty: 'intermediate',
    popularity: 4
  },
  {
    type: 'area',
    title: 'Area Chart',
    description: 'Show cumulative totals over time',
    category: 'statistical',
    icon: TrendingUp,
    useCase: 'Cumulative data, stacked values over time',
    dataRequirements: ['Time column', 'Numeric values'],
    difficulty: 'intermediate',
    popularity: 3
  },
  {
    type: 'histogram',
    title: 'Histogram',
    description: 'Display distribution of numerical data',
    category: 'statistical',
    icon: BarChart3,
    useCase: 'Data distribution, frequency analysis',
    dataRequirements: ['Numeric column'],
    difficulty: 'intermediate',
    popularity: 3
  },
  
  // Advanced Charts
  {
    type: 'dual-axis',
    title: 'Dual-Axis Chart',
    description: 'Compare two metrics with different scales',
    category: 'advanced',
    icon: Activity,
    useCase: 'Multiple metrics, different scales',
    dataRequirements: ['Two numeric columns', 'Common dimension'],
    difficulty: 'advanced',
    popularity: 3
  },
  {
    type: 'radar',
    title: 'Radar Chart',
    description: 'Multi-dimensional data comparison',
    category: 'advanced',
    icon: Target,
    useCase: 'Multi-dimensional comparisons, performance metrics',
    dataRequirements: ['Multiple numeric columns'],
    difficulty: 'advanced',
    popularity: 2
  },
  {
    type: 'treemap',
    title: 'Treemap',
    description: 'Hierarchical data with nested rectangles',
    category: 'advanced',
    icon: Grid3x3,
    useCase: 'Hierarchical data, nested proportions',
    dataRequirements: ['Hierarchical structure', 'Numeric values'],
    difficulty: 'advanced',
    popularity: 2
  },
  
  // Text Visualizations
  {
    type: 'word-cloud',
    title: 'Word Cloud',
    description: 'Visualize text frequency and importance',
    category: 'text',
    icon: Sparkles,
    useCase: 'Text analysis, keyword frequency',
    dataRequirements: ['Text column'],
    difficulty: 'intermediate',
    popularity: 3
  },
  
  // Geographic Maps
  {
    type: 'pin-map',
    title: 'Pin Map',
    description: 'Show locations with markers on a map',
    category: 'geographic',
    icon: Map,
    useCase: 'Location-based data, geographic distribution',
    dataRequirements: ['Latitude/Longitude or Address'],
    difficulty: 'intermediate',
    popularity: 4
  },
  {
    type: 'heat-map',
    title: 'Heat Map',
    description: 'Show data intensity across geographic regions',
    category: 'geographic',
    icon: Globe,
    useCase: 'Geographic density, regional comparisons',
    dataRequirements: ['Geographic regions', 'Numeric values'],
    difficulty: 'advanced',
    popularity: 3
  },
  
  // Relationship Diagrams
  {
    type: 'network-graph',
    title: 'Network Graph',
    description: 'Visualize connections and relationships',
    category: 'relationship',
    icon: Network,
    useCase: 'Network analysis, relationship mapping',
    dataRequirements: ['Node connections', 'Relationship data'],
    difficulty: 'advanced',
    popularity: 3
  },
  {
    type: 'flow-diagram',
    title: 'Flow Diagram',
    description: 'Show process flows and workflows',
    category: 'relationship',
    icon: Workflow,
    useCase: 'Process visualization, workflow mapping',
    dataRequirements: ['Process steps', 'Flow connections'],
    difficulty: 'advanced',
    popularity: 2
  },
  {
    type: 'organization-chart',
    title: 'Organization Chart',
    description: 'Display hierarchical organizational structures',
    category: 'relationship',
    icon: TreePine,
    useCase: 'Organizational hierarchy, reporting structures',
    dataRequirements: ['Hierarchical relationships'],
    difficulty: 'intermediate',
    popularity: 3
  }
];

interface EnhancedVisualizationGalleryProps {
  data: any[];
  onChartSelect: (chartType: ChartType, config?: any) => void;
  recommendations?: ChartRecommendation[];
  className?: string;
}

export const EnhancedVisualizationGallery: React.FC<EnhancedVisualizationGalleryProps> = ({
  data,
  onChartSelect,
  recommendations = [],
  className = ''
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>('all');
  const [previewChart, setPreviewChart] = useState<ChartType | null>(null);

  // Filter charts based on search and filters
  const filteredCharts = useMemo(() => {
    return CHART_TYPES.filter(chart => {
      const matchesSearch = chart.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           chart.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           chart.useCase.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesCategory = selectedCategory === 'all' || chart.category === selectedCategory;
      const matchesDifficulty = selectedDifficulty === 'all' || chart.difficulty === selectedDifficulty;
      
      return matchesSearch && matchesCategory && matchesDifficulty;
    });
  }, [searchTerm, selectedCategory, selectedDifficulty]);

  // Group charts by category
  const chartsByCategory = useMemo(() => {
    const grouped: Record<string, ChartTypeInfo[]> = {};
    filteredCharts.forEach(chart => {
      if (!grouped[chart.category]) {
        grouped[chart.category] = [];
      }
      grouped[chart.category].push(chart);
    });
    return grouped;
  }, [filteredCharts]);

  // Get recommendation score for a chart type
  const getRecommendationScore = (chartType: ChartType): number => {
    const rec = recommendations.find(r => r.chartType === chartType);
    return rec ? rec.confidence : 0;
  };

  // Handle chart selection
  const handleChartSelect = (chartType: ChartType) => {
    if (data.length === 0) {
      toast.error('Please upload data first to create visualizations');
      return;
    }

    try {
      onChartSelect(chartType);
      toast.success(`${CHART_TYPES.find(c => c.type === chartType)?.title} selected!`);
    } catch (error) {
      toast.error('Failed to select chart type');
    }
  };

  // Handle chart preview
  const handlePreview = (chartType: ChartType) => {
    if (data.length === 0) {
      toast.warning('Upload data to see chart preview');
      return;
    }
    setPreviewChart(chartType);
  };

  // Render star rating
  const renderStars = (count: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star 
        key={i} 
        className={`h-3 w-3 ${i < count ? 'text-yellow-400 fill-current' : 'text-gray-300'}`} 
      />
    ));
  };

  // Render chart card
  const renderChartCard = (chart: ChartTypeInfo) => {
    const Icon = chart.icon;
    const recommendationScore = getRecommendationScore(chart.type);
    const isRecommended = recommendationScore > 70;

    return (
      <Card 
        key={chart.type} 
        className={`relative transition-all duration-200 hover:shadow-lg cursor-pointer group ${
          isRecommended ? 'ring-2 ring-blue-500 bg-blue-50' : ''
        }`}
      >
        {isRecommended && (
          <Badge className="absolute -top-2 -right-2 bg-blue-500 text-white">
            <Sparkles className="h-3 w-3 mr-1" />
            Recommended
          </Badge>
        )}
        
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Icon className="h-5 w-5 text-blue-600" />
              <CardTitle className="text-sm font-medium">{chart.title}</CardTitle>
            </div>
            <div className="flex items-center gap-1">
              {renderStars(chart.popularity)}
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-3">
          <p className="text-xs text-gray-600">{chart.description}</p>
          
          <div className="space-y-2">
            <div>
              <p className="text-xs font-medium text-gray-700">Best for:</p>
              <p className="text-xs text-gray-500">{chart.useCase}</p>
            </div>
            
            <div>
              <p className="text-xs font-medium text-gray-700">Requirements:</p>
              <div className="flex flex-wrap gap-1 mt-1">
                {chart.dataRequirements.map((req, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {req}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
          
          {recommendationScore > 0 && (
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="text-xs">
                {recommendationScore}% match
              </Badge>
            </div>
          )}
          
          <div className="flex gap-2 pt-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => handlePreview(chart.type)}
              className="flex-1 text-xs"
            >
              <Eye className="h-3 w-3 mr-1" />
              Preview
            </Button>
            <Button
              size="sm"
              onClick={() => handleChartSelect(chart.type)}
              className="flex-1 text-xs"
            >
              <Play className="h-3 w-3 mr-1" />
              Create
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Visualization Gallery</h3>
          <p className="text-sm text-gray-600">Choose from 20+ chart types to visualize your data</p>
        </div>
        {recommendations.length > 0 && (
          <Badge variant="secondary" className="flex items-center gap-1">
            <Sparkles className="h-3 w-3" />
            {recommendations.length} AI Recommendations
          </Badge>
        )}
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search chart types..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <div className="flex gap-2">
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-3 py-2 border rounded-md text-sm"
          >
            <option value="all">All Categories</option>
            <option value="statistical">Statistical</option>
            <option value="geographic">Geographic</option>
            <option value="relationship">Relationship</option>
            <option value="text">Text</option>
            <option value="advanced">Advanced</option>
          </select>
          
          <select
            value={selectedDifficulty}
            onChange={(e) => setSelectedDifficulty(e.target.value)}
            className="px-3 py-2 border rounded-md text-sm"
          >
            <option value="all">All Levels</option>
            <option value="beginner">Beginner</option>
            <option value="intermediate">Intermediate</option>
            <option value="advanced">Advanced</option>
          </select>
        </div>
      </div>

      {/* Chart Gallery */}
      <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="all">All</TabsTrigger>
          <TabsTrigger value="statistical">Statistical</TabsTrigger>
          <TabsTrigger value="geographic">Geographic</TabsTrigger>
          <TabsTrigger value="relationship">Relationship</TabsTrigger>
          <TabsTrigger value="text">Text</TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {filteredCharts.map(renderChartCard)}
          </div>
        </TabsContent>

        {Object.entries(chartsByCategory).map(([category, charts]) => (
          <TabsContent key={category} value={category} className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {charts.map(renderChartCard)}
            </div>
          </TabsContent>
        ))}
      </Tabs>

      {/* No Results */}
      {filteredCharts.length === 0 && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Filter className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No charts found</h3>
            <p className="text-gray-500 text-center">
              Try adjusting your search terms or filters to find the perfect visualization for your data.
            </p>
            <Button
              variant="outline"
              onClick={() => {
                setSearchTerm('');
                setSelectedCategory('all');
                setSelectedDifficulty('all');
              }}
              className="mt-4"
            >
              Clear Filters
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Chart Preview Modal */}
      {previewChart && data.length > 0 && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-4xl max-h-[90vh] overflow-auto">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>
                  {CHART_TYPES.find(c => c.type === previewChart)?.title} Preview
                </CardTitle>
                <Button variant="outline" onClick={() => setPreviewChart(null)}>
                  Close
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <GPTVisChart
                chartType={previewChart}
                data={data.slice(0, 100)} // Limit data for preview
                title="Preview Chart"
                description="This is a preview of how your data will look with this chart type"
              />
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default EnhancedVisualizationGallery;
