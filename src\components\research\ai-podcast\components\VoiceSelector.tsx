import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Users, 
  Volume2, 
  Play, 
  Pause, 
  Settings,
  User,
  UserCheck,
  Globe,
  Mic
} from "lucide-react";

import { VoiceOption, AudioProvider } from '../types';

interface VoiceSelectorProps {
  availableVoices: VoiceOption[];
  availableProviders: AudioProvider[];
  selectedVoices: {
    host1?: VoiceOption;
    host2?: VoiceOption;
  };
  selectedProvider?: AudioProvider;
  outputLanguage: string;
  supportedLanguages: Array<{ code: string; name: string; flag: string }>;
  onVoiceSelect: (hostNumber: 'host1' | 'host2', voice: VoiceOption) => void;
  onProviderChange: (provider: AudioProvider) => void;
  onLanguageChange: (language: string) => void;
}

export function VoiceSelector({
  availableVoices,
  availableProviders,
  selectedVoices,
  selectedProvider,
  outputLanguage,
  supportedLanguages,
  onVoiceSelect,
  onProviderChange,
  onLanguageChange
}: VoiceSelectorProps) {
  const [playingVoice, setPlayingVoice] = useState<string | null>(null);

  const handleVoicePreview = (voiceId: string) => {
    if (playingVoice === voiceId) {
      setPlayingVoice(null);
      // Stop audio playback
    } else {
      setPlayingVoice(voiceId);
      // Start audio playback
      // In a real implementation, this would play a voice sample
      setTimeout(() => setPlayingVoice(null), 3000); // Auto-stop after 3 seconds
    }
  };

  const getVoicesByProvider = (providerId: string) => {
    return availableVoices.filter(voice => voice.provider.id === providerId);
  };

  const getVoicesByLanguage = (voices: VoiceOption[]) => {
    return voices.filter(voice => voice.language === outputLanguage);
  };

  const filteredVoices = selectedProvider 
    ? getVoicesByLanguage(getVoicesByProvider(selectedProvider.id))
    : getVoicesByLanguage(availableVoices);

  const maleVoices = filteredVoices.filter(voice => voice.gender === 'male');
  const femaleVoices = filteredVoices.filter(voice => voice.gender === 'female');

  return (
    <div className="space-y-6">
      {/* Provider Selection */}
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <Settings className="h-5 w-5 text-blue-500" />
          <h3 className="text-lg font-semibold">Audio Provider</h3>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {availableProviders.map((provider) => (
            <Card 
              key={provider.id}
              className={`cursor-pointer transition-all ${
                selectedProvider?.id === provider.id 
                  ? 'border-blue-500 bg-blue-50 shadow-md' 
                  : 'border-gray-200 hover:border-blue-300'
              }`}
              onClick={() => onProviderChange(provider)}
            >
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">{provider.name}</h4>
                    <p className="text-sm text-gray-600">
                      {getVoicesByProvider(provider.id).length} voices available
                    </p>
                  </div>
                  {selectedProvider?.id === provider.id && (
                    <UserCheck className="h-5 w-5 text-blue-500" />
                  )}
                </div>
                {provider.maxDuration && (
                  <Badge variant="secondary" className="mt-2 text-xs">
                    Max: {Math.floor(provider.maxDuration / 60)} min
                  </Badge>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Language Selection */}
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <Globe className="h-5 w-5 text-green-500" />
          <h3 className="text-lg font-semibold">Output Language</h3>
        </div>
        
        <Select value={outputLanguage} onValueChange={onLanguageChange}>
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select language" />
          </SelectTrigger>
          <SelectContent>
            {supportedLanguages.map((lang) => (
              <SelectItem key={lang.code} value={lang.code}>
                <div className="flex items-center gap-2">
                  <span>{lang.flag}</span>
                  <span>{lang.name}</span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Voice Selection */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Users className="h-5 w-5 text-purple-500" />
          <h3 className="text-lg font-semibold">Select Podcast Hosts</h3>
        </div>

        {/* Host 1 Selection */}
        <Card className="border border-gray-200">
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <User className="h-4 w-4" />
              Host 1 (Primary)
              {selectedVoices.host1 && (
                <Badge variant="outline" className="ml-auto">
                  {selectedVoices.host1.gender} • {selectedVoices.host1.name}
                </Badge>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {maleVoices.map((voice) => (
                <Card 
                  key={voice.id}
                  className={`cursor-pointer transition-all ${
                    selectedVoices.host1?.id === voice.id 
                      ? 'border-purple-500 bg-purple-50' 
                      : 'border-gray-200 hover:border-purple-300'
                  }`}
                  onClick={() => onVoiceSelect('host1', voice)}
                >
                  <CardContent className="p-3">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-sm">{voice.name}</h4>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleVoicePreview(voice.id);
                        }}
                      >
                        {playingVoice === voice.id ? (
                          <Pause className="h-3 w-3" />
                        ) : (
                          <Play className="h-3 w-3" />
                        )}
                      </Button>
                    </div>
                    <p className="text-xs text-gray-600 mb-2">{voice.description}</p>
                    <div className="flex gap-1">
                      <Badge variant="secondary" className="text-xs">
                        {voice.gender}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {voice.provider.name}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Host 2 Selection */}
        <Card className="border border-gray-200">
          <CardHeader className="pb-3">
            <CardTitle className="text-base flex items-center gap-2">
              <User className="h-4 w-4" />
              Host 2 (Secondary)
              {selectedVoices.host2 && (
                <Badge variant="outline" className="ml-auto">
                  {selectedVoices.host2.gender} • {selectedVoices.host2.name}
                </Badge>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {femaleVoices.map((voice) => (
                <Card 
                  key={voice.id}
                  className={`cursor-pointer transition-all ${
                    selectedVoices.host2?.id === voice.id 
                      ? 'border-purple-500 bg-purple-50' 
                      : 'border-gray-200 hover:border-purple-300'
                  }`}
                  onClick={() => onVoiceSelect('host2', voice)}
                >
                  <CardContent className="p-3">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-sm">{voice.name}</h4>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleVoicePreview(voice.id);
                        }}
                      >
                        {playingVoice === voice.id ? (
                          <Pause className="h-3 w-3" />
                        ) : (
                          <Play className="h-3 w-3" />
                        )}
                      </Button>
                    </div>
                    <p className="text-xs text-gray-600 mb-2">{voice.description}</p>
                    <div className="flex gap-1">
                      <Badge variant="secondary" className="text-xs">
                        {voice.gender}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {voice.provider.name}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Voice Configuration Summary */}
      {selectedVoices.host1 && selectedVoices.host2 && (
        <Card className="border border-green-200 bg-green-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-3">
              <Mic className="h-4 w-4 text-green-600" />
              <h4 className="font-medium text-green-900">Voice Configuration Ready</h4>
            </div>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p className="font-medium text-green-800">Host 1:</p>
                <p className="text-green-700">{selectedVoices.host1.name}</p>
                <p className="text-green-600 text-xs">{selectedVoices.host1.description}</p>
              </div>
              <div>
                <p className="font-medium text-green-800">Host 2:</p>
                <p className="text-green-700">{selectedVoices.host2.name}</p>
                <p className="text-green-600 text-xs">{selectedVoices.host2.description}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Tips */}
      <Card className="border border-blue-200 bg-blue-50">
        <CardContent className="p-4">
          <h4 className="font-medium text-blue-900 mb-2">🎙️ Voice Selection Tips</h4>
          <div className="space-y-1 text-sm text-blue-800">
            <p>• Different voice genders create more engaging conversations</p>
            <p>• Preview voices to find the best match for your content</p>
            <p>• Professional voices work well for academic content</p>
            <p>• Conversational voices are great for casual discussions</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
