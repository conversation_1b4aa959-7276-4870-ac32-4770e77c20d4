/**
 * Content Input Component
 * Handles input of content to be humanized
 */

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import {
  Edit3,
  FileText,
  AlertCircle,
  CheckCircle,
  Upload,
  Trash2,
  Eye,
  Copy,
  Sparkles
} from "lucide-react";
import { toast } from 'sonner';

import { useHumanizerStore } from '../stores/humanizer.store';
import { VALIDATION_LIMITS } from '../types';

export function ContentInput() {
  const {
    originalContent,
    sampleTexts,
    lastError,
    setOriginalContent,
    clearContent,
    validateContent,
    canProcessContent,
    clearError
  } = useHumanizerStore();

  const [localContent, setLocalContent] = useState(originalContent);
  const [wordCount, setWordCount] = useState(0);
  const [isValid, setIsValid] = useState(false);

  // Update local state when store changes
  useEffect(() => {
    setLocalContent(originalContent);
  }, [originalContent]);

  // Update word count and validation
  useEffect(() => {
    const words = localContent.trim() ? localContent.trim().split(/\s+/).length : 0;
    setWordCount(words);
    
    const validation = validateContent(localContent);
    setIsValid(!validation);
  }, [localContent, validateContent]);

  /**
   * Handle content change
   */
  const handleContentChange = (value: string) => {
    setLocalContent(value);
    clearError();
  };

  /**
   * Handle saving content to store
   */
  const handleSaveContent = () => {
    const validation = validateContent(localContent);
    if (validation) {
      toast.error(validation);
      return;
    }

    setOriginalContent(localContent);
    toast.success('Content saved successfully!');
  };

  /**
   * Handle clearing content
   */
  const handleClearContent = () => {
    setLocalContent('');
    clearContent();
    toast.success('Content cleared');
  };

  /**
   * Handle copying content to clipboard
   */
  const handleCopyContent = async () => {
    try {
      await navigator.clipboard.writeText(localContent);
      toast.success('Content copied to clipboard');
    } catch (error) {
      toast.error('Failed to copy content');
    }
  };

  /**
   * Handle pasting from clipboard
   */
  const handlePasteFromClipboard = async () => {
    try {
      const text = await navigator.clipboard.readText();
      if (text) {
        setLocalContent(text);
        toast.success('Content pasted from clipboard');
      }
    } catch (error) {
      toast.error('Failed to paste from clipboard');
    }
  };

  /**
   * Get word count color based on limits
   */
  const getWordCountColor = () => {
    if (wordCount === 0) return 'text-gray-500';
    if (wordCount > VALIDATION_LIMITS.MAX_CONTENT_LENGTH) return 'text-red-600';
    if (wordCount < VALIDATION_LIMITS.MIN_CONTENT_LENGTH / 2) return 'text-yellow-600';
    return 'text-green-600';
  };

  /**
   * Get progress percentage for word count
   */
  const getWordCountProgress = () => {
    return Math.min((wordCount / VALIDATION_LIMITS.MAX_CONTENT_LENGTH) * 100, 100);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold text-gray-800 mb-2">Academic Content to Humanize</h2>
        <p className="text-gray-600">
          Paste the academic content you want to rewrite in your scholarly writing style.
          The AI will analyze your samples and apply your unique academic voice to research papers, dissertations, or reports.
        </p>
      </div>

      {/* Prerequisites Check */}
      {sampleTexts.length === 0 && (
        <Alert className="border-yellow-200 bg-yellow-50">
          <AlertCircle className="h-4 w-4 text-yellow-600" />
          <AlertDescription className="text-yellow-800">
            You need to add academic writing samples first before you can humanize content.
            Go to the "Academic Writing Samples" tab to add examples from your research papers, dissertations, or reports.
          </AlertDescription>
        </Alert>
      )}

      {/* Error Alert */}
      {lastError && lastError.code.includes('VALIDATION') && (
        <Alert className="border-red-200 bg-red-50">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            {lastError.message}
          </AlertDescription>
        </Alert>
      )}

      {/* Content Input Card */}
      <Card className="shadow-lg">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg flex items-center gap-2">
                <Edit3 className="h-5 w-5 text-blue-600" />
                Content Input
              </CardTitle>
              <CardDescription>
                Enter or paste the content you want to humanize (max {VALIDATION_LIMITS.MAX_CONTENT_LENGTH} words)
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handlePasteFromClipboard}
                disabled={sampleTexts.length === 0}
              >
                <Upload className="h-4 w-4 mr-2" />
                Paste
              </Button>
              {localContent && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCopyContent}
                >
                  <Copy className="h-4 w-4 mr-2" />
                  Copy
                </Button>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="content-input">Content</Label>
            <Textarea
              id="content-input"
              placeholder={
                sampleTexts.length === 0
                  ? "Please add sample texts first..."
                  : "Paste your content here to be rewritten in your personal style..."
              }
              value={localContent}
              onChange={(e) => handleContentChange(e.target.value)}
              rows={12}
              disabled={sampleTexts.length === 0}
              className="resize-none"
            />
          </div>

          {/* Word Count and Progress */}
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Word Count</span>
              <span className={`text-sm font-medium ${getWordCountColor()}`}>
                {wordCount} / {VALIDATION_LIMITS.MAX_CONTENT_LENGTH} words
              </span>
            </div>
            <Progress 
              value={getWordCountProgress()} 
              className="h-2"
            />
            {wordCount > 0 && (
              <div className="text-xs text-gray-500">
                {wordCount < VALIDATION_LIMITS.MIN_CONTENT_LENGTH / 2 && (
                  <span className="text-yellow-600">
                    Add more content for better results (recommended: 50+ words)
                  </span>
                )}
                {wordCount > VALIDATION_LIMITS.MAX_CONTENT_LENGTH && (
                  <span className="text-red-600">
                    Content exceeds maximum word limit
                  </span>
                )}
                {wordCount >= VALIDATION_LIMITS.MIN_CONTENT_LENGTH / 2 && 
                 wordCount <= VALIDATION_LIMITS.MAX_CONTENT_LENGTH && (
                  <span className="text-green-600">
                    Content length is good for processing
                  </span>
                )}
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2 pt-4">
            <Button
              onClick={handleSaveContent}
              disabled={!isValid || !localContent.trim() || sampleTexts.length === 0}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              Save Content
            </Button>
            
            {localContent && (
              <Button
                variant="outline"
                onClick={handleClearContent}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Clear
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Status Card */}
      <Card className={`border-2 ${canProcessContent() ? 'border-green-200 bg-green-50' : 'border-gray-200 bg-gray-50'}`}>
        <CardContent className="pt-6">
          <div className="flex items-center gap-3">
            {canProcessContent() ? (
              <>
                <CheckCircle className="h-6 w-6 text-green-600" />
                <div>
                  <h3 className="font-semibold text-green-800">Ready to Process</h3>
                  <p className="text-sm text-green-700">
                    You have {sampleTexts.length} sample text(s) and {wordCount} words of content ready for humanization.
                  </p>
                </div>
              </>
            ) : (
              <>
                <AlertCircle className="h-6 w-6 text-gray-500" />
                <div>
                  <h3 className="font-semibold text-gray-700">Not Ready</h3>
                  <p className="text-sm text-gray-600">
                    {sampleTexts.length === 0 && "Add sample texts first. "}
                    {!originalContent && sampleTexts.length > 0 && "Add content to humanize. "}
                    {originalContent && !isValid && "Fix content validation issues."}
                  </p>
                </div>
              </>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Tips Card */}
      <Card className="bg-blue-50 border-blue-200">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-blue-600" />
            Tips for Better Results
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="space-y-2 text-sm text-blue-800">
            <li className="flex items-start gap-2">
              <span className="text-blue-600 mt-1">•</span>
              <span>Longer content (100+ words) generally produces better humanization results</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="text-blue-600 mt-1">•</span>
              <span>Make sure your content is well-structured with clear paragraphs</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="text-blue-600 mt-1">•</span>
              <span>The AI will preserve technical terms and important information while applying your style</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="text-blue-600 mt-1">•</span>
              <span>Review the results and make manual adjustments if needed</span>
            </li>
          </ul>
        </CardContent>
      </Card>

      {/* Empty State */}
      {!localContent && sampleTexts.length > 0 && (
        <Card className="border-dashed border-2 border-gray-300">
          <CardContent className="flex flex-col items-center justify-center py-12 text-center">
            <FileText className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-semibold text-gray-600 mb-2">
              No content added yet
            </h3>
            <p className="text-gray-500 mb-4 max-w-md">
              Paste or type the content you want to rewrite in your personal writing style.
            </p>
            <Button onClick={handlePasteFromClipboard}>
              <Upload className="h-4 w-4 mr-2" />
              Paste from Clipboard
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
