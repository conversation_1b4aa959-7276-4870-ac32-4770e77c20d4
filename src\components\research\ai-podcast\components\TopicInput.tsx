import React, { useState } from 'react';
import { <PERSON>, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Lightbulb, Sparkles, TrendingUp, BookOpen, Zap } from "lucide-react";

interface TopicInputProps {
  value: string;
  onChange: (content: string) => void;
}

const SUGGESTED_TOPICS = [
  {
    category: "Technology",
    icon: <Zap className="h-4 w-4" />,
    topics: [
      "The future of artificial intelligence in healthcare",
      "Blockchain technology and its real-world applications",
      "The impact of quantum computing on cybersecurity",
      "How 5G is transforming smart cities"
    ]
  },
  {
    category: "Science",
    icon: <BookOpen className="h-4 w-4" />,
    topics: [
      "Climate change solutions and renewable energy",
      "The latest breakthroughs in space exploration",
      "Gene editing and the future of medicine",
      "Ocean conservation and marine biodiversity"
    ]
  },
  {
    category: "Business",
    icon: <TrendingUp className="h-4 w-4" />,
    topics: [
      "Remote work trends and the future of offices",
      "Sustainable business practices and green economy",
      "The rise of e-commerce and digital transformation",
      "Cryptocurrency and the future of finance"
    ]
  }
];

export function TopicInput({ value, onChange }: TopicInputProps) {
  const [customTopic, setCustomTopic] = useState(value);

  const handleTopicSelect = (topic: string) => {
    setCustomTopic(topic);
    onChange(topic);
  };

  const handleCustomTopicChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setCustomTopic(newValue);
    onChange(newValue);
  };

  return (
    <div className="space-y-6">
      {/* Custom Topic Input */}
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <Lightbulb className="h-5 w-5 text-yellow-500" />
          <h3 className="text-lg font-semibold">Enter Your Topic</h3>
        </div>
        <Textarea
          value={customTopic}
          onChange={handleCustomTopicChange}
          placeholder="Describe the topic you'd like to create a podcast about. Be as specific or general as you like..."
          className="min-h-[120px] resize-none"
        />
        <p className="text-sm text-gray-500">
          💡 Tip: The more specific your topic, the more focused and engaging your podcast will be.
        </p>
      </div>

      {/* Suggested Topics */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Sparkles className="h-5 w-5 text-purple-500" />
          <h3 className="text-lg font-semibold">Popular Topics</h3>
        </div>
        
        <div className="grid gap-4">
          {SUGGESTED_TOPICS.map((category) => (
            <Card key={category.category} className="border border-gray-200 hover:border-purple-300 transition-colors">
              <CardContent className="p-4">
                <div className="flex items-center gap-2 mb-3">
                  {category.icon}
                  <h4 className="font-medium text-gray-900">{category.category}</h4>
                </div>
                <div className="grid gap-2">
                  {category.topics.map((topic, index) => (
                    <Button
                      key={index}
                      variant="ghost"
                      className="justify-start h-auto p-3 text-left hover:bg-purple-50 hover:text-purple-700"
                      onClick={() => handleTopicSelect(topic)}
                    >
                      <div className="flex items-start gap-2 w-full">
                        <div className="w-2 h-2 bg-purple-400 rounded-full mt-2 flex-shrink-0" />
                        <span className="text-sm leading-relaxed">{topic}</span>
                      </div>
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Topic Enhancement Suggestions */}
      {customTopic && (
        <Card className="border border-blue-200 bg-blue-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 mb-2">
              <Sparkles className="h-4 w-4 text-blue-500" />
              <h4 className="font-medium text-blue-900">Enhancement Suggestions</h4>
            </div>
            <div className="space-y-2 text-sm text-blue-800">
              <p>• Consider adding specific examples or case studies</p>
              <p>• Think about different perspectives or viewpoints to explore</p>
              <p>• Include recent developments or current events related to your topic</p>
              <p>• Consider your target audience and their level of expertise</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Character Count */}
      {customTopic && (
        <div className="flex justify-between items-center text-sm text-gray-500">
          <span>Characters: {customTopic.length}</span>
          <Badge variant={customTopic.length > 50 ? "default" : "secondary"}>
            {customTopic.length > 50 ? "Good length" : "Consider adding more detail"}
          </Badge>
        </div>
      )}
    </div>
  );
}
