import React, { useState, useCallback } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Upload, 
  BarChart3, 
  MessageSquare, 
  Settings,
  Download,
  Eye,
  Sparkles,
  Database,
  Brain,
  TrendingUp,
  Grid3x3,
  Zap,
  FileText,
  Activity
} from 'lucide-react';
import { toast } from 'sonner';
import { ChartType } from '@antv/gpt-vis';

// Import enhanced components
import { EnhancedFileUploader } from './components/EnhancedFileUploader';
import { DataPreview } from './components/DataPreview';
import { EnhancedAnalysisPipeline } from './components/EnhancedAnalysisPipeline';
import EnhancedNLQueryInterface from './components/EnhancedNLQueryInterface';
import EnhancedVisualizationGallery from './components/EnhancedVisualizationGallery';
import { GPTVisChart } from './components/GPTVisChart';
import { useDataVisualizationStore } from './stores/data-visualization.store';
import { ChartRecommendationService, type ChartRecommendation } from './services/chart-recommendation.service';
import ExportService, { type ExportOptions } from './services/export.service';

interface EnhancedDataVisualizationPlatformProps {
  className?: string;
}

const EnhancedDataVisualizationPlatform: React.FC<EnhancedDataVisualizationPlatformProps> = ({
  className = ''
}) => {
  const [selectedVisualization, setSelectedVisualization] = useState<{
    chartType: ChartType;
    config: any;
    title?: string;
    description?: string;
  } | null>(null);

  const [dataDescription, setDataDescription] = useState<string>('');

  const {
    currentFile,
    currentAnalysis,
    isAnalyzing,
    queries,
    uploadedFiles,
    setCurrentFile,
    activeTab,
    setActiveTab
  } = useDataVisualizationStore();

  // Handle successful file upload
  const handleFileProcessed = useCallback((fileId: string) => {
    console.log('Enhanced: handleFileProcessed called with fileId:', fileId);
    console.log('Enhanced: uploadedFiles:', uploadedFiles);

    // Ensure uploadedFiles is an array before calling find
    const files = Array.isArray(uploadedFiles) ? uploadedFiles : [];
    const file = files.find(f => f.id === fileId);

    console.log('Enhanced: Found file:', file);
    console.log('Enhanced: File status:', file?.status);
    console.log('Enhanced: Current activeTab before:', activeTab);

    if (file && file.status === 'ready') {
      setCurrentFile(file);
      console.log('Enhanced: Setting currentFile and switching to preview tab');
      toast.success(`Successfully loaded ${file.data?.length || 0} rows from ${file.name}`);
      setActiveTab('preview');
      console.log('Enhanced: Called setActiveTab("preview")');
    } else {
      console.log('Enhanced: File not ready or not found. File:', file, 'Status:', file?.status);
    }
  }, [uploadedFiles, setCurrentFile, setActiveTab, activeTab]);

  // Handle analysis completion
  const handleAnalysisComplete = useCallback(() => {
    toast.success('Analysis completed successfully');
    setActiveTab('gallery');
  }, [setActiveTab]);

  // Handle visualization generation from query
  const handleVisualizationGenerated = useCallback((chart: ChartRecommendation) => {
    setSelectedVisualization({
      chartType: chart.chartType,
      config: chart.config,
      title: chart.title,
      description: chart.description
    });
    setActiveTab('visualize');
    toast.success(`Generated ${chart.title}`);
  }, [setActiveTab]);

  // Handle chart selection from gallery
  const handleChartSelect = useCallback((chartType: ChartType, config: any) => {
    if (currentFile) {
      setSelectedVisualization({
        chartType,
        config,
        title: `${chartType} Chart`,
        description: `Visualization of ${currentFile.name}`
      });
      setActiveTab('visualize');
    }
  }, [currentFile]);

  // Handle export
  const handleExport = useCallback(async (format: ExportOptions['format']) => {
    if (!selectedVisualization || !currentFile) {
      toast.error('No visualization selected for export');
      return;
    }

    try {
      const exportData = {
        chartType: selectedVisualization.chartType,
        data: currentFile.data,
        config: selectedVisualization.config,
        title: selectedVisualization.title,
        description: selectedVisualization.description,
        timestamp: new Date()
      };

      const options: ExportOptions = {
        format,
        filename: ExportService.generateFilename(
          `${currentFile.name}-${selectedVisualization.chartType}`,
          format
        ),
        includeData: true,
        includeMetadata: true
      };

      // Validate options
      const errors = ExportService.validateExportOptions(options);
      if (errors.length > 0) {
        toast.error(`Export validation failed: ${errors.join(', ')}`);
        return;
      }

      switch (format) {
        case 'csv':
          await ExportService.exportAsCSV(currentFile.data, options.filename);
          break;
        case 'json':
          await ExportService.exportAsJSON(exportData, options);
          break;
        case 'excel':
          await ExportService.exportAsExcel(currentFile.data, options.filename);
          break;
        case 'png':
        case 'svg':
          const chartElement = document.querySelector('[data-chart-container]') as HTMLElement;
          if (chartElement) {
            await ExportService.exportAsImage(chartElement, options);
          } else {
            toast.error('Chart element not found for image export');
          }
          break;
        case 'pdf':
          const pdfChartElement = document.querySelector('[data-chart-container]') as HTMLElement;
          if (pdfChartElement) {
            await ExportService.exportAsPDF(exportData, pdfChartElement, options);
          } else {
            toast.error('Chart element not found for PDF export');
          }
          break;
        default:
          toast.error(`Unsupported export format: ${format}`);
      }
    } catch (error) {
      console.error('Export error:', error);
      toast.error(`Export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }, [selectedVisualization, currentFile]);

  // Copy to clipboard
  const handleCopyToClipboard = useCallback(async () => {
    const chartElement = document.querySelector('[data-chart-container]') as HTMLElement;
    if (chartElement) {
      await ExportService.copyToClipboard(chartElement);
    } else {
      toast.error('Chart element not found');
    }
  }, [setActiveTab]);

  // Get current data for components
  const currentData = currentFile?.data || [];

  // Add immediate effect to test if component is working
  React.useEffect(() => {
    console.log('EnhancedDataVisualizationPlatform mounted');
    console.log('Current activeTab:', activeTab);
    console.log('setActiveTab function:', setActiveTab);
  }, [activeTab, setActiveTab]);

  return (
    <div
      className={`min-h-screen bg-gradient-to-br from-slate-50 to-blue-50/30 ${className}`}
      style={{ pointerEvents: 'auto' }}
      onClick={() => console.log('Container clicked')}
    >
      <div className="container mx-auto px-4 py-8" style={{ pointerEvents: 'auto' }}>
        {/* Emergency Test Button */}
        <div
          onClick={() => alert('EMERGENCY BUTTON WORKS!')}
          className="fixed top-4 right-4 bg-red-600 text-white p-4 rounded cursor-pointer z-[9999]"
          style={{ pointerEvents: 'auto', zIndex: 9999 }}
        >
          EMERGENCY TEST
        </div>
        {/* Simple Tab Test */}
        <div className="mb-4 p-4 bg-red-100 border border-red-300 rounded relative z-50">
          <h3 className="font-bold text-red-800">DEBUG: Simple Tab Test</h3>
          <p>Current activeTab from store: <strong>{activeTab}</strong></p>
          <div className="flex gap-2 mt-2">
            <button
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('Direct button click - setting to preview');
                setActiveTab('preview');
                alert('Preview button clicked!');
              }}
              className="px-3 py-1 bg-blue-500 text-white rounded cursor-pointer hover:bg-blue-600 relative z-10"
              style={{ pointerEvents: 'auto' }}
            >
              Set Preview
            </button>
            <button
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('Direct button click - setting to analyze');
                setActiveTab('analyze');
                alert('Analyze button clicked!');
              }}
              className="px-3 py-1 bg-green-500 text-white rounded cursor-pointer hover:bg-green-600 relative z-10"
              style={{ pointerEvents: 'auto' }}
            >
              Set Analyze
            </button>
          </div>
        </div>
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <div className="p-3 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl">
              <BarChart3 className="h-8 w-8 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Enhanced Data Visualization Platform
              </h1>
              <p className="text-gray-600 mt-1">
                Transform your data into beautiful insights with AI-powered analysis and 20+ chart types
              </p>
            </div>
          </div>

          {/* Status Bar */}
          {currentFile && (
            <Card className="bg-white/50 backdrop-blur-sm border-0 shadow-lg">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                      <Database className="h-3 w-3 mr-1" />
                      {currentFile.name}
                    </Badge>
                    <span className="text-sm text-gray-600">
                      {currentFile.data?.length?.toLocaleString() || 0} rows • {currentFile.headers?.length || 0} columns
                    </span>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    {currentAnalysis && (
                      <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                        <Brain className="h-3 w-3 mr-1" />
                        Analyzed
                      </Badge>
                    )}
                    {currentAnalysis?.visualizations && currentAnalysis.visualizations.length > 0 && (
                      <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
                        <TrendingUp className="h-3 w-3 mr-1" />
                        {currentAnalysis.visualizations.length} Charts
                      </Badge>
                    )}
                    {queries && queries.length > 0 && (
                      <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
                        <MessageSquare className="h-3 w-3 mr-1" />
                        {queries.length} Queries
                      </Badge>
                    )}
                    {selectedVisualization && (
                      <Badge variant="outline" className="bg-indigo-50 text-indigo-700 border-indigo-200">
                        <Sparkles className="h-3 w-3 mr-1" />
                        {selectedVisualization.chartType}
                      </Badge>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Simple Tabs Test */}
        <div className="mb-4 p-4 bg-yellow-100 border border-yellow-300 rounded">
          <h3 className="font-bold text-yellow-800">DEBUG: Minimal Tabs Test</h3>
          <Tabs defaultValue="upload" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="upload">Upload</TabsTrigger>
              <TabsTrigger value="preview">Preview</TabsTrigger>
              <TabsTrigger value="analyze">Analyze</TabsTrigger>
            </TabsList>
            <TabsContent value="upload">Upload content</TabsContent>
            <TabsContent value="preview">Preview content</TabsContent>
            <TabsContent value="analyze">Analyze content</TabsContent>
          </Tabs>
        </div>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={(value) => {
          console.log('Enhanced: Tab change requested:', value);
          console.log('Enhanced: Current activeTab:', activeTab);
          setActiveTab(value);
          console.log('Enhanced: setActiveTab called with:', value);
        }} className="space-y-6">
          <TabsList className="grid w-full grid-cols-6 bg-white/50 backdrop-blur-sm">
            <TabsTrigger value="upload" className="flex items-center gap-2">
              <Upload className="h-4 w-4" />
              Upload
            </TabsTrigger>
            <TabsTrigger
              value="preview"
              className="flex items-center gap-2 cursor-pointer"
              onClick={() => console.log('Preview tab clicked directly')}
            >
              <Eye className="h-4 w-4" />
              Preview
            </TabsTrigger>
            <TabsTrigger value="analyze" className="flex items-center gap-2">
              <Brain className="h-4 w-4" />
              Analyze
            </TabsTrigger>
            <TabsTrigger value="gallery" className="flex items-center gap-2">
              <Grid3x3 className="h-4 w-4" />
              Gallery
            </TabsTrigger>
            <TabsTrigger value="query" className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              Query
            </TabsTrigger>
            <TabsTrigger value="visualize" className="flex items-center gap-2">
              <Zap className="h-4 w-4" />
              Visualize
            </TabsTrigger>
          </TabsList>

          {/* Upload Tab */}
          <TabsContent value="upload">
            <EnhancedFileUploader onFileProcessed={handleFileProcessed} />

            {/* Debug Info */}
            <div className="mt-4 p-3 bg-gray-100 rounded-lg text-xs">
              <p><strong>Enhanced Debug:</strong></p>
              <p>Uploaded Files: {uploadedFiles?.length || 0}</p>
              <p>Current File: {currentFile ? currentFile.name : 'None'}</p>
              <p>Current File Status: {currentFile ? currentFile.status : 'N/A'}</p>
              <p>Active Tab: {activeTab}</p>
              <p>All Tabs: Always Enabled</p>
              <p>Current File Data Length: {currentFile?.data?.length || 0}</p>
            </div>
          </TabsContent>

          {/* Preview Tab */}
          <TabsContent value="preview">
            {currentFile ? (
              <DataPreview
                file={currentFile}
              />
            ) : (
              <Card>
                <CardContent className="p-8 text-center">
                  <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No data loaded</h3>
                  <p className="text-gray-500">Upload a file to preview your data</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Analysis Tab */}
          <TabsContent value="analyze">
            {currentFile ? (
              <EnhancedAnalysisPipeline
                file={currentFile}
                onAnalysisComplete={handleAnalysisComplete}
              />
            ) : (
              <Card>
                <CardContent className="p-8 text-center">
                  <Brain className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No data to analyze</h3>
                  <p className="text-gray-500">Upload and preview your data first</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Gallery Tab */}
          <TabsContent value="gallery">
            {currentData.length > 0 ? (
              <EnhancedVisualizationGallery 
                data={currentData}
                onChartSelect={handleChartSelect}
              />
            ) : (
              <Card>
                <CardContent className="p-8 text-center">
                  <Grid3x3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No data available</h3>
                  <p className="text-gray-500">Upload your data to explore visualization options</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Query Tab */}
          <TabsContent value="query">
            {currentData.length > 0 && currentFile ? (
              <EnhancedNLQueryInterface
                file={currentFile}
                data={currentData}
                dataDescription={dataDescription || ''}
                onVisualizationGenerated={handleVisualizationGenerated}
              />
            ) : (
              <Card>
                <CardContent className="p-8 text-center">
                  <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No data to query</h3>
                  <p className="text-gray-500">Upload your data to start asking questions</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Visualize Tab */}
          <TabsContent value="visualize">
            {selectedVisualization && currentData.length > 0 ? (
              <div className="space-y-6">
                {/* Export Controls */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span className="flex items-center gap-2">
                        <Sparkles className="h-5 w-5" />
                        {selectedVisualization.title}
                      </span>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleCopyToClipboard}
                        >
                          <FileText className="h-4 w-4 mr-1" />
                          Copy
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleExport('png')}
                        >
                          <Download className="h-4 w-4 mr-1" />
                          PNG
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleExport('svg')}
                        >
                          <Download className="h-4 w-4 mr-1" />
                          SVG
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleExport('csv')}
                        >
                          <Download className="h-4 w-4 mr-1" />
                          CSV
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleExport('pdf')}
                        >
                          <Download className="h-4 w-4 mr-1" />
                          PDF
                        </Button>
                      </div>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div data-chart-container>
                      <GPTVisChart
                        chartType={selectedVisualization.chartType}
                        data={currentData}
                        config={selectedVisualization.config}
                        title={selectedVisualization.title}
                        description={selectedVisualization.description}
                      />
                    </div>
                  </CardContent>
                </Card>
              </div>
            ) : (
              <Card>
                <CardContent className="p-8 text-center">
                  <Zap className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No visualization selected</h3>
                  <p className="text-gray-500">Select a chart from the gallery or generate one with a query</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default EnhancedDataVisualizationPlatform;
