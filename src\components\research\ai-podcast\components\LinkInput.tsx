import React, { useState, useEffect } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Link, 
  ExternalLink, 
  CheckCircle, 
  AlertCircle, 
  Loader2,
  Globe,
  FileText,
  Video,
  Image
} from "lucide-react";

interface LinkInputProps {
  value: string;
  onChange: (content: string, metadata?: any) => void;
}

interface LinkPreview {
  title?: string;
  description?: string;
  image?: string;
  type?: 'article' | 'video' | 'pdf' | 'unknown';
  domain?: string;
  isValid: boolean;
  error?: string;
}

const EXAMPLE_LINKS = [
  {
    url: "https://arxiv.org/abs/2301.00001",
    title: "Research Paper from arXiv",
    description: "Academic papers and research articles"
  },
  {
    url: "https://medium.com/@author/article",
    title: "Medium Article",
    description: "Blog posts and thought leadership pieces"
  },
  {
    url: "https://www.youtube.com/watch?v=example",
    title: "YouTube Video",
    description: "Educational and informational videos"
  },
  {
    url: "https://news.ycombinator.com/item?id=123456",
    title: "Hacker News Discussion",
    description: "Tech news and community discussions"
  }
];

export function LinkInput({ value, onChange }: LinkInputProps) {
  const [url, setUrl] = useState(value);
  const [isValidating, setIsValidating] = useState(false);
  const [preview, setPreview] = useState<LinkPreview | null>(null);

  const validateUrl = (urlString: string): boolean => {
    try {
      const url = new URL(urlString);
      return ['http:', 'https:'].includes(url.protocol);
    } catch {
      return false;
    }
  };

  const getContentTypeIcon = (type?: string) => {
    switch (type) {
      case 'article':
        return <FileText className="h-4 w-4" />;
      case 'video':
        return <Video className="h-4 w-4" />;
      case 'pdf':
        return <FileText className="h-4 w-4" />;
      default:
        return <Globe className="h-4 w-4" />;
    }
  };

  const fetchLinkPreview = async (urlString: string) => {
    setIsValidating(true);
    
    try {
      // Simulate link preview fetching
      // In a real implementation, this would call an API to fetch metadata
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const domain = new URL(urlString).hostname;
      const mockPreview: LinkPreview = {
        title: `Content from ${domain}`,
        description: "This content will be analyzed and converted into a podcast discussion.",
        domain,
        type: domain.includes('youtube') ? 'video' : 
              domain.includes('arxiv') || urlString.includes('.pdf') ? 'pdf' : 'article',
        isValid: true
      };
      
      setPreview(mockPreview);
      onChange(urlString, mockPreview);
    } catch (error) {
      setPreview({
        isValid: false,
        error: "Unable to fetch content from this URL"
      });
    } finally {
      setIsValidating(false);
    }
  };

  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newUrl = e.target.value;
    setUrl(newUrl);
    setPreview(null);
    
    if (validateUrl(newUrl)) {
      fetchLinkPreview(newUrl);
    } else if (newUrl) {
      setPreview({
        isValid: false,
        error: "Please enter a valid URL starting with http:// or https://"
      });
    }
  };

  const handleExampleClick = (exampleUrl: string) => {
    setUrl(exampleUrl);
    fetchLinkPreview(exampleUrl);
  };

  return (
    <div className="space-y-6">
      {/* URL Input */}
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <Link className="h-5 w-5 text-blue-500" />
          <h3 className="text-lg font-semibold">Enter URL</h3>
        </div>
        
        <div className="relative">
          <Input
            type="url"
            value={url}
            onChange={handleUrlChange}
            placeholder="https://example.com/article"
            className="pr-10"
          />
          {isValidating && (
            <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin text-gray-400" />
          )}
          {preview?.isValid && !isValidating && (
            <CheckCircle className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500" />
          )}
          {preview && !preview.isValid && !isValidating && (
            <AlertCircle className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-red-500" />
          )}
        </div>

        <p className="text-sm text-gray-500">
          📎 Supported: Articles, blog posts, research papers, YouTube videos, and more
        </p>
      </div>

      {/* Link Preview */}
      {preview && (
        <Card className={`border ${preview.isValid ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
          <CardContent className="p-4">
            {preview.isValid ? (
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  {getContentTypeIcon(preview.type)}
                  <h4 className="font-medium text-green-900">
                    {preview.title || 'Content Preview'}
                  </h4>
                  <Badge variant="secondary" className="text-xs">
                    {preview.type || 'webpage'}
                  </Badge>
                </div>
                {preview.description && (
                  <p className="text-sm text-green-800">{preview.description}</p>
                )}
                {preview.domain && (
                  <div className="flex items-center gap-2 text-sm text-green-700">
                    <Globe className="h-3 w-3" />
                    <span>{preview.domain}</span>
                    <ExternalLink className="h-3 w-3" />
                  </div>
                )}
              </div>
            ) : (
              <Alert className="border-0 bg-transparent p-0">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription className="text-red-800">
                  {preview.error}
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      )}

      {/* Example Links */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <ExternalLink className="h-5 w-5 text-purple-500" />
          <h3 className="text-lg font-semibold">Example Links</h3>
        </div>
        
        <div className="grid gap-3">
          {EXAMPLE_LINKS.map((example, index) => (
            <Card key={index} className="border border-gray-200 hover:border-purple-300 transition-colors cursor-pointer">
              <CardContent className="p-4">
                <Button
                  variant="ghost"
                  className="w-full justify-start h-auto p-0 hover:bg-transparent"
                  onClick={() => handleExampleClick(example.url)}
                >
                  <div className="text-left space-y-1">
                    <div className="flex items-center gap-2">
                      <ExternalLink className="h-4 w-4 text-blue-500" />
                      <span className="font-medium text-blue-600 hover:text-blue-800">
                        {example.title}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600">{example.description}</p>
                    <p className="text-xs text-gray-400 font-mono">{example.url}</p>
                  </div>
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Tips */}
      <Card className="border border-blue-200 bg-blue-50">
        <CardContent className="p-4">
          <h4 className="font-medium text-blue-900 mb-2">💡 Tips for Better Results</h4>
          <div className="space-y-1 text-sm text-blue-800">
            <p>• Articles with clear structure work best</p>
            <p>• Research papers and academic content are excellent sources</p>
            <p>• YouTube videos will be analyzed based on their description and title</p>
            <p>• Ensure the content is publicly accessible</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
