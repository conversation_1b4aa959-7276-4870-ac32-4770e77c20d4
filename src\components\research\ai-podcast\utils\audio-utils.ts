/**
 * Utility functions for audio processing and manipulation
 */

export class AudioUtils {
  /**
   * Format duration from seconds to MM:SS format
   */
  static formatDuration(seconds: number): string {
    if (isNaN(seconds) || seconds < 0) return '0:00';
    
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  }

  /**
   * Format duration to human readable format (e.g., "5 minutes 30 seconds")
   */
  static formatDurationLong(seconds: number): string {
    if (isNaN(seconds) || seconds < 0) return '0 seconds';
    
    const hours = Math.floor(seconds / 3600);
    const mins = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    const parts: string[] = [];
    if (hours > 0) parts.push(`${hours} hour${hours !== 1 ? 's' : ''}`);
    if (mins > 0) parts.push(`${mins} minute${mins !== 1 ? 's' : ''}`);
    if (secs > 0 || parts.length === 0) parts.push(`${secs} second${secs !== 1 ? 's' : ''}`);
    
    return parts.join(' ');
  }

  /**
   * Parse duration string (MM:SS) to seconds
   */
  static parseDuration(durationString: string): number {
    const parts = durationString.split(':').map(part => parseInt(part, 10));
    
    if (parts.length === 2) {
      const [mins, secs] = parts;
      return (mins * 60) + secs;
    } else if (parts.length === 3) {
      const [hours, mins, secs] = parts;
      return (hours * 3600) + (mins * 60) + secs;
    }
    
    return 0;
  }

  /**
   * Calculate file size from duration and quality
   */
  static estimateFileSize(durationSeconds: number, quality: 'standard' | 'high'): number {
    // Rough estimates for MP3 files
    const bitrates = {
      standard: 128, // kbps
      high: 320     // kbps
    };
    
    const bitrate = bitrates[quality];
    const sizeInKB = (durationSeconds * bitrate) / 8;
    return Math.round(sizeInKB * 1024); // Return bytes
  }

  /**
   * Format file size to human readable format
   */
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
  }

  /**
   * Validate audio file
   */
  static validateAudioFile(file: File): { valid: boolean; error?: string } {
    const allowedTypes = ['audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/ogg'];
    const maxSize = 100 * 1024 * 1024; // 100MB
    
    if (!allowedTypes.includes(file.type)) {
      return {
        valid: false,
        error: 'Invalid file type. Please upload MP3, WAV, or OGG files.'
      };
    }
    
    if (file.size > maxSize) {
      return {
        valid: false,
        error: `File too large. Maximum size is ${this.formatFileSize(maxSize)}.`
      };
    }
    
    return { valid: true };
  }

  /**
   * Create audio blob from base64 data
   */
  static createAudioBlob(base64Data: string, mimeType: string = 'audio/mpeg'): Blob {
    const byteCharacters = atob(base64Data);
    const byteNumbers = new Array(byteCharacters.length);
    
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    
    const byteArray = new Uint8Array(byteNumbers);
    return new Blob([byteArray], { type: mimeType });
  }

  /**
   * Download audio file
   */
  static downloadAudio(audioUrl: string, filename: string): void {
    const link = document.createElement('a');
    link.href = audioUrl;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  /**
   * Get audio metadata from file
   */
  static async getAudioMetadata(file: File): Promise<{
    duration?: number;
    bitrate?: number;
    sampleRate?: number;
  }> {
    return new Promise((resolve) => {
      const audio = new Audio();
      const url = URL.createObjectURL(file);
      
      audio.addEventListener('loadedmetadata', () => {
        const metadata = {
          duration: audio.duration,
          // Note: bitrate and sampleRate are not directly available from HTML5 Audio API
          // In a real implementation, you might use a library like music-metadata
        };
        
        URL.revokeObjectURL(url);
        resolve(metadata);
      });
      
      audio.addEventListener('error', () => {
        URL.revokeObjectURL(url);
        resolve({});
      });
      
      audio.src = url;
    });
  }

  /**
   * Create waveform data from audio file
   */
  static async createWaveform(audioBuffer: AudioBuffer, samples: number = 100): Promise<number[]> {
    const channelData = audioBuffer.getChannelData(0);
    const blockSize = Math.floor(channelData.length / samples);
    const waveform: number[] = [];
    
    for (let i = 0; i < samples; i++) {
      const start = i * blockSize;
      const end = start + blockSize;
      let sum = 0;
      
      for (let j = start; j < end; j++) {
        sum += Math.abs(channelData[j]);
      }
      
      waveform.push(sum / blockSize);
    }
    
    return waveform;
  }

  /**
   * Normalize waveform data
   */
  static normalizeWaveform(waveform: number[]): number[] {
    const max = Math.max(...waveform);
    if (max === 0) return waveform;
    
    return waveform.map(value => value / max);
  }

  /**
   * Check if browser supports audio playback
   */
  static checkAudioSupport(): {
    mp3: boolean;
    wav: boolean;
    ogg: boolean;
  } {
    const audio = new Audio();
    
    return {
      mp3: audio.canPlayType('audio/mpeg') !== '',
      wav: audio.canPlayType('audio/wav') !== '',
      ogg: audio.canPlayType('audio/ogg') !== ''
    };
  }

  /**
   * Get optimal audio format for current browser
   */
  static getOptimalFormat(): string {
    const support = this.checkAudioSupport();
    
    if (support.mp3) return 'mp3';
    if (support.wav) return 'wav';
    if (support.ogg) return 'ogg';
    
    return 'mp3'; // Fallback
  }
}
