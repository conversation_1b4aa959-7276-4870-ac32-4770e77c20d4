import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { X, ExternalLink, Copy, Check } from 'lucide-react';
import { toast } from 'sonner';

interface SearchResult {
  title: string;
  content: string;
  url: string;
  score?: number;
}

interface SearchResultsPopupProps {
  isVisible: boolean;
  onClose: () => void;
  searchResults: SearchResult[];
  searchQuery: string;
  aiResponse?: string;
  className?: string;
}

export function SearchResultsPopup({
  isVisible,
  onClose,
  searchResults,
  searchQuery,
  aiResponse,
  className = ''
}: SearchResultsPopupProps) {
  const [copiedIndex, setCopiedIndex] = React.useState<number | null>(null);

  const handleCopyUrl = async (url: string, index: number) => {
    try {
      await navigator.clipboard.writeText(url);
      setCopiedIndex(index);
      toast.success('URL copied to clipboard');
      setTimeout(() => setCopiedIndex(null), 2000);
    } catch (error) {
      toast.error('Failed to copy URL');
    }
  };

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 z-[70] flex">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/20 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Popup positioned on left side */}
      <Card className={`relative w-96 h-full bg-white shadow-2xl border-r ${className}`}>
        <CardHeader className="pb-3 border-b">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-semibold flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
              Search Results
            </CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          {searchQuery && (
            <div className="text-sm text-gray-600">
              Query: <span className="font-medium">"{searchQuery}"</span>
            </div>
          )}
        </CardHeader>

        <CardContent className="flex-1 flex flex-col p-0">
          {/* Search Results Only - No AI Response */}
          <div className="flex-1 flex flex-col">
            <div className="p-4 border-b">
              <div className="flex items-center justify-between">
                <div className="text-sm font-medium text-gray-700">
                  Sources Found
                </div>
                <Badge variant="secondary" className="text-xs">
                  {searchResults.length} results
                </Badge>
              </div>
            </div>

            <ScrollArea className="flex-1">
              <div className="p-4 space-y-4">
                {searchResults.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <div className="text-sm">No search results found</div>
                  </div>
                ) : (
                  searchResults.map((result, index) => (
                    <Card key={index} className="p-4 hover:bg-gray-50 transition-colors border border-gray-200">
                      <div className="space-y-3">
                        {/* Title and Score */}
                        <div className="flex items-start justify-between">
                          <h4 className="font-medium text-sm line-clamp-2 flex-1">
                            {result.title}
                          </h4>
                          {result.score && (
                            <Badge variant="outline" className="text-xs ml-2 flex-shrink-0">
                              {Math.round(result.score * 100)}%
                            </Badge>
                          )}
                        </div>

                        {/* Content Preview */}
                        <p className="text-xs text-gray-600 line-clamp-3">
                          {result.content}
                        </p>

                        {/* URL and Actions */}
                        <div className="flex items-center justify-between pt-2 border-t border-gray-100">
                          <div className="text-xs text-gray-500 truncate flex-1 mr-2">
                            {new URL(result.url).hostname}
                          </div>
                          <div className="flex gap-1 flex-shrink-0">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => window.open(result.url, '_blank')}
                              className="h-6 w-6 p-0"
                              title="Open in new tab"
                            >
                              <ExternalLink className="h-3 w-3" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleCopyUrl(result.url, index)}
                              className="h-6 w-6 p-0"
                              title="Copy URL"
                            >
                              {copiedIndex === index ? (
                                <Check className="h-3 w-3 text-green-600" />
                              ) : (
                                <Copy className="h-3 w-3" />
                              )}
                            </Button>
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))
                )}
              </div>
            </ScrollArea>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
