import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert";
import { 
  FileText, 
  Copy, 
  RotateCcw, 
  CheckCircle, 
  AlertTriangle,
  BookOpen,
  Clock,
  BarChart3
} from "lucide-react";

interface TextInputProps {
  value: string;
  onChange: (content: string) => void;
}

const SAMPLE_TEXTS = [
  {
    title: "Research Article Abstract",
    content: `Artificial Intelligence (AI) has emerged as a transformative technology across various industries, fundamentally changing how we approach problem-solving and decision-making. This comprehensive analysis explores the current state of AI implementation in healthcare, finance, and education sectors. Our research indicates that while AI adoption has accelerated significantly over the past five years, several challenges remain, including ethical considerations, data privacy concerns, and the need for regulatory frameworks. The study reveals that organizations implementing AI solutions report an average efficiency improvement of 35%, though success rates vary considerably based on implementation strategies and organizational readiness. Key findings suggest that successful AI integration requires not only technological infrastructure but also cultural adaptation and workforce training. As we look toward the future, the convergence of AI with other emerging technologies such as quantum computing and edge computing promises to unlock new possibilities while presenting novel challenges that require careful consideration and strategic planning.`
  },
  {
    title: "Business Case Study",
    content: `The digital transformation of traditional retail has accelerated dramatically in recent years, driven by changing consumer behaviors and technological advancements. This case study examines how a century-old department store chain successfully pivoted to an omnichannel approach, integrating online and offline experiences to meet evolving customer expectations. The transformation journey began with a comprehensive analysis of customer data, revealing that 78% of their customers researched products online before making in-store purchases. The company invested heavily in developing a unified customer experience platform, implementing advanced analytics to personalize recommendations, and training staff to become digital ambassadors. The results were remarkable: online sales increased by 150% within two years, while in-store sales remained stable, indicating successful channel integration rather than cannibalization. Customer satisfaction scores improved by 40%, and the company now serves as a model for traditional retailers navigating digital transformation. Key lessons learned include the importance of employee buy-in, the need for robust data infrastructure, and the critical role of leadership commitment in driving organizational change.`
  },
  {
    title: "Scientific Discovery",
    content: `Recent breakthroughs in quantum computing have brought us closer to achieving quantum supremacy in practical applications. Researchers at leading institutions have developed new quantum algorithms that could revolutionize cryptography, drug discovery, and financial modeling. The latest quantum processors, featuring over 1000 qubits, demonstrate unprecedented stability and coherence times, addressing one of the fundamental challenges in quantum computing. These advances have significant implications for cybersecurity, as current encryption methods may become vulnerable to quantum attacks within the next decade. However, the same technology promises to enable quantum-resistant encryption protocols that could provide unprecedented security. In the pharmaceutical industry, quantum simulations of molecular interactions are already accelerating drug discovery processes, potentially reducing development timelines from decades to years. Financial institutions are exploring quantum algorithms for portfolio optimization and risk assessment, which could provide competitive advantages in high-frequency trading and investment strategies. Despite these promising developments, significant challenges remain, including the need for specialized infrastructure, the scarcity of quantum programming expertise, and the high costs associated with quantum hardware maintenance and operation.`
  }
];

export function TextInput({ value, onChange }: TextInputProps) {
  const [text, setText] = useState(value);
  const [wordCount, setWordCount] = useState(0);
  const [readingTime, setReadingTime] = useState(0);
  const [characterCount, setCharacterCount] = useState(0);

  useEffect(() => {
    const words = text.trim().split(/\s+/).filter(word => word.length > 0);
    const chars = text.length;
    const reading = Math.ceil(words.length / 200); // Average reading speed: 200 words per minute
    
    setWordCount(words.length);
    setCharacterCount(chars);
    setReadingTime(reading);
  }, [text]);

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newText = e.target.value;
    setText(newText);
    onChange(newText);
  };

  const handleSampleSelect = (sampleText: string) => {
    setText(sampleText);
    onChange(sampleText);
  };

  const handleClear = () => {
    setText('');
    onChange('');
  };

  const handleCopyFromClipboard = async () => {
    try {
      const clipboardText = await navigator.clipboard.readText();
      setText(clipboardText);
      onChange(clipboardText);
    } catch (error) {
      console.error('Failed to read clipboard:', error);
    }
  };

  const getContentQuality = () => {
    if (wordCount < 50) return { level: 'low', message: 'Consider adding more content for a richer podcast' };
    if (wordCount < 200) return { level: 'medium', message: 'Good length for a focused discussion' };
    if (wordCount < 500) return { level: 'good', message: 'Excellent length for detailed analysis' };
    return { level: 'excellent', message: 'Perfect for comprehensive podcast coverage' };
  };

  const quality = getContentQuality();

  return (
    <div className="space-y-6">
      {/* Text Input Area */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-green-500" />
            <h3 className="text-lg font-semibold">Enter Your Content</h3>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCopyFromClipboard}
              className="flex items-center gap-2"
            >
              <Copy className="h-4 w-4" />
              Paste
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleClear}
              className="flex items-center gap-2"
            >
              <RotateCcw className="h-4 w-4" />
              Clear
            </Button>
          </div>
        </div>

        <Textarea
          value={text}
          onChange={handleTextChange}
          placeholder="Paste your article, research paper, blog post, or any long-form content here. The AI will analyze it and create an engaging two-person podcast discussion..."
          className="min-h-[300px] resize-none"
        />

        {/* Content Statistics */}
        <div className="flex flex-wrap gap-4 text-sm text-gray-600">
          <div className="flex items-center gap-1">
            <BarChart3 className="h-4 w-4" />
            <span>{wordCount} words</span>
          </div>
          <div className="flex items-center gap-1">
            <FileText className="h-4 w-4" />
            <span>{characterCount} characters</span>
          </div>
          <div className="flex items-center gap-1">
            <Clock className="h-4 w-4" />
            <span>~{readingTime} min read</span>
          </div>
        </div>

        {/* Content Quality Indicator */}
        {text && (
          <div className="flex items-center gap-2">
            <Badge 
              variant={quality.level === 'excellent' ? 'default' : 
                      quality.level === 'good' ? 'secondary' : 
                      quality.level === 'medium' ? 'outline' : 'destructive'}
              className="flex items-center gap-1"
            >
              {quality.level === 'excellent' || quality.level === 'good' ? (
                <CheckCircle className="h-3 w-3" />
              ) : (
                <AlertTriangle className="h-3 w-3" />
              )}
              {quality.level.charAt(0).toUpperCase() + quality.level.slice(1)} Length
            </Badge>
            <span className="text-sm text-gray-600">{quality.message}</span>
          </div>
        )}
      </div>

      {/* Sample Content */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <BookOpen className="h-5 w-5 text-purple-500" />
          <h3 className="text-lg font-semibold">Sample Content</h3>
        </div>
        
        <div className="grid gap-4">
          {SAMPLE_TEXTS.map((sample, index) => (
            <Card key={index} className="border border-gray-200 hover:border-purple-300 transition-colors cursor-pointer">
              <CardContent className="p-4">
                <Button
                  variant="ghost"
                  className="w-full justify-start h-auto p-0 hover:bg-transparent"
                  onClick={() => handleSampleSelect(sample.content)}
                >
                  <div className="text-left space-y-2 w-full">
                    <h4 className="font-medium text-purple-600 hover:text-purple-800">
                      {sample.title}
                    </h4>
                    <p className="text-sm text-gray-600 line-clamp-3">
                      {sample.content.substring(0, 200)}...
                    </p>
                    <div className="flex items-center gap-4 text-xs text-gray-500">
                      <span>{sample.content.split(' ').length} words</span>
                      <span>~{Math.ceil(sample.content.split(' ').length / 200)} min read</span>
                    </div>
                  </div>
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Content Guidelines */}
      <Card className="border border-blue-200 bg-blue-50">
        <CardContent className="p-4">
          <h4 className="font-medium text-blue-900 mb-2">📝 Content Guidelines</h4>
          <div className="space-y-1 text-sm text-blue-800">
            <p>• Longer content (200+ words) produces more engaging podcasts</p>
            <p>• Well-structured content with clear points works best</p>
            <p>• Technical content will be explained in accessible language</p>
            <p>• Multiple topics will be organized into coherent discussion points</p>
          </div>
        </CardContent>
      </Card>

      {/* Format Tips */}
      {text && text.length > 100 && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>
            Great! Your content is ready for podcast generation. The AI will create an engaging 
            two-person discussion covering the key points and insights from your text.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}
