/**
 * AI Humanizer Store
 * Zustand store for managing humanizer state
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import {
  HumanizerSession,
  SampleText,
  StyleProfile,
  StyleAnalysis,
  ProcessingStatus,
  HumanizerSettings,
  UIState,
  HumanizerError,
  VALIDATION_LIMITS
} from '../types';
import { DEFAULT_HUMANIZER_SETTINGS } from '../constants';

interface HumanizerStore {
  // Current session state
  currentSession: HumanizerSession | null;
  sampleTexts: SampleText[];
  originalContent: string;
  humanizedContent: string;
  styleProfile: StyleProfile | null;
  styleAnalysis: StyleAnalysis | null;
  
  // Processing state
  processingStatus: ProcessingStatus;
  isProcessing: boolean;
  
  // UI state
  uiState: UIState;
  
  // Settings
  settings: HumanizerSettings;
  
  // Error handling
  lastError: HumanizerError | null;
  
  // Saved sessions
  savedSessions: HumanizerSession[];
  
  // Actions for sample texts
  addSampleText: (content: string, label: string) => void;
  removeSampleText: (id: string) => void;
  updateSampleText: (id: string, content: string, label: string) => void;
  clearSampleTexts: () => void;
  
  // Actions for content
  setOriginalContent: (content: string) => void;
  setHumanizedContent: (content: string) => void;
  clearContent: () => void;
  
  // Actions for style analysis
  setStyleAnalysis: (analysis: StyleAnalysis) => void;
  setStyleProfile: (profile: StyleProfile) => void;
  clearStyleData: () => void;
  
  // Actions for processing
  setProcessingStatus: (status: ProcessingStatus) => void;
  startProcessing: () => void;
  stopProcessing: () => void;
  
  // Actions for UI state
  setActiveTab: (tab: UIState['activeTab']) => void;
  toggleSidebar: () => void;
  toggleAnalysisPanel: () => void;
  togglePreview: () => void;
  
  // Actions for settings
  updateSettings: (settings: Partial<HumanizerSettings>) => void;
  resetSettings: () => void;
  
  // Actions for error handling
  setError: (error: HumanizerError) => void;
  clearError: () => void;
  
  // Actions for sessions
  createNewSession: (title?: string) => void;
  saveCurrentSession: () => void;
  loadSession: (sessionId: string) => void;
  deleteSession: (sessionId: string) => void;
  clearAllSessions: () => void;
  
  // Validation helpers
  validateSampleText: (content: string, label: string) => string | null;
  validateContent: (content: string) => string | null;
  canAddSampleText: () => boolean;
  canProcessContent: () => boolean;
}

export const useHumanizerStore = create<HumanizerStore>()(
  persist(
    (set, get) => ({
      // Initial state
      currentSession: null,
      sampleTexts: [],
      originalContent: '',
      humanizedContent: '',
      styleProfile: null,
      styleAnalysis: null,
      
      processingStatus: {
        state: 'idle',
        progress: 0,
        currentStep: ''
      },
      isProcessing: false,
      
      uiState: {
        activeTab: 'samples',
        sidebarCollapsed: false,
        showAnalysisPanel: false,
        showPreview: false
      },
      
      settings: DEFAULT_HUMANIZER_SETTINGS,
      lastError: null,
      savedSessions: [],
      
      // Sample text actions
      addSampleText: (content: string, label: string) => {
        const state = get();
        const validation = state.validateSampleText(content, label);
        
        if (validation) {
          state.setError({
            code: 'VALIDATION_ERROR',
            message: validation,
            timestamp: new Date()
          });
          return;
        }
        
        if (!state.canAddSampleText()) {
          state.setError({
            code: 'MAX_SAMPLES_REACHED',
            message: `Maximum of ${VALIDATION_LIMITS.MAX_SAMPLE_TEXTS} sample texts allowed`,
            timestamp: new Date()
          });
          return;
        }
        
        const newSample: SampleText = {
          id: `sample_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          content: content.trim(),
          label: label.trim(),
          wordCount: content.trim().split(/\s+/).length,
          addedAt: new Date(),
          isAnalyzed: false
        };
        
        set(state => ({
          sampleTexts: [...state.sampleTexts, newSample],
          lastError: null
        }));
      },
      
      removeSampleText: (id: string) => {
        set(state => ({
          sampleTexts: state.sampleTexts.filter(sample => sample.id !== id)
        }));
      },
      
      updateSampleText: (id: string, content: string, label: string) => {
        const state = get();
        const validation = state.validateSampleText(content, label);
        
        if (validation) {
          state.setError({
            code: 'VALIDATION_ERROR',
            message: validation,
            timestamp: new Date()
          });
          return;
        }
        
        set(state => ({
          sampleTexts: state.sampleTexts.map(sample =>
            sample.id === id
              ? {
                  ...sample,
                  content: content.trim(),
                  label: label.trim(),
                  wordCount: content.trim().split(/\s+/).length,
                  isAnalyzed: false
                }
              : sample
          ),
          lastError: null
        }));
      },
      
      clearSampleTexts: () => {
        set({ sampleTexts: [] });
      },
      
      // Content actions
      setOriginalContent: (content: string) => {
        set({ originalContent: content.trim() });
      },
      
      setHumanizedContent: (content: string) => {
        set({ humanizedContent: content.trim() });
      },
      
      clearContent: () => {
        set({ originalContent: '', humanizedContent: '' });
      },
      
      // Style analysis actions
      setStyleAnalysis: (analysis: StyleAnalysis) => {
        set(state => ({
          styleAnalysis: analysis,
          sampleTexts: state.sampleTexts.map(sample => ({
            ...sample,
            isAnalyzed: analysis.sampleTextIds.includes(sample.id)
          }))
        }));
      },
      
      setStyleProfile: (profile: StyleProfile) => {
        set({ styleProfile: profile });
      },
      
      clearStyleData: () => {
        set({ styleProfile: null, styleAnalysis: null });
      },
      
      // Processing actions
      setProcessingStatus: (status: ProcessingStatus) => {
        set({ processingStatus: status });
      },
      
      startProcessing: () => {
        set({
          isProcessing: true,
          processingStatus: {
            state: 'analyzing-samples',
            progress: 0,
            currentStep: 'Starting analysis...'
          },
          lastError: null
        });
      },
      
      stopProcessing: () => {
        set({
          isProcessing: false,
          processingStatus: {
            state: 'idle',
            progress: 0,
            currentStep: ''
          }
        });
      },
      
      // UI actions
      setActiveTab: (tab: UIState['activeTab']) => {
        set(state => ({
          uiState: { ...state.uiState, activeTab: tab }
        }));
      },
      
      toggleSidebar: () => {
        set(state => ({
          uiState: { ...state.uiState, sidebarCollapsed: !state.uiState.sidebarCollapsed }
        }));
      },
      
      toggleAnalysisPanel: () => {
        set(state => ({
          uiState: { ...state.uiState, showAnalysisPanel: !state.uiState.showAnalysisPanel }
        }));
      },
      
      togglePreview: () => {
        set(state => ({
          uiState: { ...state.uiState, showPreview: !state.uiState.showPreview }
        }));
      },
      
      // Settings actions
      updateSettings: (newSettings: Partial<HumanizerSettings>) => {
        set(state => ({
          settings: { ...state.settings, ...newSettings }
        }));
      },
      
      resetSettings: () => {
        set({ settings: DEFAULT_HUMANIZER_SETTINGS });
      },
      
      // Error handling actions
      setError: (error: HumanizerError) => {
        set({ lastError: error });
      },
      
      clearError: () => {
        set({ lastError: null });
      },
      
      // Session actions
      createNewSession: (title?: string) => {
        const newSession: HumanizerSession = {
          id: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          title: title || `Session ${new Date().toLocaleDateString()}`,
          sampleTexts: [],
          originalContent: '',
          humanizedContent: '',
          styleProfile: null,
          createdAt: new Date(),
          updatedAt: new Date(),
          isActive: true
        };
        
        set({
          currentSession: newSession,
          sampleTexts: [],
          originalContent: '',
          humanizedContent: '',
          styleProfile: null,
          styleAnalysis: null,
          lastError: null
        });
      },
      
      saveCurrentSession: () => {
        const state = get();
        if (!state.currentSession) return;
        
        const updatedSession: HumanizerSession = {
          ...state.currentSession,
          sampleTexts: state.sampleTexts,
          originalContent: state.originalContent,
          humanizedContent: state.humanizedContent,
          styleProfile: state.styleProfile,
          updatedAt: new Date(),
          metadata: {
            wordCount: state.originalContent.split(/\s+/).length,
            processingTime: 0,
            modelUsed: state.settings.preferredModel,
            confidence: state.styleAnalysis?.confidence || 0
          }
        };
        
        set(state => ({
          currentSession: updatedSession,
          savedSessions: [
            ...state.savedSessions.filter(s => s.id !== updatedSession.id),
            updatedSession
          ]
        }));
      },
      
      loadSession: (sessionId: string) => {
        const state = get();
        const session = state.savedSessions.find(s => s.id === sessionId);
        
        if (session) {
          set({
            currentSession: session,
            sampleTexts: session.sampleTexts,
            originalContent: session.originalContent,
            humanizedContent: session.humanizedContent,
            styleProfile: session.styleProfile,
            lastError: null
          });
        }
      },
      
      deleteSession: (sessionId: string) => {
        set(state => ({
          savedSessions: state.savedSessions.filter(s => s.id !== sessionId)
        }));
      },
      
      clearAllSessions: () => {
        set({ savedSessions: [] });
      },
      
      // Validation helpers
      validateSampleText: (content: string, label: string) => {
        if (!content || content.trim().length === 0) {
          return 'Sample text cannot be empty';
        }
        
        if (content.length < VALIDATION_LIMITS.MIN_SAMPLE_TEXT_LENGTH) {
          return `Sample text must be at least ${VALIDATION_LIMITS.MIN_SAMPLE_TEXT_LENGTH} characters long`;
        }
        
        if (content.length > VALIDATION_LIMITS.MAX_SAMPLE_TEXT_LENGTH) {
          return `Sample text cannot exceed ${VALIDATION_LIMITS.MAX_SAMPLE_TEXT_LENGTH} characters`;
        }
        
        if (!label || label.trim().length === 0) {
          return 'Sample label cannot be empty';
        }
        
        if (label.length > VALIDATION_LIMITS.MAX_SAMPLE_LABEL_LENGTH) {
          return `Sample label cannot exceed ${VALIDATION_LIMITS.MAX_SAMPLE_LABEL_LENGTH} characters`;
        }
        
        const state = get();
        if (state.sampleTexts.some(sample => sample.label.toLowerCase() === label.toLowerCase())) {
          return 'A sample with this label already exists';
        }
        
        return null;
      },
      
      validateContent: (content: string) => {
        if (!content || content.trim().length === 0) {
          return 'Content cannot be empty';
        }
        
        if (content.length < VALIDATION_LIMITS.MIN_CONTENT_LENGTH) {
          return `Content must be at least ${VALIDATION_LIMITS.MIN_CONTENT_LENGTH} characters long`;
        }
        
        const wordCount = content.trim().split(/\s+/).length;
        if (wordCount > VALIDATION_LIMITS.MAX_CONTENT_LENGTH) {
          return `Content cannot exceed ${VALIDATION_LIMITS.MAX_CONTENT_LENGTH} words`;
        }
        
        return null;
      },
      
      canAddSampleText: () => {
        const state = get();
        return state.sampleTexts.length < VALIDATION_LIMITS.MAX_SAMPLE_TEXTS;
      },
      
      canProcessContent: () => {
        const state = get();
        return (
          state.sampleTexts.length > 0 &&
          state.originalContent.trim().length > 0 &&
          !state.isProcessing
        );
      }
    }),
    {
      name: 'ai-humanizer-store',
      partialize: (state) => ({
        settings: state.settings,
        savedSessions: state.savedSessions,
        uiState: state.uiState
      })
    }
  )
);
