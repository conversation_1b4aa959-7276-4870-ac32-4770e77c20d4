# AI Podcast Generator

A comprehensive AI-powered podcast generation module that creates professional two-person podcasts from various content sources using Google Gemini API and advanced text-to-speech technology.

## Features

### Content Input Methods
- **Topic Input**: Generate podcasts from topic descriptions
- **Link Input**: Extract and process content from URLs
- **File Upload**: Support for PDF, Word, and text files
- **Long Text**: Direct text input for custom content

### Voice & Audio Configuration
- **Multiple Providers**: Fish Audio and Google Gemini TTS
- **Voice Selection**: Choose different voices for each host
- **Language Support**: Multiple languages with proper voice matching
- **Quality Options**: Standard and high-quality audio output

### Podcast Generation
- **AI-Powered Scripts**: Natural conversation generation using Gemini 2.5 Pro
- **Two-Phase Process**: Outline generation followed by script creation
- **Progress Tracking**: Real-time generation progress with detailed stages
- **Audio Synthesis**: High-quality text-to-speech conversion

### Playback & Management
- **Built-in Player**: Full-featured audio player with controls
- **Script Display**: View generated conversation scripts
- **Download Options**: Export podcasts in multiple formats
- **History Management**: Save and organize generated podcasts

## Architecture

### Components
- `AIPodcastGenerator.tsx` - Main component with tabbed interface
- `TopicInput.tsx` - Topic-based podcast generation
- `LinkInput.tsx` - URL content processing
- `FileUploader.tsx` - Multi-format file upload
- `TextInput.tsx` - Long-form text input
- `VoiceSelector.tsx` - Voice and provider configuration
- `PodcastPlayer.tsx` - Audio playback with controls
- `GenerationProgress.tsx` - Real-time progress display
- `PodcastHistory.tsx` - Podcast library management

### Services
- `PodcastGenerationService` - Core podcast generation logic
- `AudioGenerationService` - Text-to-speech processing
- `ContentProcessingService` - Input content processing
- `PodcastStorageService` - Supabase integration for data persistence

### Database Schema
- `ai_podcasts` - Main podcast records
- `ai_podcast_files` - File upload tracking
- Storage bucket for audio files and uploads

## Usage

### Basic Workflow
1. Select input method (Topic, Link, File, or Text)
2. Provide content through chosen method
3. Configure voices for both podcast hosts
4. Select audio provider and language
5. Generate podcast with real-time progress tracking
6. Play, download, or save to history

### Integration
The module is integrated into the Research Dashboard with:
- Navigation item in sidebar
- Full-screen interface
- Consistent UI/UX with other research tools

## API Integration

### Google Gemini API
- Model: `gemini-2.5-pro`
- Used for outline and script generation
- Configured via `VITE_GEMINI_API_KEY`

### Supabase Integration
- User authentication and data persistence
- File storage for uploads and generated audio
- Row-level security for user data isolation

## Configuration

### Environment Variables
```env
VITE_GEMINI_API_KEY=your_gemini_api_key
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### Audio Providers
- **Fish Audio**: Fast generation, multiple voice options
- **Google Gemini**: High-quality synthesis, natural voices

## File Structure
```
src/components/research/ai-podcast/
├── AIPodcastGenerator.tsx          # Main component
├── components/                     # UI components
│   ├── TopicInput.tsx
│   ├── LinkInput.tsx
│   ├── FileUploader.tsx
│   ├── TextInput.tsx
│   ├── VoiceSelector.tsx
│   ├── PodcastPlayer.tsx
│   ├── GenerationProgress.tsx
│   └── PodcastHistory.tsx
├── services/                       # Business logic
│   ├── podcast-generation.service.ts
│   ├── audio-generation.service.ts
│   ├── content-processing.service.ts
│   └── podcast-storage.service.ts
├── utils/                         # Utility functions
│   ├── audio-utils.ts
│   └── podcast-utils.ts
├── database/                      # Database schema
│   └── schema.sql
├── types.ts                       # TypeScript definitions
├── constants.ts                   # Configuration constants
└── index.ts                       # Module exports
```

## Future Enhancements

### Planned Features
- Additional TTS providers (ElevenLabs, Azure)
- Podcast episode series generation
- Advanced voice cloning capabilities
- Multi-language podcast generation
- Podcast RSS feed generation
- Social media integration for sharing

### Technical Improvements
- Real-time audio streaming
- Advanced audio post-processing
- Batch podcast generation
- API rate limiting and queuing
- Enhanced error handling and recovery

## Dependencies

### Core Dependencies
- React 18+ with TypeScript
- Google Generative AI SDK
- Supabase client
- Lucide React icons
- Sonner for notifications

### UI Components
- Radix UI primitives
- Tailwind CSS for styling
- Custom UI component library

## Contributing

When contributing to this module:
1. Follow existing TypeScript patterns
2. Maintain consistent UI/UX with platform
3. Add proper error handling
4. Include comprehensive type definitions
5. Update documentation for new features

## License

Part of the Paper Genius Platform - All rights reserved.
