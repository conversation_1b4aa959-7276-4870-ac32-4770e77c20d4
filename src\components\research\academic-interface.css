/* Academic Interface Styling for Enhanced Writing Editor */

/* Enhanced Floating AI Assistant */
.floating-ai-assistant {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    0 0 0 1px rgba(255, 255, 255, 0.05);
}

.floating-ai-assistant .enhanced-tool-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 2px solid transparent;
  background-clip: padding-box;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.floating-ai-assistant .enhanced-tool-card:hover {
  transform: translateY(-2px);
  box-shadow:
    0 10px 25px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border-color: rgba(59, 130, 246, 0.3);
}

.floating-ai-assistant .tool-icon-gradient {
  background: linear-gradient(135deg, #dbeafe 0%, #e0e7ff 50%, #f3e8ff 100%);
  transition: all 0.3s ease;
}

.floating-ai-assistant .enhanced-tool-card:hover .tool-icon-gradient {
  background: linear-gradient(135deg, #bfdbfe 0%, #c7d2fe 50%, #e9d5ff 100%);
  transform: scale(1.05);
}

/* Enhanced Tab Styling */
.floating-ai-assistant .tab-active {
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-bottom: 2px solid currentColor;
}

/* History Card Styling */
.floating-ai-assistant .history-card {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.floating-ai-assistant .history-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border-color: #cbd5e1;
}

/* Conversation Type Indicators */
.conversation-type-chat { background-color: #3b82f6; }
.conversation-type-tool { background-color: #10b981; }
.conversation-type-search { background-color: #8b5cf6; }

/* Primary AI Assistant Styling */
.primary-ai-assistant {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.primary-ai-assistant:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #cbd5e1;
}

.ai-prompt-input {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  transition: all 0.2s ease;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.ai-prompt-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
}

/* Action Mode Buttons */
.action-mode-button {
  transition: all 0.2s ease;
  border-radius: 6px;
  font-weight: 500;
}

.action-mode-button.selected {
  background: #3b82f6;
  color: white;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.action-mode-button:not(.selected) {
  background: white;
  border: 1px solid #e2e8f0;
  color: #64748b;
}

.action-mode-button:not(.selected):hover {
  background: #f8fafc;
  border-color: #cbd5e1;
  color: #475569;
}

/* Popup Panels Styling */
.popup-panel {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid #e2e8f0;
  backdrop-filter: blur(8px);
}

.popup-panel-header {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid #e2e8f0;
  border-radius: 16px 16px 0 0;
}

.popup-panel-content {
  max-height: calc(85vh - 120px);
  overflow-y: auto;
}

/* Tools Grid Styling */
.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
  padding: 24px;
}

.tool-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.tool-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.tool-card:hover::before {
  transform: scaleX(1);
}

.tool-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #cbd5e1;
}

.tool-card.generation {
  border-left: 4px solid #3b82f6;
}

.tool-card.enhancement {
  border-left: 4px solid #10b981;
}

.tool-card.analysis {
  border-left: 4px solid #8b5cf6;
}

.tool-card.review {
  border-left: 4px solid #f59e0b;
}

/* Academic Search Styling */
.search-panel {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.search-controls {
  background: linear-gradient(135deg, #fafbfc 0%, #f4f6f8 100%);
  border-bottom: 1px solid #e2e8f0;
  padding: 24px;
  border-radius: 16px 16px 0 0;
}

.search-input-group {
  position: relative;
}

.search-input {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 16px;
  transition: all 0.2s ease;
  width: 100%;
}

.search-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
}

.search-button {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 600;
  transition: all 0.2s ease;
  cursor: pointer;
}

.search-button:hover {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.search-button:disabled {
  background: #94a3b8;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Citation Style Selector */
.citation-selector {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 8px 12px;
  transition: all 0.2s ease;
}

.citation-selector:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
}

/* Generated Content Display */
.generated-content {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
  font-family: 'Georgia', 'Times New Roman', serif;
  line-height: 1.7;
  color: #374151;
}

.generated-content p {
  margin-bottom: 16px;
}

.generated-content p:last-child {
  margin-bottom: 0;
}

/* References Box */
.references-box {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border: 1px solid #bfdbfe;
  border-radius: 12px;
  padding: 20px;
  margin-top: 20px;
}

.references-box h4 {
  color: #1e40af;
  font-weight: 600;
  margin-bottom: 16px;
  font-size: 16px;
}

.reference-item {
  background: white;
  border: 1px solid #e0e7ff;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 12px;
  font-size: 14px;
  line-height: 1.5;
  color: #374151;
}

.reference-item:last-child {
  margin-bottom: 0;
}

/* Source Links */
.source-links {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  margin-top: 20px;
}

.source-link-item {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.source-link-item:hover {
  border-color: #cbd5e1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transform: translateY(-1px);
}

.source-link-item:last-child {
  margin-bottom: 0;
}

.source-link-title {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
  font-size: 14px;
}

.source-link-content {
  color: #6b7280;
  font-size: 13px;
  line-height: 1.4;
}

/* Formatting Toolbar Enhancements */
.formatting-toolbar {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 12px 16px;
  transition: all 0.3s ease;
}

.formatting-toolbar.hidden {
  max-height: 0;
  padding: 0 16px;
  overflow: hidden;
  opacity: 0;
}

.formatting-toolbar.visible {
  max-height: 80px;
  opacity: 1;
}

.toolbar-toggle {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 4px 8px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.toolbar-toggle:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

/* Floating AI Assistant */
.floating-ai-assistant {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(12px);
  animation: slideInFromBottom 0.3s ease-out;
  max-width: min(400px, 90vw);
  max-height: min(600px, 85vh);
}

.floating-ai-assistant .ai-prompt-input {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  transition: all 0.2s ease;
  font-size: 14px;
}

.floating-ai-assistant .ai-prompt-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
  background: white;
}

/* Floating Tools Panel */
.floating-tools-panel {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(12px);
  animation: slideInFromCenter 0.3s ease-out;
}

/* Floating AI Toolbar Improvements */
.floating-ai-toolbar {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(8px);
  animation: fadeInUp 0.2s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideInFromCenter {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Loading States */
.ai-loading {
  background: linear-gradient(90deg, #f1f5f9 25%, #e2e8f0 50%, #f1f5f9 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .tools-grid {
    grid-template-columns: 1fr;
    gap: 12px;
    padding: 16px;
  }
  
  .tool-card {
    padding: 16px;
  }
  
  .search-controls {
    padding: 16px;
  }
  
  .generated-content {
    padding: 16px;
    font-size: 14px;
  }
  
  .popup-panel {
    margin: 16px;
    border-radius: 12px;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .primary-ai-assistant {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    border-color: #475569;
  }
  
  .ai-prompt-input {
    background: #1e293b;
    border-color: #475569;
    color: #f1f5f9;
  }
  
  .popup-panel {
    background: #1e293b;
    border-color: #475569;
  }
  
  .generated-content {
    background: #334155;
    border-color: #475569;
    color: #f1f5f9;
  }
}
