import * as XLSX from 'xlsx';
import <PERSON> from 'papa<PERSON><PERSON>';
import { nanoid } from 'nanoid';
import { 
  UploadedFile, 
  FileValidationResult, 
  SupportedFileType,
  SUPPORTED_FILE_TYPES,
  FILE_SIZE_LIMITS 
} from '../types';
import { DATA_VIZ_CONFIG, ERROR_MESSAGES, DATA_TYPE_DETECTION } from '../constants';

export class FileProcessingService {
  /**
   * Validate uploaded file
   */
  static validateFile(file: File): FileValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check file size
    if (file.size > FILE_SIZE_LIMITS.MAX_FILE_SIZE) {
      errors.push(ERROR_MESSAGES.FILE_TOO_LARGE);
    } else if (file.size > FILE_SIZE_LIMITS.WARNING_SIZE) {
      warnings.push(`Large file detected (${this.formatFileSize(file.size)}). Processing may take longer.`);
    }

    // Check file type
    const isSupported = Object.keys(SUPPORTED_FILE_TYPES).includes(file.type) ||
      DATA_VIZ_CONFIG.SUPPORTED_EXTENSIONS.some(ext => file.name.toLowerCase().endsWith(ext));
    
    if (!isSupported) {
      errors.push(ERROR_MESSAGES.UNSUPPORTED_FORMAT);
    }

    // Check if file is empty
    if (file.size === 0) {
      errors.push(ERROR_MESSAGES.EMPTY_FILE);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Process uploaded file and extract data
   */
  static async processFile(file: File): Promise<UploadedFile> {
    const validation = this.validateFile(file);
    
    if (!validation.isValid) {
      throw new Error(validation.errors.join(', '));
    }

    const uploadedFile: UploadedFile = {
      id: nanoid(),
      name: file.name,
      size: file.size,
      type: file.type,
      data: [],
      headers: [],
      uploadedAt: new Date(),
      status: 'processing'
    };

    try {
      const { data, headers } = await this.extractDataFromFile(file);
      
      // Validate extracted data
      if (!data || data.length === 0) {
        throw new Error(ERROR_MESSAGES.EMPTY_FILE);
      }

      if (data.length < DATA_VIZ_CONFIG.MIN_ROWS_FOR_ANALYSIS) {
        throw new Error(ERROR_MESSAGES.INSUFFICIENT_DATA);
      }

      uploadedFile.data = data;
      uploadedFile.headers = headers;
      uploadedFile.status = 'ready';

      return uploadedFile;
    } catch (error) {
      uploadedFile.status = 'error';
      uploadedFile.error = error instanceof Error ? error.message : 'Unknown error occurred';
      throw error;
    }
  }

  /**
   * Extract data from file based on type
   */
  private static async extractDataFromFile(file: File): Promise<{ data: any[][], headers: string[] }> {
    const fileExtension = file.name.toLowerCase().split('.').pop();
    
    if (fileExtension === 'csv' || file.type === 'text/csv') {
      return this.parseCSV(file);
    } else if (fileExtension === 'xlsx' || fileExtension === 'xls' || 
               file.type.includes('spreadsheet') || file.type.includes('excel')) {
      return this.parseExcel(file);
    } else {
      throw new Error(ERROR_MESSAGES.UNSUPPORTED_FORMAT);
    }
  }

  /**
   * Parse CSV file
   */
  private static async parseCSV(file: File): Promise<{ data: any[][], headers: string[] }> {
    return new Promise((resolve, reject) => {
      Papa.parse(file, {
        header: false,
        skipEmptyLines: true,
        dynamicTyping: true,
        complete: (results) => {
          try {
            if (results.errors.length > 0) {
              console.warn('CSV parsing warnings:', results.errors);
            }

            const data = results.data as any[][];
            if (data.length === 0) {
              reject(new Error(ERROR_MESSAGES.EMPTY_FILE));
              return;
            }

            // Extract headers from first row
            const headers = data[0].map((header, index) => 
              header ? String(header).trim() : `Column ${index + 1}`
            );
            
            // Remove header row from data
            const dataRows = data.slice(1);

            resolve({ data: dataRows, headers });
          } catch (error) {
            reject(new Error(ERROR_MESSAGES.INVALID_DATA));
          }
        },
        error: (error) => {
          reject(new Error(`CSV parsing error: ${error.message}`));
        }
      });
    });
  }

  /**
   * Parse Excel file
   */
  private static async parseExcel(file: File): Promise<{ data: any[][], headers: string[] }> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer);
          const workbook = XLSX.read(data, { type: 'array' });
          
          // Get first worksheet
          const firstSheetName = workbook.SheetNames[0];
          if (!firstSheetName) {
            reject(new Error(ERROR_MESSAGES.EMPTY_FILE));
            return;
          }

          const worksheet = workbook.Sheets[firstSheetName];
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
            header: 1, 
            defval: null,
            blankrows: false 
          }) as any[][];

          if (jsonData.length === 0) {
            reject(new Error(ERROR_MESSAGES.EMPTY_FILE));
            return;
          }

          // Extract headers from first row
          const headers = jsonData[0].map((header, index) => 
            header ? String(header).trim() : `Column ${index + 1}`
          );
          
          // Remove header row from data
          const dataRows = jsonData.slice(1);

          resolve({ data: dataRows, headers });
        } catch (error) {
          reject(new Error(ERROR_MESSAGES.INVALID_DATA));
        }
      };

      reader.onerror = () => {
        reject(new Error(ERROR_MESSAGES.INVALID_DATA));
      };

      reader.readAsArrayBuffer(file);
    });
  }

  /**
   * Detect data types for columns
   */
  static detectDataTypes(data: any[][], headers: string[]): Record<string, string> {
    const dataTypes: Record<string, string> = {};

    if (!Array.isArray(data) || !Array.isArray(headers)) {
      return dataTypes;
    }

    headers.forEach((header, columnIndex) => {
      const columnData = data
        .filter(row => Array.isArray(row))
        .map(row => row[columnIndex])
        .filter(val => val != null && val !== '');
      
      if (columnData.length === 0) {
        dataTypes[header] = 'empty';
        return;
      }

      // Check for numeric data
      const numericCount = columnData.filter(val => {
        const str = String(val).trim();
        return DATA_TYPE_DETECTION.patterns.float.test(str) || DATA_TYPE_DETECTION.patterns.integer.test(str);
      }).length;

      const numericRatio = numericCount / columnData.length;

      if (numericRatio >= DATA_TYPE_DETECTION.thresholds.numeric_ratio) {
        // Check if integers vs floats
        const integerCount = columnData.filter(val => {
          const str = String(val).trim();
          return DATA_TYPE_DETECTION.patterns.integer.test(str);
        }).length;
        
        dataTypes[header] = integerCount === numericCount ? 'integer' : 'number';
      } else {
        // Check for dates
        const dateCount = columnData.filter(val => {
          const str = String(val).trim();
          return DATA_TYPE_DETECTION.patterns.date.test(str) || !isNaN(Date.parse(str));
        }).length;

        if (dateCount / columnData.length > 0.5) {
          dataTypes[header] = 'date';
        } else {
          // Check for boolean
          const booleanCount = columnData.filter(val => {
            const str = String(val).trim();
            return DATA_TYPE_DETECTION.patterns.boolean.test(str);
          }).length;

          if (booleanCount / columnData.length > 0.8) {
            dataTypes[header] = 'boolean';
          } else {
            // Default to string/categorical
            const uniqueValues = new Set(columnData).size;
            const uniqueRatio = uniqueValues / columnData.length;
            
            if (uniqueValues <= DATA_TYPE_DETECTION.thresholds.category_max && 
                uniqueRatio < DATA_TYPE_DETECTION.thresholds.unique_ratio) {
              dataTypes[header] = 'categorical';
            } else {
              dataTypes[header] = 'string';
            }
          }
        }
      }
    });

    return dataTypes;
  }

  /**
   * Calculate basic statistics for numeric columns
   */
  static calculateBasicStats(data: any[][], headers: string[], dataTypes: Record<string, string>): Record<string, any> {
    const stats: Record<string, any> = {};

    if (!Array.isArray(data) || !Array.isArray(headers)) {
      return stats;
    }

    headers.forEach((header, columnIndex) => {
      const columnData = data
        .filter(row => Array.isArray(row))
        .map(row => row[columnIndex])
        .filter(val => val != null && val !== '');
      
      if (dataTypes[header] === 'number' || dataTypes[header] === 'integer') {
        const numericData = columnData.map(val => Number(val)).filter(val => !isNaN(val));
        
        if (numericData.length > 0) {
          const sorted = numericData.sort((a, b) => a - b);
          const sum = numericData.reduce((acc, val) => acc + val, 0);
          const mean = sum / numericData.length;
          
          stats[header] = {
            count: numericData.length,
            mean: Number(mean.toFixed(2)),
            median: this.calculateMedian(sorted),
            min: sorted[0],
            max: sorted[sorted.length - 1],
            std: Number(this.calculateStandardDeviation(numericData, mean).toFixed(2)),
            missing: data.length - numericData.length
          };
        }
      } else {
        const uniqueValues = new Set(columnData).size;
        stats[header] = {
          count: columnData.length,
          unique: uniqueValues,
          missing: data.length - columnData.length,
          most_frequent: this.getMostFrequent(columnData)
        };
      }
    });

    return stats;
  }

  /**
   * Helper methods
   */
  private static calculateMedian(sortedArray: number[]): number {
    const mid = Math.floor(sortedArray.length / 2);
    return sortedArray.length % 2 !== 0 
      ? sortedArray[mid] 
      : (sortedArray[mid - 1] + sortedArray[mid]) / 2;
  }

  private static calculateStandardDeviation(data: number[], mean: number): number {
    const variance = data.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / data.length;
    return Math.sqrt(variance);
  }

  private static getMostFrequent(data: any[]): any {
    const frequency: Record<string, number> = {};
    data.forEach(item => {
      const key = String(item);
      frequency[key] = (frequency[key] || 0) + 1;
    });
    
    return Object.keys(frequency).reduce((a, b) => frequency[a] > frequency[b] ? a : b);
  }

  private static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
