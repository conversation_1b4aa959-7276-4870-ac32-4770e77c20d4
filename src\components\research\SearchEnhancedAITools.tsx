import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import {
  Search,
  Lightbulb,
  BookOpen,
  FileText,
  Users,
  ChevronDown,
  ChevronUp,
  Loader2,
  Sparkles,
  Quote,
  Link
} from 'lucide-react';
import { toast } from 'sonner';
import { tavilySearchService } from './research-search/services/tavily-search.service';
import { enhancedAIService } from './paper-generator/enhanced-ai.service';

interface SearchEnhancedAIToolsProps {
  selectedText: string;
  documentContent: string;
  onAIRequest: (prompt: string, text: string, mode: 'replace' | 'insert' | 'display') => void;
  aiLoading: boolean;
}

interface SearchTool {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  searchQuery: (text: string, context: string) => string;
  aiPrompt: (text: string, searchResults: string, context: string) => string;
}

const searchTools: SearchTool[] = [
  {
    id: 'idea-generation',
    name: 'Idea Generation',
    description: 'Generate research ideas and brainstorm topics',
    icon: <Lightbulb className="h-4 w-4" />,
    color: 'yellow',
    searchQuery: (text, context) => `research ideas ${text} academic brainstorming`,
    aiPrompt: (text, searchResults, context) => `
Based on the following research findings and the selected text, generate creative and innovative research ideas:

Selected text: "${text}"

Research findings:
${searchResults}

Document context: ${context.slice(0, 500)}...

Please provide:
1. 3-5 novel research ideas related to the topic
2. Potential research questions
3. Suggested methodological approaches
4. Gaps in current research that could be explored

Format as a well-structured academic response.`
  },
  {
    id: 'introduction-enhancement',
    name: 'Introduction Enhancement',
    description: 'Enhance introductions with current research',
    icon: <FileText className="h-4 w-4" />,
    color: 'blue',
    searchQuery: (text, context) => `${text} introduction academic research background`,
    aiPrompt: (text, searchResults, context) => `
Enhance the following introduction text using current research findings:

Current introduction: "${text}"

Recent research findings:
${searchResults}

Document context: ${context.slice(0, 500)}...

Please:
1. Strengthen the introduction with current research
2. Add relevant background information
3. Improve the flow and academic tone
4. Suggest areas where citations would be appropriate
5. Ensure the introduction sets up the research problem effectively

Provide an enhanced version that maintains the original intent while incorporating new insights.`
  },
  {
    id: 'citation-finding',
    name: 'Citation & References',
    description: 'Find relevant citations and references',
    icon: <Quote className="h-4 w-4" />,
    color: 'green',
    searchQuery: (text, context) => `"${text}" academic citations research papers`,
    aiPrompt: (text, searchResults, context) => `
Find and suggest relevant citations for the following text:

Text requiring citations: "${text}"

Available research sources:
${searchResults}

Document context: ${context.slice(0, 500)}...

Please provide:
1. Specific citation suggestions with author names and years
2. Key findings that support the text
3. Recommended in-text citation placements
4. Suggestions for additional sources to search for
5. APA format citation examples

Focus on credible, peer-reviewed sources and recent research (2018-2024).`
  },
  {
    id: 'content-research',
    name: 'Content Research',
    description: 'Research and fact-check content',
    icon: <Search className="h-4 w-4" />,
    color: 'purple',
    searchQuery: (text, context) => `${text} research facts academic verification`,
    aiPrompt: (text, searchResults, context) => `
Research and fact-check the following content:

Content to research: "${text}"

Research findings:
${searchResults}

Document context: ${context.slice(0, 500)}...

Please provide:
1. Verification of key claims and facts
2. Additional supporting evidence
3. Contradictory findings (if any)
4. Suggestions for strengthening the content
5. Recommended additional research directions

Ensure all information is accurate and well-supported by credible sources.`
  },
  {
    id: 'related-work',
    name: 'Related Work Discovery',
    description: 'Discover related work and literature',
    icon: <Users className="h-4 w-4" />,
    color: 'indigo',
    searchQuery: (text, context) => `"${text}" related work literature review academic`,
    aiPrompt: (text, searchResults, context) => `
Discover and analyze related work for the following topic:

Topic/text: "${text}"

Related research found:
${searchResults}

Document context: ${context.slice(0, 500)}...

Please provide:
1. Summary of key related work
2. Identification of research gaps
3. Comparison with current approaches
4. Suggestions for positioning this work
5. Recommended papers to cite
6. Potential collaborations or connections

Structure as a comprehensive literature review section.`
  }
];

export function SearchEnhancedAITools({
  selectedText,
  documentContent,
  onAIRequest,
  aiLoading
}: SearchEnhancedAIToolsProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [processingTool, setProcessingTool] = useState<string | null>(null);
  const [customSearchQuery, setCustomSearchQuery] = useState('');
  const [showCustomSearch, setShowCustomSearch] = useState(false);

  const handleSearchTool = async (tool: SearchTool) => {
    if (!selectedText && !documentContent) {
      toast.error('Please select text or ensure document has content');
      return;
    }

    setProcessingTool(tool.id);

    try {
      // Use selected text or extract relevant content from document
      const targetText = selectedText || documentContent.slice(0, 500);
      
      // Generate search query
      const searchQuery = tool.searchQuery(targetText, documentContent);
      
      toast.info(`Searching for: ${tool.name.toLowerCase()}...`);
      
      // Perform Tavily search
      const searchResults = await tavilySearchService.searchAcademic(searchQuery, {
        maxResults: 5,
        searchDepth: 'advanced',
        includeAnswer: true
      });

      // Format search results for AI prompt
      const formattedResults = searchResults.results
        .map(result => `Title: ${result.title}\nContent: ${result.content}\nURL: ${result.url}`)
        .join('\n\n');

      // Generate AI prompt with search results
      const aiPrompt = tool.aiPrompt(targetText, formattedResults, documentContent);

      // Execute AI request
      onAIRequest(aiPrompt, selectedText, 'display');

      toast.success(`${tool.name} completed successfully`);

    } catch (error: any) {
      console.error(`${tool.name} failed:`, error);
      toast.error(`Failed to ${tool.name.toLowerCase()}: ${error.message}`);
    } finally {
      setProcessingTool(null);
    }
  };

  const handleCustomSearch = async () => {
    if (!customSearchQuery.trim()) {
      toast.error('Please enter a search query');
      return;
    }

    setProcessingTool('custom-search');

    try {
      const targetText = selectedText || documentContent.slice(0, 500);
      
      toast.info('Performing custom search...');
      
      // Perform Tavily search with custom query
      const searchResults = await tavilySearchService.searchAcademic(customSearchQuery, {
        maxResults: 8,
        searchDepth: 'advanced',
        includeAnswer: true
      });

      // Format search results
      const formattedResults = searchResults.results
        .map(result => `Title: ${result.title}\nContent: ${result.content}\nURL: ${result.url}`)
        .join('\n\n');

      // Generate comprehensive AI prompt
      const aiPrompt = `
Based on the search query "${customSearchQuery}" and the following research findings, provide a comprehensive analysis:

${selectedText ? `Selected text: "${selectedText}"` : ''}

Research findings:
${formattedResults}

Document context: ${documentContent.slice(0, 500)}...

Please provide:
1. Summary of key findings
2. Relevance to the current work
3. Implications for the research
4. Suggested citations and references
5. Recommendations for further investigation

Structure as a well-organized academic response.`;

      // Execute AI request
      onAIRequest(aiPrompt, selectedText, 'display');

      toast.success('Custom search completed successfully');
      setCustomSearchQuery('');

    } catch (error: any) {
      console.error('Custom search failed:', error);
      toast.error(`Custom search failed: ${error.message}`);
    } finally {
      setProcessingTool(null);
    }
  };

  const getColorClasses = (color: string) => {
    const classes = {
      yellow: 'bg-yellow-100 hover:bg-yellow-200 text-yellow-700 border-yellow-200',
      blue: 'bg-blue-100 hover:bg-blue-200 text-blue-700 border-blue-200',
      green: 'bg-green-100 hover:bg-green-200 text-green-700 border-green-200',
      purple: 'bg-purple-100 hover:bg-purple-200 text-purple-700 border-purple-200',
      indigo: 'bg-indigo-100 hover:bg-indigo-200 text-indigo-700 border-indigo-200'
    };
    return classes[color as keyof typeof classes] || classes.blue;
  };

  return (
    <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
      <CollapsibleTrigger asChild>
        <Card className="p-4 bg-gradient-to-br from-emerald-50 via-teal-50 to-cyan-50 border-emerald-200 cursor-pointer hover:shadow-md transition-all duration-300 hover:border-emerald-300 group">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-lg shadow-sm group-hover:shadow-md transition-shadow">
                <Search className="h-5 w-5 text-white" />
              </div>
              <div>
                <span className="font-semibold text-emerald-900 text-lg">Search-Enhanced AI Tools</span>
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant="secondary" className="text-xs bg-emerald-100 text-emerald-700 border-emerald-200">
                    <Sparkles className="h-3 w-3 mr-1" />
                    Powered by Tavily
                  </Badge>
                  <Badge variant="outline" className="text-xs text-gray-600 border-gray-300">
                    {searchTools.length} tools
                  </Badge>
                </div>
              </div>
            </div>
            <div className="p-1 rounded-full bg-emerald-100 group-hover:bg-emerald-200 transition-colors">
              {isExpanded ? <ChevronUp className="h-4 w-4 text-emerald-700" /> : <ChevronDown className="h-4 w-4 text-emerald-700" />}
            </div>
          </div>
        </Card>
      </CollapsibleTrigger>

      <CollapsibleContent className="space-y-4 mt-4">
        {/* Search Tools Grid */}
        <div className="grid grid-cols-1 gap-3">
          {searchTools.map((tool) => (
            <Card
              key={tool.id}
              className={`p-4 cursor-pointer transition-all duration-300 hover:shadow-lg border-2 ${
                processingTool === tool.id
                  ? 'border-blue-300 bg-blue-50 shadow-md'
                  : `border-transparent hover:border-${tool.color}-200 ${getColorClasses(tool.color)}`
              }`}
              onClick={() => handleSearchTool(tool)}
            >
              <div className="flex items-start gap-4">
                <div className={`p-3 rounded-xl shadow-sm ${
                  processingTool === tool.id
                    ? 'bg-blue-100'
                    : `bg-${tool.color}-100`
                }`}>
                  {processingTool === tool.id ? (
                    <Loader2 className="h-5 w-5 animate-spin text-blue-600" />
                  ) : (
                    <div className={`text-${tool.color}-600`}>
                      {React.cloneElement(tool.icon as React.ReactElement, { className: "h-5 w-5" })}
                    </div>
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="font-semibold text-gray-900 mb-1">{tool.name}</div>
                  <div className="text-sm text-gray-600 leading-relaxed">{tool.description}</div>
                  {processingTool === tool.id && (
                    <div className="mt-2 text-xs text-blue-600 font-medium">
                      🔍 Searching and analyzing...
                    </div>
                  )}
                </div>
                <div className={`p-1 rounded-full ${
                  processingTool === tool.id
                    ? 'bg-blue-100'
                    : `bg-${tool.color}-100 opacity-0 group-hover:opacity-100`
                } transition-opacity`}>
                  <ChevronDown className={`h-4 w-4 text-${tool.color}-600 transform rotate-[-90deg]`} />
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Custom Search */}
        <Card className="border-2 border-dashed border-gray-300 hover:border-purple-300 transition-colors">
          <Collapsible open={showCustomSearch} onOpenChange={setShowCustomSearch}>
            <CollapsibleTrigger asChild>
              <div className="p-4 cursor-pointer">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg shadow-sm">
                      <Sparkles className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <span className="font-semibold text-gray-900">Custom Research Search</span>
                      <div className="text-sm text-gray-600 mt-1">Search for any topic with AI analysis</div>
                    </div>
                  </div>
                  <div className="p-1 rounded-full bg-purple-100 hover:bg-purple-200 transition-colors">
                    {showCustomSearch ? <ChevronUp className="h-4 w-4 text-purple-700" /> : <ChevronDown className="h-4 w-4 text-purple-700" />}
                  </div>
                </div>
              </div>
            </CollapsibleTrigger>

            <CollapsibleContent className="px-4 pb-4 space-y-4">
              <div className="bg-gradient-to-br from-purple-50 to-indigo-50 rounded-lg p-4 border border-purple-200">
                <Textarea
                  value={customSearchQuery}
                  onChange={(e) => setCustomSearchQuery(e.target.value)}
                  placeholder="Enter your custom research query... (e.g., 'machine learning applications in healthcare', 'climate change mitigation strategies')"
                  className="w-full text-sm min-h-[100px] resize-none border-purple-200 focus:border-purple-400 bg-white/70"
                  disabled={aiLoading || processingTool !== null}
                />
                <Button
                  size="default"
                  onClick={handleCustomSearch}
                  disabled={!customSearchQuery.trim() || aiLoading || processingTool !== null}
                  className="w-full mt-3 bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 shadow-md"
                >
                  {processingTool === 'custom-search' ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Searching & Analyzing...
                    </>
                  ) : (
                    <>
                      <Search className="h-4 w-4 mr-2" />
                      Search & Analyze
                    </>
                  )}
                </Button>
              </div>
            </CollapsibleContent>
          </Collapsible>
        </Card>

        {/* Usage Tips */}
        <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200">
          <div className="p-4">
            <div className="flex items-center gap-2 mb-3">
              <div className="p-1 bg-blue-100 rounded-full">
                <Lightbulb className="h-4 w-4 text-blue-600" />
              </div>
              <span className="font-semibold text-blue-900">Pro Tips</span>
            </div>
            <div className="grid grid-cols-1 gap-2 text-sm text-blue-800">
              <div className="flex items-start gap-2">
                <div className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                <span>Select text for targeted research assistance</span>
              </div>
              <div className="flex items-start gap-2">
                <div className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                <span>Tools automatically search academic sources</span>
              </div>
              <div className="flex items-start gap-2">
                <div className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                <span>Results include citations and references</span>
              </div>
              <div className="flex items-start gap-2">
                <div className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                <span>Use custom search for specific queries</span>
              </div>
            </div>
          </div>
        </Card>
      </CollapsibleContent>
    </Collapsible>
  );
}
