import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  BarChart3,
  Grid,
  <PERSON><PERSON><PERSON>,
  LineC<PERSON>,
  TrendingUp,
  Eye,
  Download,
  Filter,
  Search,
  Maximize2,
  Circle,
  Square
} from "lucide-react";
import { Plotly<PERSON>hart } from './PlotlyChart';
import { useDataVisualizationStore } from '../stores/data-visualization.store';
import { VisualizationConfig, DataAnalysisResult } from '../types';
import { VISUALIZATION_TYPES } from '../types';

interface VisualizationGalleryProps {
  analysisResult: DataAnalysisResult;
  className?: string;
}

export const VisualizationGallery: React.FC<VisualizationGalleryProps> = ({
  analysisResult,
  className = ""
}) => {
  const { selectedVisualization, setSelectedVisualization } = useDataVisualizationStore();
  const [filterType, setFilterType] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');

  const getVisualizationIcon = (type: string) => {
    switch (type) {
      case 'histogram':
        return <BarChart3 className="h-4 w-4" />;
      case 'scatter':
        return <Circle className="h-4 w-4" />;
      case 'correlation_heatmap':
        return <Grid className="h-4 w-4" />;
      case 'box_plot':
        return <Square className="h-4 w-4" />;
      case 'bar_chart':
        return <BarChart3 className="h-4 w-4" />;
      case 'line_chart':
        return <LineChart className="h-4 w-4" />;
      case 'pie_chart':
        return <PieChart className="h-4 w-4" />;
      default:
        return <TrendingUp className="h-4 w-4" />;
    }
  };

  const filteredVisualizations = (analysisResult?.visualizations || []).filter(viz => {
    const matchesType = filterType === 'all' || viz.type === filterType;
    const matchesSearch = searchTerm === '' ||
      viz.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      viz.description.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesType && matchesSearch;
  });

  const visualizationTypes = Array.from(
    new Set((analysisResult?.visualizations || []).map(viz => viz.type))
  );

  const downloadAllVisualizations = () => {
    // TODO: Implement bulk download functionality
    console.log('Downloading all visualizations...');
  };

  if (!analysisResult?.visualizations || analysisResult.visualizations.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center h-64">
          <div className="text-center space-y-3">
            <TrendingUp className="h-12 w-12 text-gray-400 mx-auto" />
            <div>
              <h3 className="font-medium text-gray-900">No Visualizations Available</h3>
              <p className="text-sm text-gray-500 mt-1">
                Run the analysis pipeline to generate visualizations
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                Data Visualizations
                {analysisResult.id.includes('fallback') && (
                  <Badge variant="outline" className="ml-2 text-xs">
                    Basic Analysis
                  </Badge>
                )}
                {analysisResult.id.includes('gemini') && (
                  <Badge variant="default" className="ml-2 text-xs bg-purple-500">
                    AI-Powered
                  </Badge>
                )}
              </CardTitle>
              <p className="text-sm text-gray-500 mt-1">
                {analysisResult?.visualizations?.length || 0} visualization(s) generated from your data
                {analysisResult.id.includes('fallback') && (
                  <span className="block text-xs text-orange-600 mt-1">
                    Using basic statistical analysis (AI analysis unavailable)
                  </span>
                )}
              </p>
            </div>
            
            <div className="flex items-center gap-2">
              <Badge variant="outline">
                {filteredVisualizations.length} of {analysisResult?.visualizations?.length || 0}
              </Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={downloadAllVisualizations}
              >
                <Download className="h-4 w-4 mr-2" />
                Download All
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          {/* Filters */}
          <div className="flex items-center gap-4 mb-4">
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-gray-500" />
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="text-sm border rounded px-2 py-1"
              >
                <option value="all">All Types</option>
                {visualizationTypes.map(type => (
                  <option key={type} value={type}>
                    {VISUALIZATION_TYPES[type as keyof typeof VISUALIZATION_TYPES]?.name || type}
                  </option>
                ))}
              </select>
            </div>
            
            <div className="flex items-center gap-2 flex-1 max-w-sm">
              <Search className="h-4 w-4 text-gray-500" />
              <input
                type="text"
                placeholder="Search visualizations..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="text-sm border rounded px-2 py-1 flex-1"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Visualizations */}
      <Tabs defaultValue="grid" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="grid">Grid View</TabsTrigger>
          <TabsTrigger value="detailed">Detailed View</TabsTrigger>
        </TabsList>
        
        <TabsContent value="grid" className="space-y-6">
          {filteredVisualizations.length === 0 ? (
            <Alert>
              <Search className="h-4 w-4" />
              <AlertDescription>
                No visualizations match your current filters. Try adjusting your search criteria.
              </AlertDescription>
            </Alert>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {filteredVisualizations.map((visualization) => (
                <PlotlyChart
                  key={visualization.id}
                  visualization={visualization}
                  className="cursor-pointer hover:shadow-lg transition-shadow"
                  onError={(error) => {
                    console.error('Visualization error:', error);
                    // You could add a toast notification here if needed
                  }}
                />
              ))}
            </div>
          )}
        </TabsContent>
        
        <TabsContent value="detailed" className="space-y-6">
          {filteredVisualizations.length === 0 ? (
            <Alert>
              <Search className="h-4 w-4" />
              <AlertDescription>
                No visualizations match your current filters. Try adjusting your search criteria.
              </AlertDescription>
            </Alert>
          ) : (
            <div className="space-y-8">
              {filteredVisualizations.map((visualization) => (
                <Card key={visualization.id} className="overflow-hidden">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {getVisualizationIcon(visualization.type)}
                        <div>
                          <CardTitle className="text-lg">{visualization.title}</CardTitle>
                          <p className="text-sm text-gray-500 mt-1">
                            {visualization.description}
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary">
                          {VISUALIZATION_TYPES[visualization.type as keyof typeof VISUALIZATION_TYPES]?.name || visualization.type}
                        </Badge>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setSelectedVisualization(
                            selectedVisualization === visualization.id ? null : visualization.id
                          )}
                        >
                          <Maximize2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  
                  <CardContent className="p-0">
                    <PlotlyChart
                      visualization={visualization}
                      onError={(error) => console.error('Visualization error:', error)}
                    />
                  </CardContent>
                  
                  {/* Visualization Details */}
                  <CardContent className="pt-4 border-t">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-gray-700">Type:</span>
                        <p className="text-gray-600 mt-1">
                          {VISUALIZATION_TYPES[visualization.type as keyof typeof VISUALIZATION_TYPES]?.description || 'Custom visualization'}
                        </p>
                      </div>
                      
                      <div>
                        <span className="font-medium text-gray-700">Data Points:</span>
                        <p className="text-gray-600 mt-1">
                          {visualization.data?.[0]?.x?.length || visualization.data?.[0]?.z?.length || 'N/A'} points
                        </p>
                      </div>
                      
                      <div>
                        <span className="font-medium text-gray-700">Interactive:</span>
                        <p className="text-gray-600 mt-1">
                          Zoom, pan, hover enabled
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};
