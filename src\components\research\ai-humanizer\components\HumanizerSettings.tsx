/**
 * Humanizer Settings Component
 * Configuration settings for the AI humanizer
 */

import React from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import {
  Settings,
  Brain,
  Palette,
  Shield,
  RotateCcw,
  Save,
  Info
} from "lucide-react";
import { toast } from 'sonner';

import { useHumanizerStore } from '../stores/humanizer.store';
import { HUMANIZER_AI_MODELS, UI_CONFIG, DEFAULT_HUMANIZER_SETTINGS } from '../constants';
import { AIModelSelector } from './AIModelSelector';

export function HumanizerSettings() {
  const { settings, updateSettings, resetSettings } = useHumanizerStore();

  /**
   * Handle setting change
   */
  const handleSettingChange = (key: string, value: any) => {
    updateSettings({ [key]: value });
  };

  /**
   * Handle reset settings
   */
  const handleResetSettings = () => {
    resetSettings();
    toast.success('Settings reset to defaults');
  };

  /**
   * Handle save settings (for future persistence)
   */
  const handleSaveSettings = () => {
    // Settings are automatically persisted via Zustand persist middleware
    toast.success('Settings saved successfully');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold text-gray-800 mb-2">Settings</h2>
        <p className="text-gray-600">
          Configure how the AI humanizer processes your content and applies your writing style.
        </p>
      </div>

      {/* AI Model Settings */}
      <AIModelSelector
        selectedModel={settings.preferredModel}
        onModelChange={(modelId) => handleSettingChange('preferredModel', modelId)}
      />

      {/* Processing Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Palette className="h-5 w-5 text-purple-600" />
            Processing Options
          </CardTitle>
          <CardDescription>
            Control how your content is processed and rewritten
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Creativity Level */}
          <div>
            <Label htmlFor="creativity-select">Creativity Level</Label>
            <Select
              value={settings.creativityLevel}
              onValueChange={(value) => handleSettingChange('creativityLevel', value as any)}
            >
              <SelectTrigger id="creativity-select">
                <SelectValue placeholder="Select creativity level" />
              </SelectTrigger>
              <SelectContent>
                {UI_CONFIG.CREATIVITY_LEVELS.map((level) => {
                  const Icon = level.icon;
                  return (
                    <SelectItem key={level.id} value={level.id}>
                      <div className="flex items-center gap-2">
                        <Icon className="h-4 w-4" />
                        <div className="flex flex-col">
                          <span className="font-medium">{level.name}</span>
                          <span className="text-xs text-gray-500">{level.description}</span>
                        </div>
                      </div>
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>

          {/* Output Style */}
          <div>
            <Label htmlFor="output-style-select">Output Style</Label>
            <Select
              value={settings.outputStyle}
              onValueChange={(value) => handleSettingChange('outputStyle', value as any)}
            >
              <SelectTrigger id="output-style-select">
                <SelectValue placeholder="Select output style" />
              </SelectTrigger>
              <SelectContent>
                {UI_CONFIG.OUTPUT_STYLES.map((style) => {
                  const Icon = style.icon;
                  return (
                    <SelectItem key={style.id} value={style.id}>
                      <div className="flex items-center gap-2">
                        <Icon className="h-4 w-4" />
                        <div className="flex flex-col">
                          <span className="font-medium">{style.name}</span>
                          <span className="text-xs text-gray-500">{style.description}</span>
                        </div>
                      </div>
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>

          <Separator />

          {/* Content Preservation Options */}
          <div className="space-y-4">
            <h4 className="font-medium text-gray-800">Content Preservation</h4>
            
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="preserve-formatting">Preserve Formatting</Label>
                <p className="text-sm text-gray-500">
                  Maintain original paragraph breaks and structure
                </p>
              </div>
              <Switch
                id="preserve-formatting"
                checked={settings.preserveFormatting}
                onCheckedChange={(checked) => handleSettingChange('preserveFormatting', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="maintain-technical">Maintain Technical Terms</Label>
                <p className="text-sm text-gray-500">
                  Keep technical vocabulary and specialized terms unchanged
                </p>
              </div>
              <Switch
                id="maintain-technical"
                checked={settings.maintainTechnicalTerms}
                onCheckedChange={(checked) => handleSettingChange('maintainTechnicalTerms', checked)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* User Experience Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Settings className="h-5 w-5 text-green-600" />
            User Experience
          </CardTitle>
          <CardDescription>
            Customize your workflow and interface preferences
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="auto-save">Auto-save Sessions</Label>
              <p className="text-sm text-gray-500">
                Automatically save your work after processing
              </p>
            </div>
            <Switch
              id="auto-save"
              checked={settings.autoSave}
              onCheckedChange={(checked) => handleSettingChange('autoSave', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="show-analysis">Show Analysis Details</Label>
              <p className="text-sm text-gray-500">
                Display detailed style analysis information
              </p>
            </div>
            <Switch
              id="show-analysis"
              checked={settings.showAnalysisDetails}
              onCheckedChange={(checked) => handleSettingChange('showAnalysisDetails', checked)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Information Card */}
      <Card className="bg-blue-50 border-blue-200">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Info className="h-5 w-5 text-blue-600" />
            Tips for Best Results
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="space-y-2 text-sm text-blue-800">
            <li className="flex items-start gap-2">
              <span className="text-blue-600 mt-1">•</span>
              <span>Use "Conservative" creativity for formal documents and professional content</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="text-blue-600 mt-1">•</span>
              <span>Choose "Moderate" for most general content and balanced results</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="text-blue-600 mt-1">•</span>
              <span>Select "Creative" for marketing copy and engaging content</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="text-blue-600 mt-1">•</span>
              <span>Enable "Maintain Technical Terms" for academic or technical writing</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="text-blue-600 mt-1">•</span>
              <span>Gemini 2.5 Pro provides the most accurate style analysis</span>
            </li>
          </ul>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex gap-3 pt-4">
        <Button onClick={handleSaveSettings} className="bg-blue-600 hover:bg-blue-700">
          <Save className="h-4 w-4 mr-2" />
          Save Settings
        </Button>
        <Button variant="outline" onClick={handleResetSettings}>
          <RotateCcw className="h-4 w-4 mr-2" />
          Reset to Defaults
        </Button>
      </div>

      {/* Current Settings Summary */}
      <Card className="bg-gray-50">
        <CardHeader>
          <CardTitle className="text-base">Current Configuration</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-600">Model:</span>
              <span className="ml-2">
                {HUMANIZER_AI_MODELS.find(m => m.id === settings.preferredModel)?.name || 'Unknown'}
              </span>
            </div>
            <div>
              <span className="font-medium text-gray-600">Creativity:</span>
              <span className="ml-2 capitalize">{settings.creativityLevel}</span>
            </div>
            <div>
              <span className="font-medium text-gray-600">Output Style:</span>
              <span className="ml-2 capitalize">{settings.outputStyle}</span>
            </div>
            <div>
              <span className="font-medium text-gray-600">Auto-save:</span>
              <span className="ml-2">{settings.autoSave ? 'Enabled' : 'Disabled'}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
