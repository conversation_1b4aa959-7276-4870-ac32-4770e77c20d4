import React, { useState, useCallback, useRef } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { toast } from "sonner";
import {
  Upload,
  FileText,
  File,
  X,
  CheckCircle,
  AlertCircle,
  Loader2,
  Download,
  Eye
} from "lucide-react";

import { UPLOAD_CONFIG } from '../constants';
import { FileUploadProgress } from '../types';

interface FileUploaderProps {
  onUpload: (content: string, metadata?: any) => void;
}

interface UploadedFile {
  id: string;
  file: File;
  progress: number;
  status: 'uploading' | 'processing' | 'completed' | 'failed';
  content?: string;
  error?: string;
  preview?: string;
}

export function FileUploader({ onUpload }: FileUploaderProps) {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return <FileText className="h-8 w-8 text-red-500" />;
      case 'doc':
      case 'docx':
        return <FileText className="h-8 w-8 text-blue-500" />;
      case 'txt':
      case 'md':
        return <File className="h-8 w-8 text-gray-500" />;
      default:
        return <File className="h-8 w-8 text-gray-400" />;
    }
  };

  const validateFile = (file: File): { valid: boolean; error?: string } => {
    // Check file size
    if (file.size > UPLOAD_CONFIG.maxFileSize) {
      return {
        valid: false,
        error: `File size (${(file.size / 1024 / 1024).toFixed(2)}MB) exceeds maximum allowed size (${UPLOAD_CONFIG.maxFileSize / 1024 / 1024}MB)`
      };
    }

    // Check file format
    const extension = '.' + file.name.split('.').pop()?.toLowerCase();
    const fileType = file.type.toLowerCase();
    
    const isValidFormat = UPLOAD_CONFIG.acceptedFormats.includes(extension) || 
                         UPLOAD_CONFIG.acceptedMimeTypes.includes(fileType);
    
    if (!isValidFormat) {
      return {
        valid: false,
        error: `File type "${extension}" is not supported. Accepted formats: ${UPLOAD_CONFIG.acceptedFormats.join(', ')}`
      };
    }

    return { valid: true };
  };

  const processFile = async (file: File): Promise<string> => {
    // Simulate file processing
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Mock content extraction based on file type
    const extension = file.name.split('.').pop()?.toLowerCase();
    
    switch (extension) {
      case 'txt':
      case 'md':
        return await file.text();
      case 'pdf':
        return `[PDF Content] This is extracted content from ${file.name}. In a real implementation, this would use a PDF parsing library to extract the actual text content.`;
      case 'doc':
      case 'docx':
        return `[Word Document Content] This is extracted content from ${file.name}. In a real implementation, this would use a document parsing library to extract the actual text content.`;
      default:
        return `Content extracted from ${file.name}`;
    }
  };

  const handleFileUpload = async (files: File[]) => {
    for (const file of files) {
      const validation = validateFile(file);
      if (!validation.valid) {
        toast.error(validation.error);
        continue;
      }

      const fileId = Math.random().toString(36).substr(2, 9);
      const uploadedFile: UploadedFile = {
        id: fileId,
        file,
        progress: 0,
        status: 'uploading'
      };

      setUploadedFiles(prev => [...prev, uploadedFile]);

      try {
        // Simulate upload progress
        for (let progress = 0; progress <= 100; progress += 10) {
          await new Promise(resolve => setTimeout(resolve, 100));
          setUploadedFiles(prev => prev.map(f => 
            f.id === fileId ? { ...f, progress } : f
          ));
        }

        // Update status to processing
        setUploadedFiles(prev => prev.map(f => 
          f.id === fileId ? { ...f, status: 'processing', progress: 0 } : f
        ));

        // Process file content
        const content = await processFile(file);

        // Update to completed
        setUploadedFiles(prev => prev.map(f => 
          f.id === fileId ? { 
            ...f, 
            status: 'completed', 
            progress: 100, 
            content,
            preview: content.substring(0, 200) + (content.length > 200 ? '...' : '')
          } : f
        ));

        // Call onUpload with the content
        onUpload(content, {
          fileName: file.name,
          fileSize: file.size,
          fileType: file.type
        });

        toast.success(`${file.name} uploaded successfully`);

      } catch (error) {
        setUploadedFiles(prev => prev.map(f => 
          f.id === fileId ? { 
            ...f, 
            status: 'failed', 
            error: 'Failed to process file'
          } : f
        ));
        toast.error(`Failed to upload ${file.name}`);
      }
    }
  };

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    
    const files = Array.from(e.dataTransfer.files);
    handleFileUpload(files);
  }, []);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    handleFileUpload(files);
  };

  const removeFile = (fileId: string) => {
    setUploadedFiles(prev => prev.filter(f => f.id !== fileId));
  };

  const getStatusIcon = (status: UploadedFile['status']) => {
    switch (status) {
      case 'uploading':
      case 'processing':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
    }
  };

  const getStatusText = (file: UploadedFile) => {
    switch (file.status) {
      case 'uploading':
        return `Uploading... ${file.progress}%`;
      case 'processing':
        return 'Processing content...';
      case 'completed':
        return 'Ready for podcast generation';
      case 'failed':
        return file.error || 'Upload failed';
    }
  };

  return (
    <div className="space-y-6">
      {/* Upload Area */}
      <Card 
        className={`border-2 border-dashed transition-colors cursor-pointer ${
          isDragging 
            ? 'border-purple-400 bg-purple-50' 
            : 'border-gray-300 hover:border-purple-300 hover:bg-purple-50'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => fileInputRef.current?.click()}
      >
        <CardContent className="p-8 text-center">
          <Upload className={`h-12 w-12 mx-auto mb-4 ${isDragging ? 'text-purple-500' : 'text-gray-400'}`} />
          <h3 className="text-lg font-semibold mb-2">
            {isDragging ? 'Drop files here' : 'Upload Documents'}
          </h3>
          <p className="text-gray-600 mb-4">
            Drag and drop files here, or click to browse
          </p>
          <div className="flex flex-wrap justify-center gap-2 mb-4">
            {UPLOAD_CONFIG.acceptedFormats.map((format) => (
              <Badge key={format} variant="secondary" className="text-xs">
                {format.toUpperCase()}
              </Badge>
            ))}
          </div>
          <p className="text-sm text-gray-500">
            Maximum file size: {UPLOAD_CONFIG.maxFileSize / 1024 / 1024}MB
          </p>
        </CardContent>
      </Card>

      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept={UPLOAD_CONFIG.acceptedFormats.join(',')}
        onChange={handleFileSelect}
        className="hidden"
      />

      {/* Uploaded Files */}
      {uploadedFiles.length > 0 && (
        <div className="space-y-3">
          <h3 className="text-lg font-semibold">Uploaded Files</h3>
          {uploadedFiles.map((file) => (
            <Card key={file.id} className="border border-gray-200">
              <CardContent className="p-4">
                <div className="flex items-start gap-3">
                  {getFileIcon(file.file.name)}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium truncate">{file.file.name}</h4>
                      <div className="flex items-center gap-2">
                        {getStatusIcon(file.status)}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeFile(file.id)}
                          className="h-6 w-6 p-0"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm text-gray-600">
                        <span>{(file.file.size / 1024 / 1024).toFixed(2)} MB</span>
                        <span>{getStatusText(file)}</span>
                      </div>
                      
                      {(file.status === 'uploading' || file.status === 'processing') && (
                        <Progress value={file.progress} className="h-2" />
                      )}
                      
                      {file.preview && file.status === 'completed' && (
                        <div className="mt-2 p-3 bg-gray-50 rounded text-sm text-gray-700">
                          <p className="font-medium mb-1">Content Preview:</p>
                          <p>{file.preview}</p>
                        </div>
                      )}
                      
                      {file.error && file.status === 'failed' && (
                        <Alert className="mt-2">
                          <AlertCircle className="h-4 w-4" />
                          <AlertDescription>{file.error}</AlertDescription>
                        </Alert>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Tips */}
      <Card className="border border-blue-200 bg-blue-50">
        <CardContent className="p-4">
          <h4 className="font-medium text-blue-900 mb-2">📄 File Upload Tips</h4>
          <div className="space-y-1 text-sm text-blue-800">
            <p>• PDF files work best for research papers and articles</p>
            <p>• Word documents are great for structured content</p>
            <p>• Text files should be well-formatted for best results</p>
            <p>• Multiple files will be combined into a single podcast</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
